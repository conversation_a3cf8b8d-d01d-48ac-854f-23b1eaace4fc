{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/9583c_@sentry_core_build_esm_1adfbc63._.js", "server/edge/chunks/4b803_@sentry_vercel-edge_build_esm_index_de3a4916.js", "server/edge/chunks/ec4b9_zod_dist_esm_d6b871ea._.js", "server/edge/chunks/node_modules__pnpm_096b6372._.js", "server/edge/chunks/[root-of-the-server]__a0a0371e._.js", "server/edge/chunks/apps_web_edge-wrapper_977354a8.js", "server/edge/chunks/_65232333._.js", "server/edge/chunks/ec4b9_zod_dist_esm_cbcb71bd._.js", "server/edge/chunks/eec21_@clerk_shared_dist_40b2e982._.js", "server/edge/chunks/c67f4_@clerk_backend_dist_d8cc056d._.js", "server/edge/chunks/25c57_@clerk_nextjs_dist_esm_1ca17405._.js", "server/edge/chunks/node_modules__pnpm_2d5523b0._.js", "server/edge/chunks/[root-of-the-server]__b5fdeec6._.js", "server/edge/chunks/apps_web_edge-wrapper_5738550d.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|images|ingest|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|images|ingest|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "JFmE98NiJ/xqhp3pvYld3ls8dYeXUKqhINpTXPj7HIQ=", "__NEXT_PREVIEW_MODE_ID": "9ae484832a5ccc13e55662755b5cf352", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "70c6f7d20d94981b2f4181407c76f7cbd337a6a8b8b6c7a13196c963ce0121b5", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "f4e647272a33dd8d2a38ae9f2c5968f890f98a36e4b8d8c57c2e6fa37051b139"}}}, "sortedMiddleware": ["/"], "functions": {"/[locale]/icon/route": {"files": ["server/edge/chunks/9583c_@sentry_core_build_esm_1adfbc63._.js", "server/edge/chunks/4b803_@sentry_vercel-edge_build_esm_index_de3a4916.js", "server/edge/chunks/ec4b9_zod_dist_esm_d6b871ea._.js", "server/edge/chunks/node_modules__pnpm_096b6372._.js", "server/edge/chunks/[root-of-the-server]__a0a0371e._.js", "server/edge/chunks/apps_web_edge-wrapper_977354a8.js", "server/server-reference-manifest.js", "server/middleware-build-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/app/[locale]/icon/route_client-reference-manifest.js", "server/edge/chunks/apps_web__next-internal_server_app_[locale]_icon_route_actions_76d43f43.js", "server/edge/chunks/apps_web__next-internal_server_app_[locale]_icon_route_actions_49420e93.js", "server/edge/chunks/661a5_next_dist_esm_6bd25579._.js", "server/edge/chunks/661a5_next_dist_compiled_react-server-dom-turbopack_34e1695d._.js", "server/edge/chunks/661a5_next_dist_compiled_@vercel_og_225bb2b9._.js", "server/edge/chunks/661a5_next_dist_compiled_f39918aa._.js", "server/edge/chunks/661a5_next_dist_025236e0._.js", "server/edge/chunks/apps_web_edge-wrapper_c77e8a4f.js", "server/edge/chunks/[root-of-the-server]__84d3291d._.js", "server/edge/chunks/apps_web_edge-wrapper_f99930a3.js", "server/app/[locale]/icon/route/react-loadable-manifest.js"], "name": "/[locale]/icon", "page": "/[locale]/icon/route", "matchers": [{"regexp": "^/(?P<nxtPlocale>[^/]+?)/icon(?:/)?$", "originalSource": "/[locale]/icon"}], "wasm": [{"name": "wasm_661a5_next_dist_compiled__vercel_og_resvg_325b988e", "filePath": "server/edge/chunks/661a5_next_dist_compiled_@vercel_og_resvg_325b988e.wasm"}, {"name": "wasm_661a5_next_dist_compiled__vercel_og_yoga_325b988e", "filePath": "server/edge/chunks/661a5_next_dist_compiled_@vercel_og_yoga_325b988e.wasm"}], "assets": [{"name": "server/edge/assets/noto-sans-v27-latin-regular.7e1666d1.ttf", "filePath": "server/edge/assets/noto-sans-v27-latin-regular.7e1666d1.ttf"}], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "JFmE98NiJ/xqhp3pvYld3ls8dYeXUKqhINpTXPj7HIQ=", "__NEXT_PREVIEW_MODE_ID": "9ae484832a5ccc13e55662755b5cf352", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "70c6f7d20d94981b2f4181407c76f7cbd337a6a8b8b6c7a13196c963ce0121b5", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "f4e647272a33dd8d2a38ae9f2c5968f890f98a36e4b8d8c57c2e6fa37051b139"}}}}