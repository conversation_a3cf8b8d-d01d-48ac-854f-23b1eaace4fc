{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/pricing/layout.tsx"], "sourcesContent": ["import type { ReactNode } from 'react';\n\ntype PricingLayoutProps = {\n  readonly children: ReactNode;\n};\n\nconst PricingLayout = ({ children }: PricingLayoutProps) => {\n  return (\n    <>\n      <style jsx global>{`\n        body {\n          background-color: #161616 !important;\n        }\n      `}</style>\n      {children}\n    </>\n  );\n};\n\nexport default PricingLayout;\n"], "names": [], "mappings": ";;;;;;AAMA,MAAM,gBAAgB,CAAC,EAAE,QAAQ,EAAsB;IACrD,qBACE;;;;;;YAMG;;;AAGP;uCAEe", "debugId": null}}]}