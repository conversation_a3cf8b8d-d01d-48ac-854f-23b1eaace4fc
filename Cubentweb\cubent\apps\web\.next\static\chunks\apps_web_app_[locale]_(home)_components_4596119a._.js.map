{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/%28home%29/components/community.tsx"], "sourcesContent": ["'use client';\n\nimport {\n  Avatar,\n  AvatarFallback,\n  AvatarImage,\n} from '@repo/design-system/components/ui/avatar';\nimport { Button } from '@repo/design-system/components/ui/button';\nimport type { Dictionary } from '@repo/internationalization';\nimport { ExternalLink, Github, MessageCircle } from 'lucide-react';\nimport { useEffect, useState, useMemo } from 'react';\n\ntype CommunityProps = {\n  dictionary: Dictionary;\n};\n\n// Sample community reviews data\nconst communityReviews = [\n  {\n    id: 1,\n    username: 'dev_goblin',\n    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',\n    content: 'What @cubentbase + @nextjs is amazing! 🔥 Really excited into a proof-of-concept and already have a lot of the functionality in place 🤯 🤯 🤯',\n    platform: 'twitter'\n  },\n  {\n    id: 2,\n    username: 'pixiePixels',\n    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',\n    content: 'Using @cubentbase I\\'m really impressed with the developers and see it in general. Despite being a bit dubious about the fact that I have to say I really don\\'t miss anything. The whole experience feels very robust and secure.',\n    platform: 'twitter'\n  },\n  {\n    id: 3,\n    username: 'realJoelR',\n    avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face',\n    content: 'This weekend I made a personal record 🏆 on the last time spent creating an application with social login / permissions, database, cdn, and for free. Thanks to @cubentbase and @vercel.',\n    platform: 'twitter'\n  },\n  {\n    id: 4,\n    username: 'steveOnScripts',\n    avatar: 'https://images.unsplash.com/photo-1519244703995-f4e0f30006d5?w=150&h=150&fit=crop&crop=face',\n    content: 'Working on my own cubent project. I want this to be my first because I\\'m not a backend engineer and I\\'m getting it done.',\n    platform: 'twitter'\n  },\n  {\n    id: 5,\n    username: 'braydenCodes',\n    avatar: 'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=150&h=150&fit=crop&crop=face',\n    content: 'New to @cubentbase I was really impressed with the developers and see it in general. Despite being a bit dubious about the fact that I have to say I really don\\'t miss anything.',\n    platform: 'twitter'\n  },\n  {\n    id: 6,\n    username: 'maxbyte42',\n    avatar: 'https://images.unsplash.com/photo-1507591064344-4c6ce005b128?w=150&h=150&fit=crop&crop=face',\n    content: 'Completed @cubentbase is it. It had the best experience I\\'ve had with a database. Despite being a bit dubious about the fact that I have to say I really don\\'t miss anything.',\n    platform: 'twitter'\n  },\n  {\n    id: 7,\n    username: 'cassWritesCode',\n    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',\n    content: 'Biggest @cubentbase is amazing! Really impressed with the developers and see it in general.',\n    platform: 'twitter'\n  },\n  {\n    id: 8,\n    username: 'alex0xFF',\n    avatar: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=150&h=150&fit=crop&crop=face',\n    content: 'Just shipped my first app using @cubentbase and the developer experience is incredible! The AI features saved me hours of debugging.',\n    platform: 'twitter'\n  },\n  {\n    id: 9,\n    username: 'codeKnight_',\n    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',\n    content: 'The context intelligence in @cubentbase is a game changer. It understands my codebase better than I do sometimes! 😅',\n    platform: 'twitter'\n  },\n  {\n    id: 10,\n    username: 'mike_2stack',\n    avatar: 'https://images.unsplash.com/photo-1560250097-0b93528c311a?w=150&h=150&fit=crop&crop=face',\n    content: 'Been using @cubentbase for 3 months now. The AI screenshot analysis feature is mind-blowing - it catches UI issues I completely miss.',\n    platform: 'twitter'\n  },\n  {\n    id: 11,\n    username: 'jennaDotJS',\n    avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face',\n    content: 'Finally found a coding assistant that actually understands context! @cubentbase has become essential to my workflow.',\n    platform: 'twitter'\n  },\n  {\n    id: 12,\n    username: 'tom_is_typing',\n    avatar: 'https://images.unsplash.com/photo-1566492031773-4f4e44671d66?w=150&h=150&fit=crop&crop=face',\n    content: 'The multi-language support in @cubentbase is fantastic. Seamlessly switching between JS, Python, and Rust in the same project.',\n    platform: 'twitter'\n  },\n  {\n    id: 13,\n    username: 'anna_rmd',\n    avatar: 'https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?w=150&h=150&fit=crop&crop=face',\n    content: 'The autonomous agent mode in @cubentbase is absolutely mind-blowing. I gave it a complex task and it just... did it. No back and forth, just results.',\n    platform: 'twitter'\n  },\n  {\n    id: 14,\n    username: 'liam_0x01',\n    avatar: 'https://images.unsplash.com/photo-1527980965255-d3b416303d12?w=150&h=150&fit=crop&crop=face',\n    content: 'Switched from Copilot to @cubentbase and the difference is night and day. The context awareness is incredible - it actually understands my project structure.',\n    platform: 'twitter'\n  },\n  {\n    id: 15,\n    username: 'priyaCodesLate',\n    avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face',\n    content: 'The natural language coding in @cubentbase is revolutionary. I can literally describe what I want and it builds it perfectly. This is the future of development.',\n    platform: 'twitter'\n  },\n  {\n    id: 16,\n    username: 'diego_dev',\n    avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face',\n    content: 'Documentation generation with @cubentbase saved our team weeks of work. It understands the code context and writes better docs than we ever could manually.',\n    platform: 'twitter'\n  },\n  {\n    id: 17,\n    username: 'jamieStartups',\n    avatar: 'https://images.unsplash.com/photo-1519244703995-f4e0f30006d5?w=150&h=150&fit=crop&crop=face',\n    content: 'The file operations in @cubentbase are so smart. It reads my entire workspace, understands the architecture, and makes perfect modifications across multiple files.',\n    platform: 'twitter'\n  },\n  {\n    id: 18,\n    username: 'mariaAIthings',\n    avatar: 'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=150&h=150&fit=crop&crop=face',\n    content: 'Terminal integration in @cubentbase is a game changer. It can run commands, analyze output, and fix issues automatically. It\\'s like having a DevOps expert on call.',\n    platform: 'twitter'\n  },\n  {\n    id: 19,\n    username: 'soloStone',\n    avatar: 'https://images.unsplash.com/photo-1560250097-0b93528c311a?w=150&h=150&fit=crop&crop=face',\n    content: 'As a solo founder, @cubentbase is my secret weapon. It handles complex coding tasks while I focus on business strategy. Productivity through the roof! 🚀',\n    platform: 'twitter'\n  },\n  {\n    id: 20,\n    username: 'natSecNerd',\n    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',\n    content: 'The custom modes in @cubentbase are brilliant. I created a security auditor mode and it found vulnerabilities I completely missed. This tool is incredible.',\n    platform: 'twitter'\n  },\n  {\n    id: 21,\n    username: 'owenWebby',\n    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',\n    content: 'Browser automation with @cubentbase is pure magic. It can interact with web pages, test functionality, and even debug UI issues. Mind = blown 🤯',\n    platform: 'twitter'\n  },\n  {\n    id: 22,\n    username: 'aish_on_mobile',\n    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',\n    content: 'The code generation quality in @cubentbase is unmatched. It writes cleaner code than most junior developers and follows best practices automatically.',\n    platform: 'twitter'\n  },\n  {\n    id: 23,\n    username: 'dataDan_',\n    avatar: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=150&h=150&fit=crop&crop=face',\n    content: 'Using @cubentbase for data analysis scripts has been incredible. It understands pandas, numpy, and matplotlib better than I do sometimes! 📊',\n    platform: 'twitter'\n  },\n  {\n    id: 24,\n    username: 'kubernick',\n    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',\n    content: 'The Docker and Kubernetes integration in @cubentbase is seamless. It can write deployment configs, debug container issues, and optimize resource usage.',\n    platform: 'twitter'\n  },\n  {\n    id: 25,\n    username: 'rileyRunsScripts',\n    avatar: 'https://images.unsplash.com/photo-1552058544-f2b08422138a?w=150&h=150&fit=crop&crop=face',\n    content: 'Feels like a co-pilot on steroids! @cubentbase rewrote a legacy module, added tests and opened the PR while I grabbed coffee.',\n    platform: 'twitter'\n  },\n  {\n    id: 26,\n    username: 'sophDesigns',\n    avatar: 'https://images.unsplash.com/photo-1552374196-c4e7ffc6e126?w=150&h=150&fit=crop&crop=face',\n    content: 'Docs hero: auto-generated docs are spot-on and beautifully formatted. Onboarding new engineers is now twice as fast 🚀',\n    platform: 'twitter'\n  },\n  {\n    id: 27,\n    username: 'omarBugfix',\n    avatar: 'https://images.unsplash.com/photo-1529626455594-4ff0802cfb7e?w=150&h=150&fit=crop&crop=face',\n    content: 'Bug-squashing machine! Pointed @cubentbase at failing tests and it narrowed down the root cause and pushed a fix.',\n    platform: 'twitter'\n  },\n  {\n    id: 28,\n    username: 'frontendChloe',\n    avatar: 'https://images.unsplash.com/photo-1529626455594-4ff0802cfb7e?w=150&h=150&fit=crop&crop=face',\n    content: 'The live browser preview straight from chat saves me a ton of context-switching. My UI iterations are lightning fast ⚡️',\n    platform: 'twitter'\n  },\n  {\n    id: 29,\n    username: 'rustyMara',\n    avatar: 'https://images.unsplash.com/photo-1502720705749-3c045fa5b72c?w=150&h=150&fit=crop&crop=face',\n    content: 'Loving @cubent.dev for Rust refactors — it split my crate into modules in seconds! 🦀',\n    platform: 'twitter'\n  },\n  {\n    id: 30,\n    username: 'swiftLuke',\n    avatar: 'https://images.unsplash.com/photo-1517841905240-472988babdf9?w=150&h=150&fit=crop&crop=face',\n    content: '@cubent.dev fixed three memory leaks in my iOS app and explained every change. Mind = blown.',\n    platform: 'twitter'\n  },\n  {\n    id: 31,\n    username: 'jsonSteele',\n    avatar: 'https://images.unsplash.com/photo-1502767089025-6572583495b9?w=150&h=150&fit=crop&crop=face',\n    content: 'Live context panel in @cubent.dev means I never lose track of files. Productivity +100% 💪',\n    platform: 'twitter'\n  },\n  {\n    id: 32,\n    username: 'emmaUXcreates',\n    avatar: 'https://images.unsplash.com/photo-1580489944761-15a19d654956?w=150&h=150&fit=crop&crop=face',\n    content: 'The ability to generate UI components from a simple prompt is a game-changer for my workflow. @cubent.dev is amazing!',\n    platform: 'twitter'\n  },\n  {\n    id: 33,\n    username: 'carterBoots',\n    avatar: 'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=150&h=150&fit=crop&crop=face',\n    content: 'I use @cubent.dev to bootstrap all my new projects. It sets up the boilerplate, CI/CD pipeline, and even initial docs.',\n    platform: 'twitter'\n  },\n  {\n    id: 34,\n    username: 'testZoe',\n    avatar: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?w=150&h=150&fit=crop&crop=face',\n    content: 'Generating complex test cases used to be a chore. Now, @cubent.dev writes them for me, covering all edge cases.',\n    platform: 'twitter'\n  }\n];\n\nexport const Community = ({ dictionary }: CommunityProps) => {\n  const [firstColumn, secondColumn] = useMemo(() => {\n    const shuffled = [...communityReviews].sort(() => Math.random() - 0.5);\n    const midPoint = Math.ceil(shuffled.length / 2);\n    return [shuffled.slice(0, midPoint), shuffled.slice(midPoint)];\n  }, []);\n\n  // Create animated columns for the animation effect\n  const createAnimatedColumn = (reviewsForColumn: typeof communityReviews, direction: 'left' | 'right') => {\n    const reviews = [...reviewsForColumn, ...reviewsForColumn]; // Duplicate for seamless loop\n    const animationClass = direction === 'left' ? 'animate-scroll-left' : 'animate-scroll-right';\n\n    return (\n      <div className={`flex gap-8 ${animationClass}`}>\n        {reviews.map((review, index) => (\n          <div\n            key={`${review.id}-${index}`}\n            className=\"flex-shrink-0 w-80 bg-neutral-700/50 backdrop-blur-sm rounded-lg p-6 border border-neutral-600/40 shadow-lg transition-all duration-300 relative overflow-hidden group\"\n          >\n            {/* Gradient blue-orange background with very grainy texture for hover */}\n            <div\n              className=\"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg\"\n              style={{\n                background: 'linear-gradient(135deg, rgba(59, 130, 246, 0.8) 0%, rgba(249, 115, 22, 0.8) 100%)',\n                backgroundImage: `url(\"data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilterHover'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='4.5' numOctaves='12' stitchTiles='stitch'/%3E%3CfeColorMatrix values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilterHover)'/%3E%3C/svg%3E\")`,\n                mixBlendMode: 'overlay'\n              }}\n            />\n            <div className=\"flex items-start gap-3\">\n              <Avatar className=\"h-10 w-10 flex-shrink-0\">\n                <AvatarImage src={review.avatar} alt={review.username} />\n                <AvatarFallback>{review.username.slice(0, 2).toUpperCase()}</AvatarFallback>\n              </Avatar>\n              <div className=\"flex-1 min-w-0\">\n                <div className=\"flex items-center gap-2 mb-2\">\n                  <span className=\"text-white font-medium text-sm\">@{review.username}</span>\n                  {/* X (Twitter) Logo with shadow */}\n                  <div className=\"relative\">\n                    <svg\n                      viewBox=\"0 0 24 24\"\n                      className=\"w-4 h-4 fill-white drop-shadow-lg\"\n                      style={{\n                        filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.3)) drop-shadow(0 0 8px rgba(255,255,255,0.1))'\n                      }}\n                    >\n                      <path d=\"M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z\"/>\n                    </svg>\n                  </div>\n                </div>\n                <p className=\"text-neutral-300 text-sm leading-relaxed\">\n                  {review.content.replace(/@cubentbase/g, '@cubent.dev')}\n                </p>\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n    );\n  };\n\n  return (\n    <div className=\"w-full relative px-4 sm:px-6\" style={{ backgroundColor: '#161616' }}>\n      <div\n        className=\"max-w-7xl mx-auto px-6 sm:px-8 lg:px-12 py-24 lg:py-36 relative overflow-hidden\"\n        style={{\n          border: '1px solid rgba(255, 255, 255, 0.08)',\n          borderTop: 'none',\n          backgroundColor: 'transparent'\n        }}\n      >\n        <div className=\"flex flex-col items-center gap-10\">\n          {/* Header */}\n          <div className=\"text-center\">\n            <h2 className=\"text-white font-regular text-3xl tracking-tighter md:text-5xl mb-4\">\n              Join the community\n            </h2>\n            <p className=\"text-neutral-400 text-lg max-w-2xl mx-auto\">\n              Discover what our community has to say about their Cubent experience.\n            </p>\n          </div>\n\n          {/* Community Buttons */}\n          <div className=\"flex flex-col sm:flex-row gap-4\">\n            <Button\n              variant=\"outline\"\n              className=\"bg-neutral-900/50 border-neutral-700 text-white hover:bg-neutral-800/50 backdrop-blur-sm\"\n              asChild\n            >\n              <a\n                href=\"https://github.com/cubent/discussions\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"flex items-center gap-2\"\n              >\n                <Github className=\"w-4 h-4\" />\n                GitHub discussions\n                <ExternalLink className=\"w-3 h-3\" />\n              </a>\n            </Button>\n            <Button\n              variant=\"outline\"\n              className=\"bg-neutral-900/50 border-neutral-700 text-white hover:bg-neutral-800/50 backdrop-blur-sm\"\n              asChild\n            >\n              <a\n                href=\"https://discord.gg/cubent\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"flex items-center gap-2\"\n              >\n                <MessageCircle className=\"w-4 h-4\" />\n                Discord\n                <ExternalLink className=\"w-3 h-3\" />\n              </a>\n            </Button>\n          </div>\n\n          {/* Animated Reviews */}\n          <div className=\"w-full max-w-7xl mx-auto\">\n            <div className=\"space-y-12 mask-gradient\">\n              {/* First column - scrolling left */}\n              <div className=\"flex flex-col gap-8\">\n                {createAnimatedColumn(firstColumn, 'left')}\n                {createAnimatedColumn(secondColumn, 'right')}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AAKA;AAEA;AAAA;AAAA;AACA;;;AAVA;;;;;AAgBA,gCAAgC;AAChC,MAAM,mBAAmB;IACvB;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;CACD;AAEM,MAAM,YAAY,CAAC,EAAE,UAAU,EAAkB;;IACtD,MAAM,CAAC,aAAa,aAAa,GAAG,CAAA,GAAA,4QAAA,CAAA,UAAO,AAAD;6BAAE;YAC1C,MAAM,WAAW;mBAAI;aAAiB,CAAC,IAAI;8CAAC,IAAM,KAAK,MAAM,KAAK;;YAClE,MAAM,WAAW,KAAK,IAAI,CAAC,SAAS,MAAM,GAAG;YAC7C,OAAO;gBAAC,SAAS,KAAK,CAAC,GAAG;gBAAW,SAAS,KAAK,CAAC;aAAU;QAChE;4BAAG,EAAE;IAEL,mDAAmD;IACnD,MAAM,uBAAuB,CAAC,kBAA2C;QACvE,MAAM,UAAU;eAAI;eAAqB;SAAiB,EAAE,8BAA8B;QAC1F,MAAM,iBAAiB,cAAc,SAAS,wBAAwB;QAEtE,qBACE,4SAAC;YAAI,WAAW,CAAC,WAAW,EAAE,gBAAgB;sBAC3C,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,4SAAC;oBAEC,WAAU;;sCAGV,4SAAC;4BACC,WAAU;4BACV,OAAO;gCACL,YAAY;gCACZ,iBAAiB,CAAC,sYAAsY,CAAC;gCACzZ,cAAc;4BAChB;;;;;;sCAEF,4SAAC;4BAAI,WAAU;;8CACb,4SAAC,8JAAA,CAAA,SAAM;oCAAC,WAAU;;sDAChB,4SAAC,8JAAA,CAAA,cAAW;4CAAC,KAAK,OAAO,MAAM;4CAAE,KAAK,OAAO,QAAQ;;;;;;sDACrD,4SAAC,8JAAA,CAAA,iBAAc;sDAAE,OAAO,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,WAAW;;;;;;;;;;;;8CAE1D,4SAAC;oCAAI,WAAU;;sDACb,4SAAC;4CAAI,WAAU;;8DACb,4SAAC;oDAAK,WAAU;;wDAAiC;wDAAE,OAAO,QAAQ;;;;;;;8DAElE,4SAAC;oDAAI,WAAU;8DACb,cAAA,4SAAC;wDACC,SAAQ;wDACR,WAAU;wDACV,OAAO;4DACL,QAAQ;wDACV;kEAEA,cAAA,4SAAC;4DAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;sDAId,4SAAC;4CAAE,WAAU;sDACV,OAAO,OAAO,CAAC,OAAO,CAAC,gBAAgB;;;;;;;;;;;;;;;;;;;mBAlCzC,GAAG,OAAO,EAAE,CAAC,CAAC,EAAE,OAAO;;;;;;;;;;IA0CtC;IAEA,qBACE,4SAAC;QAAI,WAAU;QAA+B,OAAO;YAAE,iBAAiB;QAAU;kBAChF,cAAA,4SAAC;YACC,WAAU;YACV,OAAO;gBACL,QAAQ;gBACR,WAAW;gBACX,iBAAiB;YACnB;sBAEA,cAAA,4SAAC;gBAAI,WAAU;;kCAEb,4SAAC;wBAAI,WAAU;;0CACb,4SAAC;gCAAG,WAAU;0CAAqE;;;;;;0CAGnF,4SAAC;gCAAE,WAAU;0CAA6C;;;;;;;;;;;;kCAM5D,4SAAC;wBAAI,WAAU;;0CACb,4SAAC,8JAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,WAAU;gCACV,OAAO;0CAEP,cAAA,4SAAC;oCACC,MAAK;oCACL,QAAO;oCACP,KAAI;oCACJ,WAAU;;sDAEV,4SAAC,6RAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAY;sDAE9B,4SAAC,6SAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAG5B,4SAAC,8JAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,WAAU;gCACV,OAAO;0CAEP,cAAA,4SAAC;oCACC,MAAK;oCACL,QAAO;oCACP,KAAI;oCACJ,WAAU;;sDAEV,4SAAC,+SAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;wCAAY;sDAErC,4SAAC,6SAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAM9B,4SAAC;wBAAI,WAAU;kCACb,cAAA,4SAAC;4BAAI,WAAU;sCAEb,cAAA,4SAAC;gCAAI,WAAU;;oCACZ,qBAAqB,aAAa;oCAClC,qBAAqB,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQpD;GAnIa;KAAA", "debugId": null}}, {"offset": {"line": 588, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/%28home%29/components/download.tsx"], "sourcesContent": ["'use client';\n\nimport { Button } from '@repo/design-system/components/ui/button';\nimport Link from 'next/link';\n\nexport const Download = () => {\n  return (\n    <div className=\"w-full relative px-4 sm:px-6\" style={{ backgroundColor: '#161616' }}>\n      <div\n        className=\"max-w-7xl mx-auto px-6 sm:px-8 lg:px-12 py-24 lg:py-36 relative overflow-hidden\"\n        style={{\n          border: '1px solid rgba(255, 255, 255, 0.08)',\n          borderTop: 'none',\n          backgroundColor: 'transparent'\n        }}\n      >\n        <div className=\"flex flex-col items-center gap-8 max-w-4xl mx-auto text-center\">\n          {/* Header */}\n          <div className=\"space-y-4\">\n            <div className=\"text-orange-500 text-sm font-medium tracking-wider uppercase\">\n              — Let's Code\n            </div>\n            <h2 className=\"text-white font-regular text-3xl tracking-tighter md:text-5xl leading-tight\">\n              Install our extension and<br />\n              start coding today\n            </h2>\n            <p className=\"text-white/70 text-lg leading-relaxed max-w-2xl mx-auto\">\n              Get started with AI-powered development. Choose your preferred editor and experience the future of coding.\n            </p>\n          </div>\n\n          {/* Download Buttons */}\n          <div className=\"flex flex-col sm:flex-row gap-4 w-full max-w-md\">\n            <Button\n              size=\"lg\"\n              className=\"flex-1 bg-neutral-700/50 backdrop-blur-sm text-white border-0 rounded-lg px-8 py-4 text-lg font-medium transition-all duration-300 hover:scale-105 hover:bg-neutral-600/60\"\n              asChild\n            >\n              <Link\n                href=\"https://marketplace.visualstudio.com/items?itemName=cubent.cubent\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n              >\n                <div className=\"flex items-center gap-3\">\n                  <svg className=\"w-6 h-6\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                    <path d=\"M23.15 2.587L18.21.21a1.494 1.494 0 0 0-1.705.29l-9.46 8.63-4.12-3.128a.999.999 0 0 0-1.276.057L.327 7.261A1 1 0 0 0 .326 8.74L3.899 12 .326 15.26a1 1 0 0 0 .001 1.479L1.65 17.94a.999.999 0 0 0 1.276.057l4.12-3.128 9.46 8.63a1.492 1.492 0 0 0 1.704.29l4.942-2.377A1.5 1.5 0 0 0 24 20.06V3.939a1.5 1.5 0 0 0-.85-1.352zm-5.146 14.861L10.826 12l7.178-5.448v10.896z\"/>\n                  </svg>\n                  VS Code\n                </div>\n              </Link>\n            </Button>\n\n            <Button\n              size=\"lg\"\n              className=\"flex-1 bg-neutral-700/50 backdrop-blur-sm text-white border border-neutral-600/40 rounded-lg px-8 py-4 text-lg font-medium transition-all duration-300 hover:scale-105 hover:bg-neutral-600/60\"\n              asChild\n            >\n              <Link\n                href=\"https://plugins.jetbrains.com/plugin/cubent\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n              >\n                <div className=\"flex items-center gap-3\">\n                  <svg className=\"w-6 h-6\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                    <path d=\"M0 0v24h24V0H0zm3.723 3.111h5v1.834h-1.39v6.277h1.39v1.834h-5v-1.834h1.444V4.945H3.723V3.111zm11.055 0H17v1.834h-1.389v6.277H17v1.834h-2.222V3.111zm-8.334 8.944H9.61v1.833H6.444v-1.833z\"/>\n                  </svg>\n                  JetBrains\n                </div>\n              </Link>\n            </Button>\n          </div>\n\n          {/* Additional info */}\n          <div className=\"text-center\">\n            <p className=\"text-white/50 text-sm\">\n              Not using Windows? <Link href=\"#\" className=\"text-orange-500 hover:text-orange-400 transition-colors\">View all download options</Link>\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKO,MAAM,WAAW;IACtB,qBACE,4SAAC;QAAI,WAAU;QAA+B,OAAO;YAAE,iBAAiB;QAAU;kBAChF,cAAA,4SAAC;YACC,WAAU;YACV,OAAO;gBACL,QAAQ;gBACR,WAAW;gBACX,iBAAiB;YACnB;sBAEA,cAAA,4SAAC;gBAAI,WAAU;;kCAEb,4SAAC;wBAAI,WAAU;;0CACb,4SAAC;gCAAI,WAAU;0CAA+D;;;;;;0CAG9E,4SAAC;gCAAG,WAAU;;oCAA8E;kDACjE,4SAAC;;;;;oCAAK;;;;;;;0CAGjC,4SAAC;gCAAE,WAAU;0CAA0D;;;;;;;;;;;;kCAMzE,4SAAC;wBAAI,WAAU;;0CACb,4SAAC,8JAAA,CAAA,SAAM;gCACL,MAAK;gCACL,WAAU;gCACV,OAAO;0CAEP,cAAA,4SAAC,8QAAA,CAAA,UAAI;oCACH,MAAK;oCACL,QAAO;oCACP,KAAI;8CAEJ,cAAA,4SAAC;wCAAI,WAAU;;0DACb,4SAAC;gDAAI,WAAU;gDAAU,SAAQ;gDAAY,MAAK;0DAChD,cAAA,4SAAC;oDAAK,GAAE;;;;;;;;;;;4CACJ;;;;;;;;;;;;;;;;;0CAMZ,4SAAC,8JAAA,CAAA,SAAM;gCACL,MAAK;gCACL,WAAU;gCACV,OAAO;0CAEP,cAAA,4SAAC,8QAAA,CAAA,UAAI;oCACH,MAAK;oCACL,QAAO;oCACP,KAAI;8CAEJ,cAAA,4SAAC;wCAAI,WAAU;;0DACb,4SAAC;gDAAI,WAAU;gDAAU,SAAQ;gDAAY,MAAK;0DAChD,cAAA,4SAAC;oDAAK,GAAE;;;;;;;;;;;4CACJ;;;;;;;;;;;;;;;;;;;;;;;kCAQd,4SAAC;wBAAI,WAAU;kCACb,cAAA,4SAAC;4BAAE,WAAU;;gCAAwB;8CAChB,4SAAC,8QAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;8CAA0D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpH;KA7Ea", "debugId": null}}, {"offset": {"line": 807, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/%28home%29/components/trusted-by.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\n\ntype TrustedByProps = {\n  dictionary: any;\n};\n\nexport const TrustedBy = ({ dictionary }: TrustedByProps) => {\n  // Add CSS animation for scrolling companies\n  useEffect(() => {\n    const style = document.createElement('style');\n    style.textContent = `\n      @keyframes scroll-companies {\n        0% {\n          transform: translateX(0);\n        }\n        100% {\n          transform: translateX(-50%);\n        }\n      }\n\n      .animate-scroll-companies {\n        animation: scroll-companies 20s linear infinite;\n      }\n    `;\n    document.head.appendChild(style);\n\n    return () => {\n      document.head.removeChild(style);\n    };\n  }, []);\n\n  return (\n  <div className=\"w-full relative overflow-hidden py-6 lg:py-8\">\n\n\n    <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 relative z-20\">\n      <div className=\"flex flex-col items-center justify-center gap-6\">\n\n\n        {/* Company logos - animated horizontal scroll showing 4 at a time */}\n        <div className=\"w-full max-w-2xl relative overflow-hidden mx-auto\">\n          {/* Left shadow gradient */}\n          <div\n            className=\"absolute left-0 top-0 bottom-0 w-16 z-10 pointer-events-none\"\n            style={{\n              background: 'linear-gradient(to right, #161616, transparent)'\n            }}\n          ></div>\n\n          {/* Right shadow gradient */}\n          <div\n            className=\"absolute right-0 top-0 bottom-0 w-16 z-10 pointer-events-none\"\n            style={{\n              background: 'linear-gradient(to left, #161616, transparent)'\n            }}\n          ></div>\n\n          <div className=\"flex gap-24 animate-scroll-companies opacity-60 hover:opacity-80 transition-opacity\">\n            {/* First set of all 6 companies */}\n            <div className=\"flex gap-24 shrink-0\">\n            {/* Notion Logo */}\n            <div className=\"flex flex-col items-center gap-2\">\n              <svg className=\"h-8 w-8 text-muted-foreground\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M4.459 4.208c.746.606 1.026.56 2.428.466l13.215-.793c.28 0 .047-.28-.046-.326L17.86 1.968c-.42-.326-.981-.7-2.055-.607L3.01 2.295c-.466.046-.56.28-.374.466zm.793 3.08v13.904c0 .747.373 1.027 1.214.98l14.523-.84c.841-.046.935-.56.935-1.167V6.354c0-.606-.233-.933-.748-.887l-15.177.887c-.56.047-.747.327-.747.933zm14.337.745c.093.42 0 .84-.42.888l-.7.14v10.264c-.608.327-1.168.514-1.635.514-.748 0-.935-.234-1.495-.933l-4.577-7.186v6.952L12.21 19s0 .84-1.168.84l-3.222.186c-.093-.186 0-.653.327-.746l.84-.233V9.854L7.822 9.76c-.094-.42.14-1.026.793-1.073l3.456-.233 4.764 7.279v-6.44l-1.215-.139c-.093-.514.28-.887.747-.933zM1.936 1.035l13.31-.98c1.634-.14 2.055-.047 3.082.7l4.249 2.986c.7.513.934.653.934 1.213v16.378c0 1.026-.373 1.634-1.68 1.726l-15.458.934c-.98.047-1.448-.093-1.962-.747l-3.129-4.06c-.56-.747-.793-1.306-.793-1.96V2.667c0-.839.374-1.54 1.447-1.632z\"/>\n              </svg>\n              <span className=\"text-xs text-muted-foreground font-medium\">Notion</span>\n            </div>\n\n            {/* Figma Logo */}\n            <div className=\"flex flex-col items-center gap-2\">\n              <svg className=\"h-8 w-8 text-muted-foreground\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M15.852 8.981h-4.588V0h4.588c2.476 0 4.49 2.014 4.49 4.49s-2.014 4.491-4.49 4.491zM12.735 7.51h3.117c1.665 0 3.019-1.355 3.019-3.019s-1.354-3.019-3.019-3.019h-3.117V7.51zm0 1.471H8.148c-2.476 0-4.49-2.015-4.49-4.491S5.672 0 8.148 0h4.588v8.981zm-4.587-7.51c-1.665 0-3.019 1.355-3.019 3.019s1.354 3.02 3.019 3.02h3.117V1.471H8.148zm4.587 15.019H8.148c-2.476 0-4.49-2.014-4.49-4.49s2.014-4.49 4.49-4.49h4.588v8.98zM8.148 8.981c-1.665 0-3.019 1.355-3.019 3.019s1.355 3.019 3.019 3.019h3.117V8.981H8.148zM8.172 24c-2.489 0-4.515-2.014-4.515-4.49s2.014-4.49 4.49-4.49h4.588v4.441c0 2.503-2.047 4.539-4.563 4.539zm-.024-7.51a3.023 3.023 0 0 0-3.019 3.019c0 1.665 1.365 3.019 3.044 3.019 1.705 0 3.093-1.376 3.093-3.068v-2.97H8.148z\"/>\n                <path d=\"M12.764 24c-2.516 0-4.563-2.036-4.563-4.539s2.047-4.539 4.563-4.539 4.564 2.036 4.564 4.539S15.28 24 12.764 24zm0-7.588a3.023 3.023 0 0 0-3.093 3.049c0 1.691 1.387 3.068 3.093 3.068s3.093-1.377 3.093-3.068-1.387-3.049-3.093-3.049z\"/>\n              </svg>\n              <span className=\"text-xs text-muted-foreground font-medium\">Figma</span>\n            </div>\n\n            {/* Discord Logo */}\n            <div className=\"flex flex-col items-center gap-2\">\n              <svg className=\"h-8 w-8 text-muted-foreground\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M20.317 4.3698a19.7913 19.7913 0 00-4.8851-1.5152.0741.0741 0 00-.0785.0371c-.211.3753-.4447.8648-.6083 1.2495-1.8447-.2762-3.68-.2762-5.4868 0-.1636-.3933-.4058-.8742-.6177-1.2495a.077.077 0 00-.0785-.037 19.7363 19.7363 0 00-4.8852 1.515.0699.0699 0 00-.0321.0277C.5334 9.0458-.319 13.5799.0992 18.0578a.0824.0824 0 00.0312.0561c2.0528 1.5076 4.0413 2.4228 5.9929 3.0294a.0777.0777 0 00.0842-.0276c.4616-.6304.8731-1.2952 1.226-1.9942a.076.076 0 00-.0416-.1057c-.6528-.2476-1.2743-.5495-1.8722-.8923a.077.077 0 01-.0076-.1277c.1258-.0943.2517-.1923.3718-.2914a.0743.0743 0 01.0776-.0105c3.9278 1.7933 8.18 1.7933 12.0614 0a.0739.0739 0 01.0785.0095c.1202.099.246.1981.3728.2924a.077.077 0 01-.0066.1276 12.2986 12.2986 0 01-1.873.8914.0766.0766 0 00-.0407.1067c.3604.698.7719 1.3628 1.225 1.9932a.076.076 0 00.0842.0286c1.961-.6067 3.9495-1.5219 6.0023-3.0294a.077.077 0 00.0313-.0552c.5004-5.177-.8382-9.6739-3.5485-13.6604a.061.061 0 00-.0312-.0286zM8.02 15.3312c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9555-2.4189 2.157-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419-.0002 1.3332-.9555 2.4189-2.1569 2.4189zm7.9748 0c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9554-2.4189 2.1569-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.9554 2.4189-2.1568 2.4189Z\"/>\n              </svg>\n              <span className=\"text-xs text-muted-foreground font-medium\">Discord</span>\n            </div>\n\n            {/* Slack Logo */}\n            <div className=\"flex flex-col items-center gap-2\">\n              <svg className=\"h-8 w-8 text-muted-foreground\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M5.042 15.165a2.528 2.528 0 0 1-2.52 2.523A2.528 2.528 0 0 1 0 15.165a2.527 2.527 0 0 1 2.522-2.52h2.52v2.52zM6.313 15.165a2.527 2.527 0 0 1 2.521-2.52 2.527 2.527 0 0 1 2.521 2.52v6.313A2.528 2.528 0 0 1 8.834 24a2.528 2.528 0 0 1-2.521-2.522v-6.313zM8.834 5.042a2.528 2.528 0 0 1-2.521-2.52A2.528 2.528 0 0 1 8.834 0a2.528 2.528 0 0 1 2.521 2.522v2.52H8.834zM8.834 6.313a2.528 2.528 0 0 1 2.521 2.521 2.528 2.528 0 0 1-2.521 2.521H2.522A2.528 2.528 0 0 1 0 8.834a2.528 2.528 0 0 1 2.522-2.521h6.312zM18.956 8.834a2.528 2.528 0 0 1 2.522-2.521A2.528 2.528 0 0 1 24 8.834a2.528 2.528 0 0 1-2.522 2.521h-2.522V8.834zM17.688 8.834a2.528 2.528 0 0 1-2.523 2.521 2.527 2.527 0 0 1-2.52-2.521V2.522A2.527 2.527 0 0 1 15.165 0a2.528 2.528 0 0 1 2.523 2.522v6.312zM15.165 18.956a2.528 2.528 0 0 1 2.523 2.522A2.528 2.528 0 0 1 15.165 24a2.527 2.527 0 0 1-2.52-2.522v-2.522h2.52zM15.165 17.688a2.527 2.527 0 0 1-2.52-2.523 2.526 2.526 0 0 1 2.52-2.52h6.313A2.527 2.527 0 0 1 24 15.165a2.528 2.528 0 0 1-2.522 2.523h-6.313z\"/>\n              </svg>\n              <span className=\"text-xs text-muted-foreground font-medium\">Slack</span>\n            </div>\n\n            {/* Spotify Logo */}\n            <div className=\"flex flex-col items-center gap-2\">\n              <svg className=\"h-8 w-8 text-muted-foreground\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M12 0C5.4 0 0 5.4 0 12s5.4 12 12 12 12-5.4 12-12S18.66 0 12 0zm5.521 17.34c-.24.359-.66.48-1.021.24-2.82-1.74-6.36-2.101-10.561-1.141-.418.122-.779-.179-.899-.539-.12-.421.18-.78.54-.9 4.56-1.021 8.52-.6 11.64 1.32.42.18.479.659.301 1.02zm1.44-3.3c-.301.42-.841.6-1.262.3-3.239-1.98-8.159-2.58-11.939-1.38-.479.12-1.02-.12-1.14-.6-.12-.48.12-1.021.6-1.141C9.6 9.9 15 10.561 18.72 12.84c.361.181.54.78.241 1.2zm.12-3.36C15.24 8.4 8.82 8.16 5.16 9.301c-.6.179-1.2-.181-1.38-.721-.18-.601.18-1.2.72-1.381 4.26-1.26 11.28-1.02 15.721 1.621.539.3.719 1.02.42 1.56-.299.421-1.02.599-1.559.3z\"/>\n              </svg>\n              <span className=\"text-xs text-muted-foreground font-medium\">Spotify</span>\n            </div>\n\n            {/* Dropbox Logo */}\n            <div className=\"flex flex-col items-center gap-2\">\n              <svg className=\"h-8 w-8 text-muted-foreground\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M6 2L12 6.5 6 11 0 6.5 6 2zm6 4.5L18 2l6 4.5L18 11l-6-4.5zM0 13.5L6 9l6 4.5L6 18l-6-4.5zm18-4.5l6 4.5L18 18l-6-4.5L18 9zM6 20.5l6-4.5 6 4.5L12 25l-6-4.5z\"/>\n              </svg>\n              <span className=\"text-xs text-muted-foreground font-medium\">Dropbox</span>\n            </div>\n            </div>\n\n            {/* Duplicate set for seamless scrolling - all 6 companies */}\n            <div className=\"flex gap-24 shrink-0\">\n              {/* Notion Logo */}\n              <div className=\"flex flex-col items-center gap-2\">\n                <svg className=\"h-8 w-8 text-muted-foreground\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <path d=\"M4.459 4.208c.746.606 1.026.56 2.428.466l13.215-.793c.28 0 .047-.28-.046-.326L17.86 1.968c-.42-.326-.981-.7-2.055-.607L3.01 2.295c-.466.046-.56.28-.374.466zm.793 3.08v13.904c0 .747.373 1.027 1.214.98l14.523-.84c.841-.046.935-.56.935-1.167V6.354c0-.606-.233-.933-.748-.887l-15.177.887c-.56.047-.747.327-.747.933zm14.337.745c.093.42 0 .84-.42.888l-.7.14v10.264c-.608.327-1.168.514-1.635.514-.748 0-.935-.234-1.495-.933l-4.577-7.186v6.952L12.21 19s0 .84-1.168.84l-3.222.186c-.093-.186 0-.653.327-.746l.84-.233V9.854L7.822 9.76c-.094-.42.14-1.026.793-1.073l3.456-.233 4.764 7.279v-6.44l-1.215-.139c-.093-.514.28-.887.747-.933zM1.936 1.035l13.31-.98c1.634-.14 2.055-.047 3.082.7l4.249 2.986c.7.513.934.653.934 1.213v16.378c0 1.026-.373 1.634-1.68 1.726l-15.458.934c-.98.047-1.448-.093-1.962-.747l-3.129-4.06c-.56-.747-.793-1.306-.793-1.96V2.667c0-.839.374-1.54 1.447-1.632z\"/>\n                </svg>\n                <span className=\"text-xs text-muted-foreground font-medium\">Notion</span>\n              </div>\n\n              {/* Figma Logo */}\n              <div className=\"flex flex-col items-center gap-2\">\n                <svg className=\"h-8 w-8 text-muted-foreground\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <path d=\"M15.852 8.981h-4.588V0h4.588c2.476 0 4.49 2.014 4.49 4.49s-2.014 4.491-4.49 4.491zM12.735 7.51h3.117c1.665 0 3.019-1.355 3.019-3.019s-1.354-3.019-3.019-3.019h-3.117V7.51zm0 1.471H8.148c-2.476 0-4.49-2.015-4.49-4.491S5.672 0 8.148 0h4.588v8.981zm-4.587-7.51c-1.665 0-3.019 1.355-3.019 3.019s1.354 3.02 3.019 3.02h3.117V1.471H8.148zm4.587 15.019H8.148c-2.476 0-4.49-2.014-4.49-4.49s2.014-4.49 4.49-4.49h4.588v8.98zM8.148 8.981c-1.665 0-3.019 1.355-3.019 3.019s1.355 3.019 3.019 3.019h3.117V8.981H8.148zM8.172 24c-2.489 0-4.515-2.014-4.515-4.49s2.014-4.49 4.49-4.49h4.588v4.441c0 2.503-2.047 4.539-4.563 4.539zm-.024-7.51a3.023 3.023 0 0 0-3.019 3.019c0 1.665 1.365 3.019 3.044 3.019 1.705 0 3.093-1.376 3.093-3.068v-2.97H8.148z\"/>\n                  <path d=\"M12.764 24c-2.516 0-4.563-2.036-4.563-4.539s2.047-4.539 4.563-4.539 4.564 2.036 4.564 4.539S15.28 24 12.764 24zm0-7.588a3.023 3.023 0 0 0-3.093 3.049c0 1.691 1.387 3.068 3.093 3.068s3.093-1.377 3.093-3.068-1.387-3.049-3.093-3.049z\"/>\n                </svg>\n                <span className=\"text-xs text-muted-foreground font-medium\">Figma</span>\n              </div>\n\n              {/* Discord Logo */}\n              <div className=\"flex flex-col items-center gap-2\">\n                <svg className=\"h-8 w-8 text-muted-foreground\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <path d=\"M20.317 4.3698a19.7913 19.7913 0 00-4.8851-1.5152.0741.0741 0 00-.0785.0371c-.211.3753-.4447.8648-.6083 1.2495-1.8447-.2762-3.68-.2762-5.4868 0-.1636-.3933-.4058-.8742-.6177-1.2495a.077.077 0 00-.0785-.037 19.7363 19.7363 0 00-4.8852 1.515.0699.0699 0 00-.0321.0277C.5334 9.0458-.319 13.5799.0992 18.0578a.0824.0824 0 00.0312.0561c2.0528 1.5076 4.0413 2.4228 5.9929 3.0294a.0777.0777 0 00.0842-.0276c.4616-.6304.8731-1.2952 1.226-1.9942a.076.076 0 00-.0416-.1057c-.6528-.2476-1.2743-.5495-1.8722-.8923a.077.077 0 01-.0076-.1277c.1258-.0943.2517-.1923.3718-.2914a.0743.0743 0 01.0776-.0105c3.9278 1.7933 8.18 1.7933 12.0614 0a.0739.0739 0 01.0785.0095c.1202.099.246.1981.3728.2924a.077.077 0 01-.0066.1276 12.2986 12.2986 0 01-1.873.8914.0766.0766 0 00-.0407.1067c.3604.698.7719 1.3628 1.225 1.9932a.076.076 0 00.0842.0286c1.961-.6067 3.9495-1.5219 6.0023-3.0294a.077.077 0 00.0313-.0552c.5004-5.177-.8382-9.6739-3.5485-13.6604a.061.061 0 00-.0312-.0286zM8.02 15.3312c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9555-2.4189 2.157-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419-.0002 1.3332-.9555 2.4189-2.1569 2.4189zm7.9748 0c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9554-2.4189 2.1569-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.9554 2.4189-2.1568 2.4189Z\"/>\n                </svg>\n                <span className=\"text-xs text-muted-foreground font-medium\">Discord</span>\n              </div>\n\n              {/* Slack Logo */}\n              <div className=\"flex flex-col items-center gap-2\">\n                <svg className=\"h-8 w-8 text-muted-foreground\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <path d=\"M5.042 15.165a2.528 2.528 0 0 1-2.52 2.523A2.528 2.528 0 0 1 0 15.165a2.527 2.527 0 0 1 2.522-2.52h2.52v2.52zM6.313 15.165a2.527 2.527 0 0 1 2.521-2.52 2.527 2.527 0 0 1 2.521 2.52v6.313A2.528 2.528 0 0 1 8.834 24a2.528 2.528 0 0 1-2.521-2.522v-6.313zM8.834 5.042a2.528 2.528 0 0 1-2.521-2.52A2.528 2.528 0 0 1 8.834 0a2.528 2.528 0 0 1 2.521 2.522v2.52H8.834zM8.834 6.313a2.528 2.528 0 0 1 2.521 2.521 2.528 2.528 0 0 1-2.521 2.521H2.522A2.528 2.528 0 0 1 0 8.834a2.528 2.528 0 0 1 2.522-2.521h6.312zM18.956 8.834a2.528 2.528 0 0 1 2.522-2.521A2.528 2.528 0 0 1 24 8.834a2.528 2.528 0 0 1-2.522 2.521h-2.522V8.834zM17.688 8.834a2.528 2.528 0 0 1-2.523 2.521 2.527 2.527 0 0 1-2.52-2.521V2.522A2.527 2.527 0 0 1 15.165 0a2.528 2.528 0 0 1 2.523 2.522v6.312zM15.165 18.956a2.528 2.528 0 0 1 2.523 2.522A2.528 2.528 0 0 1 15.165 24a2.527 2.527 0 0 1-2.52-2.522v-2.522h2.52zM15.165 17.688a2.527 2.527 0 0 1-2.52-2.523 2.526 2.526 0 0 1 2.52-2.52h6.313A2.527 2.527 0 0 1 24 15.165a2.528 2.528 0 0 1-2.522 2.523h-6.313z\"/>\n                </svg>\n                <span className=\"text-xs text-muted-foreground font-medium\">Slack</span>\n              </div>\n\n              {/* Spotify Logo */}\n              <div className=\"flex flex-col items-center gap-2\">\n                <svg className=\"h-8 w-8 text-muted-foreground\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <path d=\"M12 0C5.4 0 0 5.4 0 12s5.4 12 12 12 12-5.4 12-12S18.66 0 12 0zm5.521 17.34c-.24.359-.66.48-1.021.24-2.82-1.74-6.36-2.101-10.561-1.141-.418.122-.779-.179-.899-.539-.12-.421.18-.78.54-.9 4.56-1.021 8.52-.6 11.64 1.32.42.18.479.659.301 1.02zm1.44-3.3c-.301.42-.841.6-1.262.3-3.239-1.98-8.159-2.58-11.939-1.38-.479.12-1.02-.12-1.14-.6-.12-.48.12-1.021.6-1.141C9.6 9.9 15 10.561 18.72 12.84c.361.181.54.78.241 1.2zm.12-3.36C15.24 8.4 8.82 8.16 5.16 9.301c-.6.179-1.2-.181-1.38-.721-.18-.601.18-1.2.72-1.381 4.26-1.26 11.28-1.02 15.721 1.621.539.3.719 1.02.42 1.56-.299.421-1.02.599-1.559.3z\"/>\n                </svg>\n                <span className=\"text-xs text-muted-foreground font-medium\">Spotify</span>\n              </div>\n\n              {/* Dropbox Logo */}\n              <div className=\"flex flex-col items-center gap-2\">\n                <svg className=\"h-8 w-8 text-muted-foreground\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <path d=\"M6 2L12 6.5 6 11 0 6.5 6 2zm6 4.5L18 2l6 4.5L18 11l-6-4.5zM0 13.5L6 9l6 4.5L6 18l-6-4.5zm18-4.5l6 4.5L18 18l-6-4.5L18 9zM6 20.5l6-4.5 6 4.5L12 25l-6-4.5z\"/>\n                </svg>\n                <span className=\"text-xs text-muted-foreground font-medium\">Dropbox</span>\n              </div>\n            </div>\n          </div>\n        </div>\n\n\n      </div>\n    </div>\n  </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAQO,MAAM,YAAY,CAAC,EAAE,UAAU,EAAkB;;IACtD,4CAA4C;IAC5C,CAAA,GAAA,4QAAA,CAAA,YAAS,AAAD;+BAAE;YACR,MAAM,QAAQ,SAAS,aAAa,CAAC;YACrC,MAAM,WAAW,GAAG,CAAC;;;;;;;;;;;;;IAarB,CAAC;YACD,SAAS,IAAI,CAAC,WAAW,CAAC;YAE1B;uCAAO;oBACL,SAAS,IAAI,CAAC,WAAW,CAAC;gBAC5B;;QACF;8BAAG,EAAE;IAEL,qBACA,4SAAC;QAAI,WAAU;kBAGb,cAAA,4SAAC;YAAI,WAAU;sBACb,cAAA,4SAAC;gBAAI,WAAU;0BAIb,cAAA,4SAAC;oBAAI,WAAU;;sCAEb,4SAAC;4BACC,WAAU;4BACV,OAAO;gCACL,YAAY;4BACd;;;;;;sCAIF,4SAAC;4BACC,WAAU;4BACV,OAAO;gCACL,YAAY;4BACd;;;;;;sCAGF,4SAAC;4BAAI,WAAU;;8CAEb,4SAAC;oCAAI,WAAU;;sDAEf,4SAAC;4CAAI,WAAU;;8DACb,4SAAC;oDAAI,WAAU;oDAAgC,SAAQ;oDAAY,MAAK;8DACtE,cAAA,4SAAC;wDAAK,GAAE;;;;;;;;;;;8DAEV,4SAAC;oDAAK,WAAU;8DAA4C;;;;;;;;;;;;sDAI9D,4SAAC;4CAAI,WAAU;;8DACb,4SAAC;oDAAI,WAAU;oDAAgC,SAAQ;oDAAY,MAAK;;sEACtE,4SAAC;4DAAK,GAAE;;;;;;sEACR,4SAAC;4DAAK,GAAE;;;;;;;;;;;;8DAEV,4SAAC;oDAAK,WAAU;8DAA4C;;;;;;;;;;;;sDAI9D,4SAAC;4CAAI,WAAU;;8DACb,4SAAC;oDAAI,WAAU;oDAAgC,SAAQ;oDAAY,MAAK;8DACtE,cAAA,4SAAC;wDAAK,GAAE;;;;;;;;;;;8DAEV,4SAAC;oDAAK,WAAU;8DAA4C;;;;;;;;;;;;sDAI9D,4SAAC;4CAAI,WAAU;;8DACb,4SAAC;oDAAI,WAAU;oDAAgC,SAAQ;oDAAY,MAAK;8DACtE,cAAA,4SAAC;wDAAK,GAAE;;;;;;;;;;;8DAEV,4SAAC;oDAAK,WAAU;8DAA4C;;;;;;;;;;;;sDAI9D,4SAAC;4CAAI,WAAU;;8DACb,4SAAC;oDAAI,WAAU;oDAAgC,SAAQ;oDAAY,MAAK;8DACtE,cAAA,4SAAC;wDAAK,GAAE;;;;;;;;;;;8DAEV,4SAAC;oDAAK,WAAU;8DAA4C;;;;;;;;;;;;sDAI9D,4SAAC;4CAAI,WAAU;;8DACb,4SAAC;oDAAI,WAAU;oDAAgC,SAAQ;oDAAY,MAAK;8DACtE,cAAA,4SAAC;wDAAK,GAAE;;;;;;;;;;;8DAEV,4SAAC;oDAAK,WAAU;8DAA4C;;;;;;;;;;;;;;;;;;8CAK9D,4SAAC;oCAAI,WAAU;;sDAEb,4SAAC;4CAAI,WAAU;;8DACb,4SAAC;oDAAI,WAAU;oDAAgC,SAAQ;oDAAY,MAAK;8DACtE,cAAA,4SAAC;wDAAK,GAAE;;;;;;;;;;;8DAEV,4SAAC;oDAAK,WAAU;8DAA4C;;;;;;;;;;;;sDAI9D,4SAAC;4CAAI,WAAU;;8DACb,4SAAC;oDAAI,WAAU;oDAAgC,SAAQ;oDAAY,MAAK;;sEACtE,4SAAC;4DAAK,GAAE;;;;;;sEACR,4SAAC;4DAAK,GAAE;;;;;;;;;;;;8DAEV,4SAAC;oDAAK,WAAU;8DAA4C;;;;;;;;;;;;sDAI9D,4SAAC;4CAAI,WAAU;;8DACb,4SAAC;oDAAI,WAAU;oDAAgC,SAAQ;oDAAY,MAAK;8DACtE,cAAA,4SAAC;wDAAK,GAAE;;;;;;;;;;;8DAEV,4SAAC;oDAAK,WAAU;8DAA4C;;;;;;;;;;;;sDAI9D,4SAAC;4CAAI,WAAU;;8DACb,4SAAC;oDAAI,WAAU;oDAAgC,SAAQ;oDAAY,MAAK;8DACtE,cAAA,4SAAC;wDAAK,GAAE;;;;;;;;;;;8DAEV,4SAAC;oDAAK,WAAU;8DAA4C;;;;;;;;;;;;sDAI9D,4SAAC;4CAAI,WAAU;;8DACb,4SAAC;oDAAI,WAAU;oDAAgC,SAAQ;oDAAY,MAAK;8DACtE,cAAA,4SAAC;wDAAK,GAAE;;;;;;;;;;;8DAEV,4SAAC;oDAAK,WAAU;8DAA4C;;;;;;;;;;;;sDAI9D,4SAAC;4CAAI,WAAU;;8DACb,4SAAC;oDAAI,WAAU;oDAAgC,SAAQ;oDAAY,MAAK;8DACtE,cAAA,4SAAC;wDAAK,GAAE;;;;;;;;;;;8DAEV,4SAAC;oDAAK,WAAU;8DAA4C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW5E;GAnKa;KAAA", "debugId": null}}, {"offset": {"line": 1349, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/%28home%29/components/mockup.tsx"], "sourcesContent": ["'use client';\n\nimport Image from 'next/image';\nimport { X } from 'lucide-react';\nimport { useState } from 'react';\n\n// Feature data\nconst features = [\n  {\n    id: 1,\n    title: \"Sit back while it handles the heavy lifting, step by step\",\n    image: \"/images/cubent-feature-1.png\",\n    alt: \"Context Intelligence\",\n    description: \"Let <PERSON><PERSON><PERSON> chain terminal commands, edit files, run tests and open pull requests while you sip your coffee—true hands-free automation.\",\n    content: [\n      \"Skip the boilerplate. <PERSON><PERSON><PERSON> queues terminal commands, edits files, runs tests and even opens pull-requests in a single autonomous flow. <PERSON><PERSON><PERSON> revolutionizes this experience by providing deep, contextual understanding of your entire project ecosystem. Our advanced AI doesn't just read your code—it comprehends the intricate relationships between components, understands your architectural decisions, and learns from your coding patterns.\",\n\n      \"Unlike traditional code assistants that work in isolation, <PERSON><PERSON><PERSON> maintains a comprehensive map of your project's structure, dependencies, and design patterns. This enables it to make suggestions that aren't just syntactically correct, but architecturally sound and consistent with your existing codebase. Whether you're working on a microservices architecture, a monolithic application, or a complex distributed system, <PERSON><PERSON><PERSON> adapts to your specific context.\",\n\n      \"The intelligence extends beyond simple code completion. <PERSON><PERSON><PERSON> analyzes cross-file dependencies, understands the impact of changes across your entire system, and can predict potential issues before they arise. This proactive approach to development helps teams maintain code quality while accelerating their development velocity, making it an indispensable tool for serious product development teams.\"\n    ]\n  },\n  {\n    id: 2,\n    title: \"Stay in sync with a real-time panel that knows your project\",\n    image: \"/images/cubent-feature-2.png\",\n    alt: \"AI Screenshot Analysis\",\n    description: \"A single overlay that pulses with your repo’s heartbeat—files, problems, Git, terminals and folders always a glance away.\",\n    content: [\n      \"Never lose your place again. The context panel surfaces open files, failing tests, Git diffs, terminals and folders in one glanceable hub. Cubent's revolutionary screenshot-to-code technology bridges the gap between design and implementation, allowing developers to transform visual mockups into functional code in seconds rather than hours.\",\n\n      \"Our advanced computer vision algorithms analyze every pixel of your designs, understanding not just what elements are present, but how they should behave and interact. The system recognizes common UI patterns, understands responsive design principles, and generates code that follows modern best practices. Whether you're working with Figma designs, hand-drawn sketches, or competitor screenshots, Cubent can interpret and implement them accurately.\",\n\n      \"The generated code isn't just a static representation—it's production-ready, accessible, and optimized for performance. Cubent automatically handles responsive breakpoints, generates semantic HTML, applies appropriate ARIA labels, and ensures cross-browser compatibility. This means you can go from concept to working prototype in minutes, allowing for rapid iteration and faster time-to-market for your products.\"\n    ]\n  },\n  {\n    id: 3,\n    title: \"Instant browser preview & testing\",\n    image: \"/images/cubent-feature-3.png\",\n    alt: \"Smart Code Editing\",\n    description: \"Spin up a live browser, snap screenshots and sanity-check UX—all without ever Alt-Tabbing out of your editor.\",\n    content: [\n      \"Request a ‘preview’ in chat and Cubent spins up a local server, opens a browser preview and streams logs back to you. Cubent's intelligent editing capabilities go far beyond traditional autocomplete, offering a sophisticated understanding of code quality, performance implications, and best practices. Every suggestion is crafted with the goal of not just making your code work, but making it exceptional.\",\n\n      \"The system continuously analyzes your code for potential improvements, from micro-optimizations that enhance performance to architectural suggestions that improve maintainability. Cubent understands the nuances of different programming languages, frameworks, and design patterns, allowing it to provide highly specific and relevant recommendations tailored to your technology stack and coding style.\",\n\n      \"What sets Cubent apart is its ability to learn and adapt to your team's specific standards and preferences. It recognizes your coding conventions, understands your project's unique requirements, and evolves its suggestions to match your team's definition of perfect code. This results in a more consistent codebase, reduced technical debt, and a development experience that feels truly personalized and intelligent.\"\n    ]\n  }\n];\n\nexport const Mockup = () => {\n  const [selectedFeature, setSelectedFeature] = useState<typeof features[0] | null>(null);\n\n  return (\n  <>\n    <div className=\"w-full relative px-4 sm:px-6\" style={{ backgroundColor: '#161616' }}>\n      <div className=\"hidden flex-col items-center justify-center gap-2 py-6\">\n        <div className=\"relative w-full max-w-8xl\">\n          <div className=\"relative overflow-hidden\">\n            <Image\n              src=\"/images/Cubent.Dev.gif\"\n              alt=\"Cubent Editor Interface - Code editing with AI assistance\"\n              width={1200}\n              height={800}\n              className=\"w-full h-auto object-cover rounded-lg\"\n              priority\n              unoptimized\n            />\n            {/* Soft glow effect */}\n            <div className=\"absolute -inset-4 bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-pink-500/20 rounded-2xl blur-xl opacity-30 -z-10\" />\n          </div>\n        </div>\n      </div>\n    </div>\n\n    {/* Made for modern product teams section */}\n    <div className=\"w-full relative px-4 sm:px-6\" style={{ backgroundColor: '#161616' }}>\n      <div\n        className=\"max-w-7xl mx-auto px-6 sm:px-8 lg:px-12 pt-24 lg:pt-36 pb-0 relative\"\n        style={{\n          border: '1px solid rgba(255, 255, 255, 0.08)',\n          borderTop: 'none',\n          backgroundColor: 'transparent'\n        }}\n      >\n\n        {/* Top section - Title on left, description on right */}\n        <div className=\"relative z-10 flex flex-col lg:flex-row gap-10 lg:gap-20 items-start mb-20 lg:mb-24\">\n        {/* Left side - Title */}\n        <div className=\"flex-1 max-w-md\">\n          <h2 className=\"text-4xl lg:text-5xl font-regular tracking-tighter text-white\">\n            An AI that vibes with your code and keeps up with your flow.\n          </h2>\n        </div>\n\n        {/* Right side - Description */}\n        <div className=\"flex-1 max-w-lg\">\n          <p className=\"text-lg text-muted-foreground leading-relaxed\">\n            Cubent transforms how developers work by providing intelligent, context-aware assistance that learns from your codebase. From instant screenshot-to-code conversion to deep architectural understanding, we're building the future of software development.\n          </p>\n        </div>\n      </div>\n\n      {/* Vertical lines section */}\n      <div className=\"relative z-10 mb-20 lg:mb-24 -mx-6 sm:-mx-8 lg:-mx-12\">\n        <div className=\"h-16 lg:h-20 w-full relative overflow-hidden\" style={{ backgroundColor: '#161616' }}>\n          {/* Top horizontal line */}\n          <div\n            className=\"absolute top-0 left-0 right-0 h-px\"\n            style={{ backgroundColor: 'rgba(255,255,255,0.08)' }}\n          />\n          {/* Thin vertical lines pattern - extending to edges */}\n          <div\n            className=\"absolute inset-0\"\n            style={{\n              backgroundImage: 'linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px)',\n              backgroundSize: '8px 100%'\n            }}\n          />\n          {/* Bottom horizontal line */}\n          <div\n            className=\"absolute bottom-0 left-0 right-0 h-px\"\n            style={{ backgroundColor: 'rgba(255,255,255,0.08)' }}\n          />\n        </div>\n      </div>\n\n      {/* Two images section - attached to lined bar bottom */}\n      <div className=\"relative z-10 -mt-20 lg:-mt-24 mb-8 -mx-6 sm:-mx-8 lg:-mx-12\">\n        <div className=\"grid grid-cols-2 gap-0\" style={{ backgroundColor: '#161616' }}>\n          {/* Left image */}\n          <div className=\"relative\">\n            <Image\n              src=\"/images/Cubent (2).png\"\n              alt=\"Stay in sync with a real-time panel that knows your project\"\n              width={800}\n              height={600}\n              className=\"w-full h-auto object-cover border border-gray-600\"\n            />\n          </div>\n          {/* Right image */}\n          <div className=\"relative\">\n            <Image\n              src=\"/images/Cubent (3).png\"\n              alt=\"Sit back while it handles the heavy lifting, step by step\"\n              width={800}\n              height={600}\n              className=\"w-full h-auto object-cover border border-gray-600\"\n            />\n          </div>\n        </div>\n\n        {/* Text content directly below images with more spacing */}\n        <div className=\"grid grid-cols-2 gap-8 px-6 sm:px-8 lg:px-12 mt-16 mb-0 relative\">\n          {/* Center divider line */}\n          <div className=\"absolute left-1/2 top-0 bottom-0 w-px bg-white/10 transform -translate-x-1/2\"></div>\n\n          {/* Left text */}\n          <div className=\"max-w-md mx-auto text-left\">\n            <h3 className=\"text-4xl font-normal text-white/90 mb-6 leading-tight\">\n              Stay in sync with a real-time panel that knows your project\n            </h3>\n            <p className=\"text-gray-400 text-sm leading-relaxed pb-6\">\n              A single overlay that pulses with your repo's heartbeat—files, problems, Git, terminals and folders always a glance away.\n            </p>\n          </div>\n          {/* Right text */}\n          <div className=\"max-w-md mx-auto text-left\">\n            <h3 className=\"text-4xl font-normal text-white/90 mb-6 leading-tight\">\n              Sit back while it handles the heavy lifting, step by step\n            </h3>\n            <p className=\"text-gray-400 text-sm leading-relaxed pb-6\">\n              Let Cubent chain terminal commands, edit files, run tests and open pull requests while you sip your coffee—true hands-free automation.\n            </p>\n          </div>\n        </div>\n\n        {/* Vertical lines section - inside the container with proper limits */}\n        <div className=\"relative z-10 mt-16\">\n          <div className=\"h-16 lg:h-20 w-full relative overflow-hidden\" style={{ backgroundColor: '#161616' }}>\n            {/* Top horizontal line */}\n            <div\n              className=\"absolute top-0 left-0 right-0 h-px\"\n              style={{ backgroundColor: 'rgba(255,255,255,0.08)' }}\n            />\n            {/* Thin vertical lines pattern - extending to edges */}\n            <div\n              className=\"absolute inset-0\"\n              style={{\n                backgroundImage: 'linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px)',\n                backgroundSize: '8px 100%'\n              }}\n            />\n            {/* Bottom horizontal line */}\n            <div\n              className=\"absolute bottom-0 left-0 right-0 h-px\"\n              style={{ backgroundColor: 'rgba(255,255,255,0.08)' }}\n            />\n          </div>\n        </div>\n      </div>\n    </div>\n    </div>\n\n    {/* MCP Tools Section - Bordered box like AI-powered section */}\n    <div className=\"w-full relative px-4 sm:px-6\" style={{ backgroundColor: '#161616' }}>\n      <div\n        className=\"max-w-7xl mx-auto px-6 sm:px-8 lg:px-12 pt-24 lg:pt-36 pb-12 lg:pb-16 relative\"\n        style={{\n          border: '1px solid rgba(255, 255, 255, 0.08)',\n          borderTop: 'none',\n          backgroundColor: 'transparent'\n        }}\n      >\n\n\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-0 items-start\">\n            {/* Left side - Content only */}\n            <div className=\"space-y-8 pr-10 lg:pr-20 py-4 -mt-8\">\n              {/* MCP Tools label */}\n              <div className=\"text-white/60 text-sm font-medium tracking-wider\">\n                — MCP Tools\n              </div>\n\n              {/* Main heading */}\n              <div className=\"space-y-4\">\n                <h2 className=\"text-4xl lg:text-5xl font-regular tracking-tight text-white leading-tight\">\n                  Integrate with the tools you already use\n                </h2>\n                <p className=\"text-white/70 text-lg leading-relaxed max-w-md\">\n                  Cubent brings together your essential apps with MCP from GitHub to Notion — into one powerful interface. Discover, connect, and explore your data like never before.\n                </p>\n              </div>\n            </div>\n\n            {/* Right side - Square image touching top and right borders */}\n            <div className=\"hidden lg:block relative h-full -mr-6 sm:-mr-8 lg:-mr-12\">\n              <div className=\"aspect-square w-full absolute -top-24 lg:-top-36 right-0\">\n                <img\n                  src=\"/images/Cubent.Dev (25).png\"\n                  alt=\"Cubent MCP Tools interface\"\n                  className=\"w-full h-full object-cover\"\n                />\n              </div>\n            </div>\n          </div>\n\n        {/* Vertical lines section - boxed within bordered container */}\n        <div className=\"relative z-10 mt-32 lg:mt-36 -mx-6 sm:-mx-8 lg:-mx-12\">\n          <div className=\"h-16 lg:h-20 w-full relative overflow-hidden\" style={{ backgroundColor: '#161616' }}>\n            {/* Top horizontal line */}\n            <div\n              className=\"absolute top-0 left-0 right-0 h-px\"\n              style={{ backgroundColor: 'rgba(255,255,255,0.08)' }}\n            />\n            {/* Thin vertical lines pattern - extending to edges */}\n            <div\n              className=\"absolute inset-0\"\n              style={{\n                backgroundImage: 'linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px)',\n                backgroundSize: '8px 100%'\n              }}\n            />\n            {/* Bottom horizontal line */}\n            <div\n              className=\"absolute bottom-0 left-0 right-0 h-px\"\n              style={{ backgroundColor: 'rgba(255,255,255,0.08)' }}\n            />\n          </div>\n        </div>\n      </div>\n    </div>\n\n    {/* Speed with context awareness section */}\n    <div className=\"w-full relative px-4 sm:px-6\" style={{ backgroundColor: '#161616' }}>\n      <div\n        className=\"max-w-7xl mx-auto px-6 sm:px-8 lg:px-12 py-16 lg:py-20 relative\"\n        style={{\n          border: '1px solid rgba(255, 255, 255, 0.08)',\n          borderTop: 'none',\n          backgroundColor: 'transparent'\n        }}\n      >\n        <div className=\"flex flex-col lg:flex-row items-center justify-between gap-8 mb-16 lg:mb-20\">\n          {/* Left side - Title and description */}\n          <div className=\"flex-1\">\n            <h2 className=\"text-4xl lg:text-5xl font-regular tracking-tight text-white leading-tight mb-4\">\n              Speed with context awareness\n            </h2>\n            <p className=\"text-white/70 text-lg leading-relaxed max-w-lg\">\n              With a Time-to-First-Audio of 40ms, Sonic is the fastest generative voice model built for streaming.\n            </p>\n          </div>\n\n          {/* Right side - Percentage */}\n          <div className=\"flex-shrink-0 text-right\">\n            <div\n              className=\"text-6xl lg:text-7xl font-normal leading-none\"\n              style={{\n                background: 'linear-gradient(135deg, #ff8c00, #ff4500)',\n                WebkitBackgroundClip: 'text',\n                WebkitTextFillColor: 'transparent',\n                backgroundClip: 'text'\n              }}\n            >\n              *145%\n            </div>\n            <p className=\"text-white/60 text-sm mt-2\">\n              faster than the next best competitor\n            </p>\n          </div>\n        </div>\n\n        {/* Image below the content */}\n        <div className=\"w-full flex justify-center mb-8 lg:mb-10\">\n          <img\n            src=\"/images/Cubent.Dev (30).png\"\n            alt=\"Cubent speed demonstration\"\n            className=\"w-full max-w-6xl h-auto object-contain\"\n          />\n        </div>\n\n        {/* Vertical lines section - inside the same container */}\n        <div className=\"relative z-10 -mx-6 sm:-mx-8 lg:-mx-12\">\n          <div className=\"h-16 lg:h-20 w-full relative overflow-hidden\" style={{ backgroundColor: '#161616' }}>\n            {/* Top horizontal line */}\n            <div\n              className=\"absolute top-0 left-0 right-0 h-px\"\n              style={{ backgroundColor: 'rgba(255,255,255,0.08)' }}\n            />\n            {/* Thin vertical lines pattern - extending to edges */}\n            <div\n              className=\"absolute inset-0\"\n              style={{\n                backgroundImage: 'linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px)',\n                backgroundSize: '8px 100%'\n              }}\n            />\n            {/* Bottom horizontal line */}\n            <div\n              className=\"absolute bottom-0 left-0 right-0 h-px\"\n              style={{ backgroundColor: 'rgba(255,255,255,0.08)' }}\n            />\n          </div>\n        </div>\n      </div>\n    </div>\n\n    {/* Duplicated MCP Tools Section - Image on left, content on right */}\n    <div className=\"w-full relative px-4 sm:px-6\" style={{ backgroundColor: '#161616' }}>\n      <div\n        className=\"max-w-7xl mx-auto px-6 sm:px-8 lg:px-12 py-24 lg:py-36 pb-16 lg:pb-20 relative\"\n        style={{\n          border: '1px solid rgba(255, 255, 255, 0.08)',\n          borderTop: 'none',\n          backgroundColor: 'transparent'\n        }}\n      >\n\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-0 items-start\">\n            {/* Left side - Square image touching top and left borders */}\n            <div className=\"hidden lg:block relative h-full -ml-6 sm:-ml-8 lg:-ml-12\">\n              <div className=\"aspect-square w-full absolute -top-24 lg:-top-36 left-0\">\n                <img\n                  src=\"/images/Cubent.Dev (29).png\"\n                  alt=\"Cubent Autocomplete interface\"\n                  className=\"w-full h-full object-cover\"\n                />\n              </div>\n            </div>\n\n            {/* Right side - Content only */}\n            <div className=\"space-y-8 pl-10 lg:pl-20 py-4 -mt-8\">\n              {/* Autocomplete label */}\n              <div className=\"text-white/60 text-sm font-medium tracking-wider\">\n                — Autocomplete\n              </div>\n\n              {/* Main heading */}\n              <div className=\"space-y-4\">\n                <h2 className=\"text-4xl lg:text-5xl font-regular tracking-tight text-white leading-tight\">\n                  Code smarter with context-aware autocomplete\n                </h2>\n                <p className=\"text-white/70 text-lg leading-relaxed max-w-md\">\n                  Experience intelligent code completion that understands your project context. Cubent's autocomplete adapts to your coding patterns and suggests the most relevant completions — just start typing, and let the magic happen.\n                </p>\n              </div>\n            </div>\n          </div>\n\n        {/* Vertical lines section - boxed within bordered container */}\n        <div className=\"relative z-10 mt-16 lg:mt-20 -mx-6 sm:-mx-8 lg:-mx-12\">\n          <div className=\"h-16 lg:h-20 w-full relative overflow-hidden\" style={{ backgroundColor: '#161616' }}>\n            {/* Top horizontal line */}\n            <div\n              className=\"absolute top-0 left-0 right-0 h-px\"\n              style={{ backgroundColor: 'rgba(255,255,255,0.08)' }}\n            />\n            {/* Thin vertical lines pattern - extending to edges */}\n            <div\n              className=\"absolute inset-0\"\n              style={{\n                backgroundImage: 'linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px)',\n                backgroundSize: '8px 100%'\n              }}\n            />\n            {/* Bottom horizontal line */}\n            <div\n              className=\"absolute bottom-0 left-0 right-0 h-px\"\n              style={{ backgroundColor: 'rgba(255,255,255,0.08)' }}\n            />\n          </div>\n        </div>\n      </div>\n    </div>\n\n    {/* Modal */}\n    {selectedFeature && (\n      <div className=\"fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4\">\n        <div className=\"bg-[#1a1a1a] rounded-3xl max-w-4xl w-full max-h-[90vh] overflow-hidden\">\n          {/* Modal Header */}\n          <div className=\"relative\">\n            <button\n              onClick={() => setSelectedFeature(null)}\n              className=\"absolute top-6 right-6 z-10 w-10 h-10 bg-black/20 hover:bg-black/40 rounded-full flex items-center justify-center text-white/70 hover:text-white transition-all duration-200\"\n            >\n              <X size={20} />\n            </button>\n\n            {/* Feature Image */}\n            <div className=\"relative h-80 w-full overflow-hidden\">\n              <Image\n                src={selectedFeature.image}\n                alt={selectedFeature.alt}\n                fill\n                className=\"object-cover\"\n              />\n              <div className=\"absolute inset-0 bg-gradient-to-t from-[#1a1a1a] via-transparent to-transparent\" />\n            </div>\n          </div>\n\n          {/* Modal Content */}\n          <div className=\"p-8 max-h-[50vh] overflow-y-auto\">\n            <h2 className=\"text-3xl font-bold text-white mb-6\">\n              {selectedFeature.title}\n            </h2>\n\n            <p className=\"text-gray-300 text-lg mb-8 leading-relaxed\">\n              {selectedFeature.description}\n            </p>\n\n            <div className=\"space-y-6\">\n              {selectedFeature.content.map((paragraph, index) => (\n                <p key={index} className=\"text-gray-300 leading-relaxed text-base\">\n                  {paragraph}\n                </p>\n              ))}\n            </div>\n\n            <div className=\"mt-8 pt-6 border-t border-white/10\">\n              <p className=\"text-gray-400 text-sm leading-relaxed\">\n                Experience the power of AI-driven development with Cubent's advanced features designed to accelerate your workflow and improve code quality. Join thousands of developers who have already transformed their development process with intelligent, context-aware coding assistance.\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n    )}\n  </>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMA,eAAe;AACf,MAAM,WAAW;IACf;QACE,IAAI;QACJ,OAAO;QACP,OAAO;QACP,KAAK;QACL,aAAa;QACb,SAAS;YACP;YAEA;YAEA;SACD;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,OAAO;QACP,KAAK;QACL,aAAa;QACb,SAAS;YACP;YAEA;YAEA;SACD;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,OAAO;QACP,KAAK;QACL,aAAa;QACb,SAAS;YACP;YAEA;YAEA;SACD;IACH;CACD;AAEM,MAAM,SAAS;;IACpB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAA6B;IAElF,qBACA;;0BACE,4SAAC;gBAAI,WAAU;gBAA+B,OAAO;oBAAE,iBAAiB;gBAAU;0BAChF,cAAA,4SAAC;oBAAI,WAAU;8BACb,cAAA,4SAAC;wBAAI,WAAU;kCACb,cAAA,4SAAC;4BAAI,WAAU;;8CACb,4SAAC,+OAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,OAAO;oCACP,QAAQ;oCACR,WAAU;oCACV,QAAQ;oCACR,WAAW;;;;;;8CAGb,4SAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOvB,4SAAC;gBAAI,WAAU;gBAA+B,OAAO;oBAAE,iBAAiB;gBAAU;0BAChF,cAAA,4SAAC;oBACC,WAAU;oBACV,OAAO;wBACL,QAAQ;wBACR,WAAW;wBACX,iBAAiB;oBACnB;;sCAIA,4SAAC;4BAAI,WAAU;;8CAEf,4SAAC;oCAAI,WAAU;8CACb,cAAA,4SAAC;wCAAG,WAAU;kDAAgE;;;;;;;;;;;8CAMhF,4SAAC;oCAAI,WAAU;8CACb,cAAA,4SAAC;wCAAE,WAAU;kDAAgD;;;;;;;;;;;;;;;;;sCAOjE,4SAAC;4BAAI,WAAU;sCACb,cAAA,4SAAC;gCAAI,WAAU;gCAA+C,OAAO;oCAAE,iBAAiB;gCAAU;;kDAEhG,4SAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,iBAAiB;wCAAyB;;;;;;kDAGrD,4SAAC;wCACC,WAAU;wCACV,OAAO;4CACL,iBAAiB;4CACjB,gBAAgB;wCAClB;;;;;;kDAGF,4SAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,iBAAiB;wCAAyB;;;;;;;;;;;;;;;;;sCAMzD,4SAAC;4BAAI,WAAU;;8CACb,4SAAC;oCAAI,WAAU;oCAAyB,OAAO;wCAAE,iBAAiB;oCAAU;;sDAE1E,4SAAC;4CAAI,WAAU;sDACb,cAAA,4SAAC,+OAAA,CAAA,UAAK;gDACJ,KAAI;gDACJ,KAAI;gDACJ,OAAO;gDACP,QAAQ;gDACR,WAAU;;;;;;;;;;;sDAId,4SAAC;4CAAI,WAAU;sDACb,cAAA,4SAAC,+OAAA,CAAA,UAAK;gDACJ,KAAI;gDACJ,KAAI;gDACJ,OAAO;gDACP,QAAQ;gDACR,WAAU;;;;;;;;;;;;;;;;;8CAMhB,4SAAC;oCAAI,WAAU;;sDAEb,4SAAC;4CAAI,WAAU;;;;;;sDAGf,4SAAC;4CAAI,WAAU;;8DACb,4SAAC;oDAAG,WAAU;8DAAwD;;;;;;8DAGtE,4SAAC;oDAAE,WAAU;8DAA6C;;;;;;;;;;;;sDAK5D,4SAAC;4CAAI,WAAU;;8DACb,4SAAC;oDAAG,WAAU;8DAAwD;;;;;;8DAGtE,4SAAC;oDAAE,WAAU;8DAA6C;;;;;;;;;;;;;;;;;;8CAO9D,4SAAC;oCAAI,WAAU;8CACb,cAAA,4SAAC;wCAAI,WAAU;wCAA+C,OAAO;4CAAE,iBAAiB;wCAAU;;0DAEhG,4SAAC;gDACC,WAAU;gDACV,OAAO;oDAAE,iBAAiB;gDAAyB;;;;;;0DAGrD,4SAAC;gDACC,WAAU;gDACV,OAAO;oDACL,iBAAiB;oDACjB,gBAAgB;gDAClB;;;;;;0DAGF,4SAAC;gDACC,WAAU;gDACV,OAAO;oDAAE,iBAAiB;gDAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS7D,4SAAC;gBAAI,WAAU;gBAA+B,OAAO;oBAAE,iBAAiB;gBAAU;0BAChF,cAAA,4SAAC;oBACC,WAAU;oBACV,OAAO;wBACL,QAAQ;wBACR,WAAW;wBACX,iBAAiB;oBACnB;;sCAIE,4SAAC;4BAAI,WAAU;;8CAEb,4SAAC;oCAAI,WAAU;;sDAEb,4SAAC;4CAAI,WAAU;sDAAmD;;;;;;sDAKlE,4SAAC;4CAAI,WAAU;;8DACb,4SAAC;oDAAG,WAAU;8DAA4E;;;;;;8DAG1F,4SAAC;oDAAE,WAAU;8DAAiD;;;;;;;;;;;;;;;;;;8CAOlE,4SAAC;oCAAI,WAAU;8CACb,cAAA,4SAAC;wCAAI,WAAU;kDACb,cAAA,4SAAC;4CACC,KAAI;4CACJ,KAAI;4CACJ,WAAU;;;;;;;;;;;;;;;;;;;;;;sCAOpB,4SAAC;4BAAI,WAAU;sCACb,cAAA,4SAAC;gCAAI,WAAU;gCAA+C,OAAO;oCAAE,iBAAiB;gCAAU;;kDAEhG,4SAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,iBAAiB;wCAAyB;;;;;;kDAGrD,4SAAC;wCACC,WAAU;wCACV,OAAO;4CACL,iBAAiB;4CACjB,gBAAgB;wCAClB;;;;;;kDAGF,4SAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,iBAAiB;wCAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ7D,4SAAC;gBAAI,WAAU;gBAA+B,OAAO;oBAAE,iBAAiB;gBAAU;0BAChF,cAAA,4SAAC;oBACC,WAAU;oBACV,OAAO;wBACL,QAAQ;wBACR,WAAW;wBACX,iBAAiB;oBACnB;;sCAEA,4SAAC;4BAAI,WAAU;;8CAEb,4SAAC;oCAAI,WAAU;;sDACb,4SAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAG/F,4SAAC;4CAAE,WAAU;sDAAiD;;;;;;;;;;;;8CAMhE,4SAAC;oCAAI,WAAU;;sDACb,4SAAC;4CACC,WAAU;4CACV,OAAO;gDACL,YAAY;gDACZ,sBAAsB;gDACtB,qBAAqB;gDACrB,gBAAgB;4CAClB;sDACD;;;;;;sDAGD,4SAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;;;;;;;sCAO9C,4SAAC;4BAAI,WAAU;sCACb,cAAA,4SAAC;gCACC,KAAI;gCACJ,KAAI;gCACJ,WAAU;;;;;;;;;;;sCAKd,4SAAC;4BAAI,WAAU;sCACb,cAAA,4SAAC;gCAAI,WAAU;gCAA+C,OAAO;oCAAE,iBAAiB;gCAAU;;kDAEhG,4SAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,iBAAiB;wCAAyB;;;;;;kDAGrD,4SAAC;wCACC,WAAU;wCACV,OAAO;4CACL,iBAAiB;4CACjB,gBAAgB;wCAClB;;;;;;kDAGF,4SAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,iBAAiB;wCAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ7D,4SAAC;gBAAI,WAAU;gBAA+B,OAAO;oBAAE,iBAAiB;gBAAU;0BAChF,cAAA,4SAAC;oBACC,WAAU;oBACV,OAAO;wBACL,QAAQ;wBACR,WAAW;wBACX,iBAAiB;oBACnB;;sCAGE,4SAAC;4BAAI,WAAU;;8CAEb,4SAAC;oCAAI,WAAU;8CACb,cAAA,4SAAC;wCAAI,WAAU;kDACb,cAAA,4SAAC;4CACC,KAAI;4CACJ,KAAI;4CACJ,WAAU;;;;;;;;;;;;;;;;8CAMhB,4SAAC;oCAAI,WAAU;;sDAEb,4SAAC;4CAAI,WAAU;sDAAmD;;;;;;sDAKlE,4SAAC;4CAAI,WAAU;;8DACb,4SAAC;oDAAG,WAAU;8DAA4E;;;;;;8DAG1F,4SAAC;oDAAE,WAAU;8DAAiD;;;;;;;;;;;;;;;;;;;;;;;;sCAQtE,4SAAC;4BAAI,WAAU;sCACb,cAAA,4SAAC;gCAAI,WAAU;gCAA+C,OAAO;oCAAE,iBAAiB;gCAAU;;kDAEhG,4SAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,iBAAiB;wCAAyB;;;;;;kDAGrD,4SAAC;wCACC,WAAU;wCACV,OAAO;4CACL,iBAAiB;4CACjB,gBAAgB;wCAClB;;;;;;kDAGF,4SAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,iBAAiB;wCAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQ5D,iCACC,4SAAC;gBAAI,WAAU;0BACb,cAAA,4SAAC;oBAAI,WAAU;;sCAEb,4SAAC;4BAAI,WAAU;;8CACb,4SAAC;oCACC,SAAS,IAAM,mBAAmB;oCAClC,WAAU;8CAEV,cAAA,4SAAC,mRAAA,CAAA,IAAC;wCAAC,MAAM;;;;;;;;;;;8CAIX,4SAAC;oCAAI,WAAU;;sDACb,4SAAC,+OAAA,CAAA,UAAK;4CACJ,KAAK,gBAAgB,KAAK;4CAC1B,KAAK,gBAAgB,GAAG;4CACxB,IAAI;4CACJ,WAAU;;;;;;sDAEZ,4SAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;;sCAKnB,4SAAC;4BAAI,WAAU;;8CACb,4SAAC;oCAAG,WAAU;8CACX,gBAAgB,KAAK;;;;;;8CAGxB,4SAAC;oCAAE,WAAU;8CACV,gBAAgB,WAAW;;;;;;8CAG9B,4SAAC;oCAAI,WAAU;8CACZ,gBAAgB,OAAO,CAAC,GAAG,CAAC,CAAC,WAAW,sBACvC,4SAAC;4CAAc,WAAU;sDACtB;2CADK;;;;;;;;;;8CAMZ,4SAAC;oCAAI,WAAU;8CACb,cAAA,4SAAC;wCAAE,WAAU;kDAAwC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUnE;GApaa;KAAA", "debugId": null}}]}