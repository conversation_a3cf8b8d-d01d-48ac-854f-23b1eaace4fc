{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/cms/.basehub/next-toolbar/chunk-YSQDPG26.js"], "sourcesContent": ["// This file was generated by basehub. Do not edit directly. Read more: https://basehub.com/docs/api-reference/basehub-sdk\n\n/* eslint-disable */\n/* eslint-disable eslint-comments/no-restricted-disable */\n/* tslint:disable */\n\nvar __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __commonJS = (cb, mod) => function __require() {\n  return mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = { exports: {} }).exports, mod), mod.exports;\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\n\nexport {\n  __commonJS,\n  __toESM\n};\n"], "names": [], "mappings": "AAAA,0HAA0H;AAE1H,kBAAkB,GAClB,wDAAwD,GACxD,kBAAkB;;;;AAElB,IAAI,WAAW,OAAO,MAAM;AAC5B,IAAI,YAAY,OAAO,cAAc;AACrC,IAAI,mBAAmB,OAAO,wBAAwB;AACtD,IAAI,oBAAoB,OAAO,mBAAmB;AAClD,IAAI,eAAe,OAAO,cAAc;AACxC,IAAI,eAAe,OAAO,SAAS,CAAC,cAAc;AAClD,IAAI,aAAa,CAAC,IAAI,MAAQ,SAAS;QACrC,OAAO,OAAO,CAAC,GAAG,EAAE,CAAC,kBAAkB,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,MAAM;YAAE,SAAS,CAAC;QAAE,CAAC,EAAE,OAAO,EAAE,MAAM,IAAI,OAAO;IACpG;AACA,IAAI,cAAc,CAAC,IAAI,MAAM,QAAQ;IACnC,IAAI,QAAQ,OAAO,SAAS,YAAY,OAAO,SAAS,YAAY;QAClE,KAAK,IAAI,OAAO,kBAAkB,MAChC,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,QAAQ,QAAQ,QACzC,UAAU,IAAI,KAAK;YAAE,KAAK,IAAM,IAAI,CAAC,IAAI;YAAE,YAAY,CAAC,CAAC,OAAO,iBAAiB,MAAM,IAAI,KAAK,KAAK,UAAU;QAAC;IACtH;IACA,OAAO;AACT;AACA,IAAI,UAAU,CAAC,KAAK,YAAY,SAAW,CAAC,SAAS,OAAO,OAAO,SAAS,aAAa,QAAQ,CAAC,GAAG,YACnG,sEAAsE;IACtE,iEAAiE;IACjE,sEAAsE;IACtE,qEAAqE;IACrE,cAAc,CAAC,OAAO,CAAC,IAAI,UAAU,GAAG,UAAU,QAAQ,WAAW;QAAE,OAAO;QAAK,YAAY;IAAK,KAAK,QACzG,IACD", "debugId": null}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/cms/.basehub/runtime/_error.ts"], "sourcesContent": ["// This file was generated by basehub. Do not edit directly. Read more: https://basehub.com/docs/api-reference/basehub-sdk\n\n/* eslint-disable */\n/* eslint-disable eslint-comments/no-restricted-disable */\n/* tslint:disable */\n\n// @ts-nocheck\nexport class GenqlError extends Error {\n    errors: Array<GraphqlError> = []\n    /**\n     * Partial data returned by the server\n     */\n    data?: any\n    constructor(errors: any[], data: any) {\n        let message = Array.isArray(errors)\n            ? errors.map((x) => x?.message || '').join('\\n')\n            : ''\n        if (!message) {\n            message = 'GraphQL error'\n        }\n        super(message)\n        this.errors = errors\n        this.data = data\n    }\n}\n\ninterface GraphqlError {\n    message: string\n    locations?: Array<{\n        line: number\n        column: number\n    }>\n    path?: string[]\n    extensions?: Record<string, any>\n}\n"], "names": [], "mappings": "AAAA,0HAA0H;AAE1H,kBAAkB,GAClB,wDAAwD,GACxD,kBAAkB,GAElB,cAAc;;;;AACP,MAAM,mBAAmB;IAC5B,SAA8B,EAAE,CAAA;IAChC;;KAEC,GACD,KAAU;IACV,YAAY,MAAa,EAAE,IAAS,CAAE;QAClC,IAAI,UAAU,MAAM,OAAO,CAAC,UACtB,OAAO,GAAG,CAAC,CAAC,IAAM,GAAG,WAAW,IAAI,IAAI,CAAC,QACzC;QACN,IAAI,CAAC,SAAS;YACV,UAAU;QACd;QACA,KAAK,CAAC;QACN,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,IAAI,GAAG;IAChB;AACJ", "debugId": null}}, {"offset": {"line": 73, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/cms/.basehub/runtime/_batcher.ts"], "sourcesContent": ["// This file was generated by basehub. Do not edit directly. Read more: https://basehub.com/docs/api-reference/basehub-sdk\n\n/* eslint-disable */\n/* eslint-disable eslint-comments/no-restricted-disable */\n/* tslint:disable */\n\n// @ts-nocheck\nimport type { GraphqlOperation } from './_generate-graphql-operation'\nimport { GenqlError } from './_error'\n\ntype Variables = Record<string, any>\n\ntype QueryError = Error & {\n    message: string\n\n    locations?: Array<{\n        line: number\n        column: number\n    }>\n    path?: any\n    rid: string\n    details?: Record<string, any>\n}\ntype Result = {\n    data: Record<string, any>\n    errors: Array<QueryError>\n}\ntype Fetcher = (\n    batchedQuery: GraphqlOperation | Array<GraphqlOperation>,\n    extraFetchOptions?: Partial<RequestInit>,\n) => Promise<Array<Result>>\ntype Options = {\n    batchInterval?: number\n    shouldBatch?: boolean\n    maxBatchSize?: number\n}\ntype Queue = Array<{\n    request: GraphqlOperation\n    resolve: (...args: Array<any>) => any\n    reject: (...args: Array<any>) => any\n}>\n\n/**\n * takes a list of requests (queue) and batches them into a single server request.\n * It will then resolve each individual requests promise with the appropriate data.\n * @private\n * @param {QueryBatcher}   client - the client to use\n * @param {Queue} queue  - the list of requests to batch\n */\nfunction dispatchQueueBatch(client: QueryBatcher, queue: Queue): void {\n    let batchedQuery: any = queue.map((item) => item.request)\n\n    if (batchedQuery.length === 1) {\n        batchedQuery = batchedQuery[0]\n    }\n    ;(() => {\n        try {\n            return client.fetcher(batchedQuery)\n        } catch (e) {\n            return Promise.reject(e)\n        }\n    })()\n        .then((responses: any) => {\n            if (queue.length === 1 && !Array.isArray(responses)) {\n                if (responses.errors && responses.errors.length) {\n                    queue[0].reject(\n                        new GenqlError(responses.errors, responses.data),\n                    )\n                    return\n                }\n\n                queue[0].resolve(responses)\n                return\n            } else if (responses.length !== queue.length) {\n                throw new Error('response length did not match query length')\n            }\n\n            for (let i = 0; i < queue.length; i++) {\n                if (responses[i].errors && responses[i].errors.length) {\n                    queue[i].reject(\n                        new GenqlError(responses[i].errors, responses[i].data),\n                    )\n                } else {\n                    queue[i].resolve(responses[i])\n                }\n            }\n        })\n        .catch((e) => {\n            for (let i = 0; i < queue.length; i++) {\n                queue[i].reject(e)\n            }\n        })\n}\n\n/**\n * creates a list of requests to batch according to max batch size.\n * @private\n * @param {QueryBatcher} client - the client to create list of requests from from\n * @param {Options} options - the options for the batch\n */\nfunction dispatchQueue(client: QueryBatcher, options: Options): void {\n    const queue = client._queue\n    const maxBatchSize = options.maxBatchSize || 0\n    client._queue = []\n\n    if (maxBatchSize > 0 && maxBatchSize < queue.length) {\n        for (let i = 0; i < queue.length / maxBatchSize; i++) {\n            dispatchQueueBatch(\n                client,\n                queue.slice(i * maxBatchSize, (i + 1) * maxBatchSize),\n            )\n        }\n    } else {\n        dispatchQueueBatch(client, queue)\n    }\n}\n/**\n * Create a batcher client.\n * @param {Fetcher} fetcher                 - A function that can handle the network requests to graphql endpoint\n * @param {Options} options                 - the options to be used by client\n * @param {boolean} options.shouldBatch     - should the client batch requests. (default true)\n * @param {integer} options.batchInterval   - duration (in MS) of each batch window. (default 6)\n * @param {integer} options.maxBatchSize    - max number of requests in a batch. (default 0)\n * @param {boolean} options.defaultHeaders  - default headers to include with every request\n *\n * @example\n * const fetcher = batchedQuery => fetch('path/to/graphql', {\n *    method: 'post',\n *    headers: {\n *      Accept: 'application/json',\n *      'Content-Type': 'application/json',\n *    },\n *    body: JSON.stringify(batchedQuery),\n *    credentials: 'include',\n * })\n * .then(response => response.json())\n *\n * const client = new QueryBatcher(fetcher, { maxBatchSize: 10 })\n */\n\nexport class QueryBatcher {\n    fetcher: Fetcher\n    _options: Options\n    _queue: Queue\n\n    constructor(\n        fetcher: Fetcher,\n        {\n            batchInterval = 16,\n            shouldBatch = true,\n            maxBatchSize = 0,\n        }: Options = {},\n    ) {\n        this.fetcher = fetcher\n        this._options = {\n            batchInterval,\n            shouldBatch,\n            maxBatchSize,\n        }\n        this._queue = []\n    }\n\n    /**\n     * Fetch will send a graphql request and return the parsed json.\n     * @param {string}      query          - the graphql query.\n     * @param {Variables}   variables      - any variables you wish to inject as key/value pairs.\n     * @param {[string]}    operationName  - the graphql operationName.\n     * @param {Options}     overrides      - the client options overrides.\n     *\n     * @return {promise} resolves to parsed json of server response\n     *\n     * @example\n     * client.fetch(`\n     *    query getHuman($id: ID!) {\n     *      human(id: $id) {\n     *        name\n     *        height\n     *      }\n     *    }\n     * `, { id: \"1001\" }, 'getHuman')\n     *    .then(human => {\n     *      // do something with human\n     *      console.log(human);\n     *    });\n     */\n    fetch(\n        query: string,\n        variables?: Variables,\n        operationName?: string,\n        overrides: Options = {},\n    ): Promise<Result> {\n        const request: GraphqlOperation = {\n            query,\n        }\n        const options = Object.assign({}, this._options, overrides)\n\n        if (variables) {\n            request.variables = variables\n        }\n\n        if (operationName) {\n            request.operationName = operationName\n        }\n\n        const promise = new Promise<Result>((resolve, reject) => {\n            this._queue.push({\n                request,\n                resolve,\n                reject,\n            })\n\n            if (this._queue.length === 1) {\n                if (options.shouldBatch) {\n                    setTimeout(\n                        () => dispatchQueue(this, options),\n                        options.batchInterval,\n                    )\n                } else {\n                    dispatchQueue(this, options)\n                }\n            }\n        })\n        return promise\n    }\n\n    /**\n     * Fetch will send a graphql request and return the parsed json.\n     * @param {string}      query          - the graphql query.\n     * @param {Variables}   variables      - any variables you wish to inject as key/value pairs.\n     * @param {[string]}    operationName  - the graphql operationName.\n     * @param {Options}     overrides      - the client options overrides.\n     *\n     * @return {Promise<Array<Result>>} resolves to parsed json of server response\n     *\n     * @example\n     * client.forceFetch(`\n     *    query getHuman($id: ID!) {\n     *      human(id: $id) {\n     *        name\n     *        height\n     *      }\n     *    }\n     * `, { id: \"1001\" }, 'getHuman')\n     *    .then(human => {\n     *      // do something with human\n     *      console.log(human);\n     *    });\n     */\n    forceFetch(\n        query: string,\n        variables?: Variables,\n        operationName?: string,\n        overrides: Options = {},\n    ): Promise<Result> {\n        const request: GraphqlOperation = {\n            query,\n        }\n        const options = Object.assign({}, this._options, overrides, {\n            shouldBatch: false,\n        })\n\n        if (variables) {\n            request.variables = variables\n        }\n\n        if (operationName) {\n            request.operationName = operationName\n        }\n\n        const promise = new Promise<Result>((resolve, reject) => {\n            const client = new QueryBatcher(this.fetcher, this._options)\n            client._queue = [\n                {\n                    request,\n                    resolve,\n                    reject,\n                },\n            ]\n            dispatchQueue(client, options)\n        })\n        return promise\n    }\n}\n"], "names": [], "mappings": "AAAA,0HAA0H;AAE1H,kBAAkB,GAClB,wDAAwD,GACxD,kBAAkB,GAElB,cAAc;;;;AAEd;;AAkCA;;;;;;CAMC,GACD,SAAS,mBAAmB,MAAoB,EAAE,KAAY;IAC1D,IAAI,eAAoB,MAAM,GAAG,CAAC,CAAC,OAAS,KAAK,OAAO;IAExD,IAAI,aAAa,MAAM,KAAK,GAAG;QAC3B,eAAe,YAAY,CAAC,EAAE;IAClC;;IACC,CAAC;QACE,IAAI;YACA,OAAO,OAAO,OAAO,CAAC;QAC1B,EAAE,OAAO,GAAG;YACR,OAAO,QAAQ,MAAM,CAAC;QAC1B;IACJ,CAAC,IACI,IAAI,CAAC,CAAC;QACH,IAAI,MAAM,MAAM,KAAK,KAAK,CAAC,MAAM,OAAO,CAAC,YAAY;YACjD,IAAI,UAAU,MAAM,IAAI,UAAU,MAAM,CAAC,MAAM,EAAE;gBAC7C,KAAK,CAAC,EAAE,CAAC,MAAM,CACX,IAAI,iJAAA,CAAA,aAAU,CAAC,UAAU,MAAM,EAAE,UAAU,IAAI;gBAEnD;YACJ;YAEA,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC;YACjB;QACJ,OAAO,IAAI,UAAU,MAAM,KAAK,MAAM,MAAM,EAAE;YAC1C,MAAM,IAAI,MAAM;QACpB;QAEA,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YACnC,IAAI,SAAS,CAAC,EAAE,CAAC,MAAM,IAAI,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,MAAM,EAAE;gBACnD,KAAK,CAAC,EAAE,CAAC,MAAM,CACX,IAAI,iJAAA,CAAA,aAAU,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,EAAE,SAAS,CAAC,EAAE,CAAC,IAAI;YAE7D,OAAO;gBACH,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;YACjC;QACJ;IACJ,GACC,KAAK,CAAC,CAAC;QACJ,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YACnC,KAAK,CAAC,EAAE,CAAC,MAAM,CAAC;QACpB;IACJ;AACR;AAEA;;;;;CAKC,GACD,SAAS,cAAc,MAAoB,EAAE,OAAgB;IACzD,MAAM,QAAQ,OAAO,MAAM;IAC3B,MAAM,eAAe,QAAQ,YAAY,IAAI;IAC7C,OAAO,MAAM,GAAG,EAAE;IAElB,IAAI,eAAe,KAAK,eAAe,MAAM,MAAM,EAAE;QACjD,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,GAAG,cAAc,IAAK;YAClD,mBACI,QACA,MAAM,KAAK,CAAC,IAAI,cAAc,CAAC,IAAI,CAAC,IAAI;QAEhD;IACJ,OAAO;QACH,mBAAmB,QAAQ;IAC/B;AACJ;AAyBO,MAAM;IACT,QAAgB;IAChB,SAAiB;IACjB,OAAa;IAEb,YACI,OAAgB,EAChB,EACI,gBAAgB,EAAE,EAClB,cAAc,IAAI,EAClB,eAAe,CAAC,EACV,GAAG,CAAC,CAAC,CACjB;QACE,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,QAAQ,GAAG;YACZ;YACA;YACA;QACJ;QACA,IAAI,CAAC,MAAM,GAAG,EAAE;IACpB;IAEA;;;;;;;;;;;;;;;;;;;;;;KAsBC,GACD,MACI,KAAa,EACb,SAAqB,EACrB,aAAsB,EACtB,YAAqB,CAAC,CAAC,EACR;QACf,MAAM,UAA4B;YAC9B;QACJ;QACA,MAAM,UAAU,OAAO,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,EAAE;QAEjD,IAAI,WAAW;YACX,QAAQ,SAAS,GAAG;QACxB;QAEA,IAAI,eAAe;YACf,QAAQ,aAAa,GAAG;QAC5B;QAEA,MAAM,UAAU,IAAI,QAAgB,CAAC,SAAS;YAC1C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;gBACb;gBACA;gBACA;YACJ;YAEA,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,GAAG;gBAC1B,IAAI,QAAQ,WAAW,EAAE;oBACrB,WACI,IAAM,cAAc,IAAI,EAAE,UAC1B,QAAQ,aAAa;gBAE7B,OAAO;oBACH,cAAc,IAAI,EAAE;gBACxB;YACJ;QACJ;QACA,OAAO;IACX;IAEA;;;;;;;;;;;;;;;;;;;;;;KAsBC,GACD,WACI,KAAa,EACb,SAAqB,EACrB,aAAsB,EACtB,YAAqB,CAAC,CAAC,EACR;QACf,MAAM,UAA4B;YAC9B;QACJ;QACA,MAAM,UAAU,OAAO,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,EAAE,WAAW;YACxD,aAAa;QACjB;QAEA,IAAI,WAAW;YACX,QAAQ,SAAS,GAAG;QACxB;QAEA,IAAI,eAAe;YACf,QAAQ,aAAa,GAAG;QAC5B;QAEA,MAAM,UAAU,IAAI,QAAgB,CAAC,SAAS;YAC1C,MAAM,SAAS,IAAI,aAAa,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ;YAC3D,OAAO,MAAM,GAAG;gBACZ;oBACI;oBACA;oBACA;gBACJ;aACH;YACD,cAAc,QAAQ;QAC1B;QACA,OAAO;IACX;AACJ", "debugId": null}}, {"offset": {"line": 256, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/cms/.basehub/runtime/_fetcher.ts"], "sourcesContent": ["// This file was generated by basehub. Do not edit directly. Read more: https://basehub.com/docs/api-reference/basehub-sdk\n\n/* eslint-disable */\n/* eslint-disable eslint-comments/no-restricted-disable */\n/* tslint:disable */\n\n// @ts-nocheck\nimport { QueryBatcher } from './_batcher'\n\nimport type { ClientOptions } from './_create-client'\nimport type { GraphqlOperation } from './_generate-graphql-operation'\nimport { GenqlError } from './_error'\n\nexport interface Fetcher {\n    (\n        gql: GraphqlOperation,\n        extraFetchOptions?: Partial<RequestInit>,\n    ): Promise<any>\n}\n\nexport type BatchOptions = {\n    batchInterval?: number // ms\n    maxBatchSize?: number\n}\n\nconst DEFAULT_BATCH_OPTIONS = {\n    maxBatchSize: 10,\n    batchInterval: 40,\n}\n\nexport const createFetcher = ({\n    url,\n    headers = {},\n    fetcher,\n    fetch: _fetch,\n    batch = false,\n    ...rest\n}: ClientOptions): Fetcher => {\n    if (!url && !fetcher) {\n        throw new Error('url or fetcher is required')\n    }\n\n    fetcher =\n        fetcher ||\n        (async (body, extraFetchOptions) => {\n            let headersObject =\n                typeof headers == 'function' ? await headers() : headers\n            headersObject = headersObject || {}\n            if (typeof fetch === 'undefined' && !_fetch) {\n                throw new Error(\n                    'Global `fetch` function is not available, pass a fetch polyfill to Genql `createClient`',\n                )\n            }\n            let fetchImpl = _fetch || fetch\n\n            if (extraFetchOptions?.headers) {\n                headersObject = {\n                    ...headersObject,\n                    ...extraFetchOptions.headers,\n                }\n                delete extraFetchOptions.headers\n            }\n\n            const res = await fetchImpl(url!, {\n                headers: {\n                    'Content-Type': 'application/json',\n                    ...headersObject,\n                },\n                method: 'POST',\n                body: JSON.stringify(body),\n                ...rest,\n                ...extraFetchOptions,\n            })\n            if (!res.ok) {\n                throw new Error(`${res.statusText}: ${await res.text()}`)\n            }\n            const json = await res.json()\n            return json\n        })\n\n    if (!batch) {\n        return async (body, extraFetchOptions) => {\n            const json = await fetcher!(body, extraFetchOptions)\n            if (Array.isArray(json)) {\n                return json.map((json) => {\n                    if (json?.errors?.length) {\n                        throw new GenqlError(json.errors || [], json.data)\n                    }\n                    return json.data\n                })\n            } else {\n                if (json?.errors?.length) {\n                    throw new GenqlError(json.errors || [], json.data)\n                }\n                return json.data\n            }\n        }\n    }\n\n    const batcher = new QueryBatcher(\n        async (batchedQuery, extraFetchOptions) => {\n            // console.log(batchedQuery) // [{ query: 'query{user{age}}', variables: {} }, ...]\n            const json = await fetcher!(batchedQuery, extraFetchOptions)\n            return json as any\n        },\n        batch === true ? DEFAULT_BATCH_OPTIONS : batch,\n    )\n\n    return async ({ query, variables }) => {\n        const json = await batcher.fetch(query, variables)\n        if (json?.data) {\n            return json.data\n        }\n        throw new Error(\n            'Genql batch fetcher returned unexpected result ' +\n                JSON.stringify(json),\n        )\n    }\n}\n"], "names": [], "mappings": "AAAA,0HAA0H;AAE1H,kBAAkB,GAClB,wDAAwD,GACxD,kBAAkB,GAElB,cAAc;;;;AACd;AAIA;;;AAcA,MAAM,wBAAwB;IAC1B,cAAc;IACd,eAAe;AACnB;AAEO,MAAM,gBAAgB,CAAC,EAC1B,GAAG,EACH,UAAU,CAAC,CAAC,EACZ,OAAO,EACP,OAAO,MAAM,EACb,QAAQ,KAAK,EACb,GAAG,MACS;IACZ,IAAI,CAAC,OAAO,CAAC,SAAS;QAClB,MAAM,IAAI,MAAM;IACpB;IAEA,UACI,WACA,CAAC,OAAO,MAAM;QACV,IAAI,gBACA,OAAO,WAAW,aAAa,MAAM,YAAY;QACrD,gBAAgB,iBAAiB,CAAC;QAClC,IAAI,OAAO,UAAU,eAAe,CAAC,QAAQ;YACzC,MAAM,IAAI,MACN;QAER;QACA,IAAI,YAAY,UAAU;QAE1B,IAAI,mBAAmB,SAAS;YAC5B,gBAAgB;gBACZ,GAAG,aAAa;gBAChB,GAAG,kBAAkB,OAAO;YAChC;YACA,OAAO,kBAAkB,OAAO;QACpC;QAEA,MAAM,MAAM,MAAM,UAAU,KAAM;YAC9B,SAAS;gBACL,gBAAgB;gBAChB,GAAG,aAAa;YACpB;YACA,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;YACrB,GAAG,IAAI;YACP,GAAG,iBAAiB;QACxB;QACA,IAAI,CAAC,IAAI,EAAE,EAAE;YACT,MAAM,IAAI,MAAM,GAAG,IAAI,UAAU,CAAC,EAAE,EAAE,MAAM,IAAI,IAAI,IAAI;QAC5D;QACA,MAAM,OAAO,MAAM,IAAI,IAAI;QAC3B,OAAO;IACX,CAAC;IAEL,IAAI,CAAC,OAAO;QACR,OAAO,OAAO,MAAM;YAChB,MAAM,OAAO,MAAM,QAAS,MAAM;YAClC,IAAI,MAAM,OAAO,CAAC,OAAO;gBACrB,OAAO,KAAK,GAAG,CAAC,CAAC;oBACb,IAAI,MAAM,QAAQ,QAAQ;wBACtB,MAAM,IAAI,iJAAA,CAAA,aAAU,CAAC,KAAK,MAAM,IAAI,EAAE,EAAE,KAAK,IAAI;oBACrD;oBACA,OAAO,KAAK,IAAI;gBACpB;YACJ,OAAO;gBACH,IAAI,MAAM,QAAQ,QAAQ;oBACtB,MAAM,IAAI,iJAAA,CAAA,aAAU,CAAC,KAAK,MAAM,IAAI,EAAE,EAAE,KAAK,IAAI;gBACrD;gBACA,OAAO,KAAK,IAAI;YACpB;QACJ;IACJ;IAEA,MAAM,UAAU,IAAI,mJAAA,CAAA,eAAY,CAC5B,OAAO,cAAc;QACjB,mFAAmF;QACnF,MAAM,OAAO,MAAM,QAAS,cAAc;QAC1C,OAAO;IACX,GACA,UAAU,OAAO,wBAAwB;IAG7C,OAAO,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE;QAC9B,MAAM,OAAO,MAAM,QAAQ,KAAK,CAAC,OAAO;QACxC,IAAI,MAAM,MAAM;YACZ,OAAO,KAAK,IAAI;QACpB;QACA,MAAM,IAAI,MACN,oDACI,KAAK,SAAS,CAAC;IAE3B;AACJ", "debugId": null}}, {"offset": {"line": 340, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/cms/.basehub/runtime/_aliasing.js"], "sourcesContent": ["// This file was generated by basehub. Do not edit directly. Read more: https://basehub.com/docs/api-reference/basehub-sdk\n\n/* eslint-disable */\n/* eslint-disable eslint-comments/no-restricted-disable */\n/* tslint:disable */\n\n// @ts-nocheck\nexport const aliasSeparator = '__alias__'\n\nexport function replaceSystemAliases(obj) {\n    if (typeof obj !== 'object' || obj === null) {\n        return obj\n    }\n\n    if (Array.isArray(obj)) {\n        return obj.map((item) => replaceSystemAliases(item))\n    }\n\n    const newObj = {}\n    for (const [key, value] of Object.entries(obj)) {\n        if (key.includes(aliasSeparator)) {\n            const [_prefix, ...rest] = key.split(aliasSeparator)\n            const newKey = rest.join(aliasSeparator) // In case there are multiple __alias__ in the key\n            newObj[newKey] = replaceSystemAliases(value)\n        } else {\n            newObj[key] = replaceSystemAliases(value)\n        }\n    }\n\n    return newObj\n}\n"], "names": [], "mappings": "AAAA,0HAA0H;AAE1H,kBAAkB,GAClB,wDAAwD,GACxD,kBAAkB,GAElB,cAAc;;;;;AACP,MAAM,iBAAiB;AAEvB,SAAS,qBAAqB,GAAG;IACpC,IAAI,OAAO,QAAQ,YAAY,QAAQ,MAAM;QACzC,OAAO;IACX;IAEA,IAAI,MAAM,OAAO,CAAC,MAAM;QACpB,OAAO,IAAI,GAAG,CAAC,CAAC,OAAS,qBAAqB;IAClD;IAEA,MAAM,SAAS,CAAC;IAChB,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,KAAM;QAC5C,IAAI,IAAI,QAAQ,CAAC,iBAAiB;YAC9B,MAAM,CAAC,SAAS,GAAG,KAAK,GAAG,IAAI,KAAK,CAAC;YACrC,MAAM,SAAS,KAAK,IAAI,CAAC,gBAAgB,kDAAkD;;YAC3F,MAAM,CAAC,OAAO,GAAG,qBAAqB;QAC1C,OAAO;YACH,MAAM,CAAC,IAAI,GAAG,qBAAqB;QACvC;IACJ;IAEA,OAAO;AACX", "debugId": null}}, {"offset": {"line": 373, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/cms/.basehub/runtime/_generate-graphql-operation.ts"], "sourcesContent": ["// This file was generated by basehub. Do not edit directly. Read more: https://basehub.com/docs/api-reference/basehub-sdk\n\n/* eslint-disable */\n/* eslint-disable eslint-comments/no-restricted-disable */\n/* tslint:disable */\n\n// @ts-nocheck\nimport { aliasSeparator } from './_aliasing'\nimport type { LinkedField, LinkedType } from './_types'\n\nexport interface Args {\n    [arg: string]: any | undefined\n}\n\nexport interface Fields {\n    [field: string]: Request\n}\n\nexport type Request = boolean | number | Fields\n\nexport interface Variables {\n    [name: string]: {\n        value: any\n        typing: [LinkedType, string]\n    }\n}\n\nexport interface Context {\n    root: LinkedType\n    varCounter: number\n    variables: Variables\n    fragmentCounter: number\n    fragments: string[]\n}\n\nexport interface GraphqlOperation {\n    query: string\n    variables?: { [name: string]: any }\n    operationName?: string\n}\n\nconst parseRequest = (\n    request: Request | undefined,\n    ctx: Context,\n    path: string[],\n    options?: { aliasPrefix?: string },\n): string => {\n    if (typeof request === 'object' && '__args' in request) {\n        const args: any = request.__args\n        let fields: Request | undefined = { ...request }\n        delete fields.__args\n        const argNames = Object.keys(args)\n\n        if (argNames.length === 0) {\n            return parseRequest(fields, ctx, path, options)\n        }\n\n        const field = getFieldFromPath(ctx.root, path)\n\n        const argStrings = argNames.map((argName) => {\n            ctx.varCounter++\n            const varName = `v${ctx.varCounter}`\n\n            const typing = field.args && field.args[argName] // typeMap used here, .args\n\n            if (!typing) {\n                throw new Error(\n                    `no typing defined for argument \\`${argName}\\` in path \\`${path.join(\n                        '.',\n                    )}\\``,\n                )\n            }\n\n            const shouldStringifyValue = ['String', 'String!'].includes(\n                typing[1],\n            )\n            let value = args[argName]\n            if (shouldStringifyValue) {\n                if (typeof value === 'object') {\n                    // stringify the object\n                    value = JSON.stringify(value)\n                }\n            }\n\n            ctx.variables[varName] = {\n                value,\n                typing,\n            }\n\n            return `${argName}:$${varName}`\n        })\n        return `(${argStrings})${parseRequest(fields, ctx, path, options)}`\n    } else if (typeof request === 'object' && Object.keys(request).length > 0) {\n        const fields = request\n        const fieldNames = Object.keys(fields).filter((k) => Boolean(fields[k]))\n\n        const type =\n            path.length > 0 ? getFieldFromPath(ctx.root, path).type : ctx.root\n        const scalarFields = type.scalar\n\n        let scalarFieldsFragment: string | undefined\n\n        const validFieldNames = fieldNames.filter((f) => {\n            if (['__scalar', '__name', '__fragmentOn'].includes(f)) return true\n            if (f.startsWith('on_')) return true\n            return type.fields && f in type.fields\n        })\n\n        if (validFieldNames.length === 0) {\n            return ''\n        }\n\n        if (fieldNames.includes('__scalar')) {\n            const falsyFieldNames = new Set(\n                Object.keys(fields).filter((k) => !Boolean(fields[k])),\n            )\n            if (scalarFields?.length) {\n                ctx.fragmentCounter++\n                scalarFieldsFragment = `f${ctx.fragmentCounter}`\n\n                ctx.fragments.push(\n                    `fragment ${scalarFieldsFragment} on ${\n                        type.name\n                    }{${scalarFields\n                        .filter((f) => !falsyFieldNames.has(f))\n                        .map(\n                            (f) =>\n                                `${\n                                    options?.aliasPrefix\n                                        ? `${options.aliasPrefix}${aliasSeparator}${f}: `\n                                        : ''\n                                }${f}`,\n                        )\n                        .join(',')}}`,\n                )\n            }\n        }\n\n        const fieldsSelection = validFieldNames\n            .filter((f) => !['__scalar', '__name', '__fragmentOn'].includes(f))\n            .map((f) => {\n                if (f.startsWith('on_')) {\n                    ctx.fragmentCounter++\n                    const implementationFragment = `f${ctx.fragmentCounter}`\n                    const parsed = parseRequest(fields[f], ctx, [...path, f], {\n                        ...options,\n                        aliasPrefix: implementationFragment,\n                    })\n\n                    const typeMatch = f.match(/^on_(.+)/)\n\n                    if (!typeMatch || !typeMatch[1])\n                        throw new Error('match failed')\n\n                    ctx.fragments.push(\n                        `fragment ${implementationFragment} on ${typeMatch[1]}${parsed}`,\n                    )\n\n                    return `...${implementationFragment}`\n                } else {\n                    const field = type.fields?.[f]\n                    if (!field) return ''\n\n                    // For scalar fields or fields without subfields, just return the field name\n                    if (!field.type.fields) {\n                        return `${\n                            options?.aliasPrefix\n                                ? `${options.aliasPrefix}${aliasSeparator}${f}: `\n                                : ''\n                        }${f}`\n                    }\n\n                    // For object fields, parse recursively\n                    const parsed = parseRequest(\n                        fields[f],\n                        ctx,\n                        [...path, f],\n                        options,\n                    )\n\n                    // If the parsed result is empty and this is an object type,\n                    // we should include at least one field or it will be invalid GraphQL\n                    if (!parsed && field.type.fields) {\n                        // Get the first scalar field as a default selection\n                        const firstScalar = field.type.scalar?.[0]\n                        if (firstScalar) {\n                            return `${\n                                options?.aliasPrefix\n                                    ? `${options.aliasPrefix}${aliasSeparator}${f}: `\n                                    : ''\n                            }${f}{${firstScalar}}`\n                        }\n                    }\n\n                    return `${\n                        options?.aliasPrefix\n                            ? `${options.aliasPrefix}${aliasSeparator}${f}: `\n                            : ''\n                    }${f}${parsed}`\n                }\n            })\n            .filter(Boolean)\n            .concat(scalarFieldsFragment ? [`...${scalarFieldsFragment}`] : [])\n            .join(',')\n\n        return fieldsSelection ? `{${fieldsSelection}}` : ''\n    } else {\n        return ''\n    }\n}\n\nexport const generateGraphqlOperation = (\n    operation: 'query' | 'mutation' | 'subscription',\n    root: LinkedType,\n    fields?: Fields,\n): GraphqlOperation => {\n    const ctx: Context = {\n        root: root,\n        varCounter: 0,\n        variables: {},\n        fragmentCounter: 0,\n        fragments: [],\n    }\n    const result = parseRequest(fields, ctx, [])\n\n    const varNames = Object.keys(ctx.variables)\n\n    const varsString =\n        varNames.length > 0\n            ? `(${varNames.map((v) => {\n                  const variableType = ctx.variables[v].typing[1]\n                  return `$${v}:${variableType}`\n              })})`\n            : ''\n\n    const operationName = fields?.__name || ''\n\n    return {\n        query: [\n            `${operation} ${operationName}${varsString}${result}`,\n            ...ctx.fragments,\n        ].join(','),\n        variables: Object.keys(ctx.variables).reduce<{ [name: string]: any }>(\n            (r, v) => {\n                r[v] = ctx.variables[v].value\n                return r\n            },\n            {},\n        ),\n        ...(operationName ? { operationName: operationName.toString() } : {}),\n    }\n}\n\nexport const getFieldFromPath = (\n    root: LinkedType | undefined,\n    path: string[],\n) => {\n    let current: LinkedField | undefined\n\n    if (!root) throw new Error('root type is not provided')\n\n    if (path.length === 0) throw new Error(`path is empty`)\n\n    path.forEach((f) => {\n        const type = current ? current.type : root\n\n        if (!type.fields)\n            throw new Error(`type \\`${type.name}\\` does not have fields`)\n\n        const possibleTypes = Object.keys(type.fields)\n            .filter((i) => i.startsWith('on_'))\n            .reduce(\n                (types, fieldName) => {\n                    const field = type.fields && type.fields[fieldName]\n                    if (field) types.push(field.type)\n                    return types\n                },\n                [type],\n            )\n\n        let field: LinkedField | null = null\n\n        possibleTypes.forEach((type) => {\n            const found = type.fields && type.fields[f]\n            if (found) field = found\n        })\n\n        if (!field)\n            throw new Error(\n                `type \\`${type.name}\\` does not have a field \\`${f}\\``,\n            )\n\n        current = field\n    })\n\n    return current as LinkedField\n}\n"], "names": [], "mappings": "AAAA,0HAA0H;AAE1H,kBAAkB,GAClB,wDAAwD,GACxD,kBAAkB,GAElB,cAAc;;;;;AACd;;AAkCA,MAAM,eAAe,CACjB,SACA,KACA,MACA;IAEA,IAAI,OAAO,YAAY,YAAY,YAAY,SAAS;QACpD,MAAM,OAAY,QAAQ,MAAM;QAChC,IAAI,SAA8B;YAAE,GAAG,OAAO;QAAC;QAC/C,OAAO,OAAO,MAAM;QACpB,MAAM,WAAW,OAAO,IAAI,CAAC;QAE7B,IAAI,SAAS,MAAM,KAAK,GAAG;YACvB,OAAO,aAAa,QAAQ,KAAK,MAAM;QAC3C;QAEA,MAAM,QAAQ,iBAAiB,IAAI,IAAI,EAAE;QAEzC,MAAM,aAAa,SAAS,GAAG,CAAC,CAAC;YAC7B,IAAI,UAAU;YACd,MAAM,UAAU,CAAC,CAAC,EAAE,IAAI,UAAU,EAAE;YAEpC,MAAM,SAAS,MAAM,IAAI,IAAI,MAAM,IAAI,CAAC,QAAQ,CAAC,2BAA2B;;YAE5E,IAAI,CAAC,QAAQ;gBACT,MAAM,IAAI,MACN,CAAC,iCAAiC,EAAE,QAAQ,aAAa,EAAE,KAAK,IAAI,CAChE,KACF,EAAE,CAAC;YAEb;YAEA,MAAM,uBAAuB;gBAAC;gBAAU;aAAU,CAAC,QAAQ,CACvD,MAAM,CAAC,EAAE;YAEb,IAAI,QAAQ,IAAI,CAAC,QAAQ;YACzB,IAAI,sBAAsB;gBACtB,IAAI,OAAO,UAAU,UAAU;oBAC3B,uBAAuB;oBACvB,QAAQ,KAAK,SAAS,CAAC;gBAC3B;YACJ;YAEA,IAAI,SAAS,CAAC,QAAQ,GAAG;gBACrB;gBACA;YACJ;YAEA,OAAO,GAAG,QAAQ,EAAE,EAAE,SAAS;QACnC;QACA,OAAO,CAAC,CAAC,EAAE,WAAW,CAAC,EAAE,aAAa,QAAQ,KAAK,MAAM,UAAU;IACvE,OAAO,IAAI,OAAO,YAAY,YAAY,OAAO,IAAI,CAAC,SAAS,MAAM,GAAG,GAAG;QACvE,MAAM,SAAS;QACf,MAAM,aAAa,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,CAAC,IAAM,QAAQ,MAAM,CAAC,EAAE;QAEtE,MAAM,OACF,KAAK,MAAM,GAAG,IAAI,iBAAiB,IAAI,IAAI,EAAE,MAAM,IAAI,GAAG,IAAI,IAAI;QACtE,MAAM,eAAe,KAAK,MAAM;QAEhC,IAAI;QAEJ,MAAM,kBAAkB,WAAW,MAAM,CAAC,CAAC;YACvC,IAAI;gBAAC;gBAAY;gBAAU;aAAe,CAAC,QAAQ,CAAC,IAAI,OAAO;YAC/D,IAAI,EAAE,UAAU,CAAC,QAAQ,OAAO;YAChC,OAAO,KAAK,MAAM,IAAI,KAAK,KAAK,MAAM;QAC1C;QAEA,IAAI,gBAAgB,MAAM,KAAK,GAAG;YAC9B,OAAO;QACX;QAEA,IAAI,WAAW,QAAQ,CAAC,aAAa;YACjC,MAAM,kBAAkB,IAAI,IACxB,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,CAAC,IAAM,CAAC,QAAQ,MAAM,CAAC,EAAE;YAExD,IAAI,cAAc,QAAQ;gBACtB,IAAI,eAAe;gBACnB,uBAAuB,CAAC,CAAC,EAAE,IAAI,eAAe,EAAE;gBAEhD,IAAI,SAAS,CAAC,IAAI,CACd,CAAC,SAAS,EAAE,qBAAqB,IAAI,EACjC,KAAK,IAAI,CACZ,CAAC,EAAE,aACC,MAAM,CAAC,CAAC,IAAM,CAAC,gBAAgB,GAAG,CAAC,IACnC,GAAG,CACA,CAAC,IACG,GACI,SAAS,cACH,GAAG,QAAQ,WAAW,GAAG,oJAAA,CAAA,iBAAc,GAAG,EAAE,EAAE,CAAC,GAC/C,KACP,GAAG,EAEb,IAAI,CAAC,KAAK,CAAC,CAAC;YAEzB;QACJ;QAEA,MAAM,kBAAkB,gBACnB,MAAM,CAAC,CAAC,IAAM,CAAC;gBAAC;gBAAY;gBAAU;aAAe,CAAC,QAAQ,CAAC,IAC/D,GAAG,CAAC,CAAC;YACF,IAAI,EAAE,UAAU,CAAC,QAAQ;gBACrB,IAAI,eAAe;gBACnB,MAAM,yBAAyB,CAAC,CAAC,EAAE,IAAI,eAAe,EAAE;gBACxD,MAAM,SAAS,aAAa,MAAM,CAAC,EAAE,EAAE,KAAK;uBAAI;oBAAM;iBAAE,EAAE;oBACtD,GAAG,OAAO;oBACV,aAAa;gBACjB;gBAEA,MAAM,YAAY,EAAE,KAAK,CAAC;gBAE1B,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,EAAE,EAC3B,MAAM,IAAI,MAAM;gBAEpB,IAAI,SAAS,CAAC,IAAI,CACd,CAAC,SAAS,EAAE,uBAAuB,IAAI,EAAE,SAAS,CAAC,EAAE,GAAG,QAAQ;gBAGpE,OAAO,CAAC,GAAG,EAAE,wBAAwB;YACzC,OAAO;gBACH,MAAM,QAAQ,KAAK,MAAM,EAAE,CAAC,EAAE;gBAC9B,IAAI,CAAC,OAAO,OAAO;gBAEnB,4EAA4E;gBAC5E,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,EAAE;oBACpB,OAAO,GACH,SAAS,cACH,GAAG,QAAQ,WAAW,GAAG,oJAAA,CAAA,iBAAc,GAAG,EAAE,EAAE,CAAC,GAC/C,KACP,GAAG;gBACV;gBAEA,uCAAuC;gBACvC,MAAM,SAAS,aACX,MAAM,CAAC,EAAE,EACT,KACA;uBAAI;oBAAM;iBAAE,EACZ;gBAGJ,4DAA4D;gBAC5D,qEAAqE;gBACrE,IAAI,CAAC,UAAU,MAAM,IAAI,CAAC,MAAM,EAAE;oBAC9B,oDAAoD;oBACpD,MAAM,cAAc,MAAM,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE;oBAC1C,IAAI,aAAa;wBACb,OAAO,GACH,SAAS,cACH,GAAG,QAAQ,WAAW,GAAG,oJAAA,CAAA,iBAAc,GAAG,EAAE,EAAE,CAAC,GAC/C,KACP,EAAE,CAAC,EAAE,YAAY,CAAC,CAAC;oBAC1B;gBACJ;gBAEA,OAAO,GACH,SAAS,cACH,GAAG,QAAQ,WAAW,GAAG,oJAAA,CAAA,iBAAc,GAAG,EAAE,EAAE,CAAC,GAC/C,KACP,IAAI,QAAQ;YACnB;QACJ,GACC,MAAM,CAAC,SACP,MAAM,CAAC,uBAAuB;YAAC,CAAC,GAAG,EAAE,sBAAsB;SAAC,GAAG,EAAE,EACjE,IAAI,CAAC;QAEV,OAAO,kBAAkB,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAC,GAAG;IACtD,OAAO;QACH,OAAO;IACX;AACJ;AAEO,MAAM,2BAA2B,CACpC,WACA,MACA;IAEA,MAAM,MAAe;QACjB,MAAM;QACN,YAAY;QACZ,WAAW,CAAC;QACZ,iBAAiB;QACjB,WAAW,EAAE;IACjB;IACA,MAAM,SAAS,aAAa,QAAQ,KAAK,EAAE;IAE3C,MAAM,WAAW,OAAO,IAAI,CAAC,IAAI,SAAS;IAE1C,MAAM,aACF,SAAS,MAAM,GAAG,IACZ,CAAC,CAAC,EAAE,SAAS,GAAG,CAAC,CAAC;QACd,MAAM,eAAe,IAAI,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE;QAC/C,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,cAAc;IAClC,GAAG,CAAC,CAAC,GACL;IAEV,MAAM,gBAAgB,QAAQ,UAAU;IAExC,OAAO;QACH,OAAO;YACH,GAAG,UAAU,CAAC,EAAE,gBAAgB,aAAa,QAAQ;eAClD,IAAI,SAAS;SACnB,CAAC,IAAI,CAAC;QACP,WAAW,OAAO,IAAI,CAAC,IAAI,SAAS,EAAE,MAAM,CACxC,CAAC,GAAG;YACA,CAAC,CAAC,EAAE,GAAG,IAAI,SAAS,CAAC,EAAE,CAAC,KAAK;YAC7B,OAAO;QACX,GACA,CAAC;QAEL,GAAI,gBAAgB;YAAE,eAAe,cAAc,QAAQ;QAAG,IAAI,CAAC,CAAC;IACxE;AACJ;AAEO,MAAM,mBAAmB,CAC5B,MACA;IAEA,IAAI;IAEJ,IAAI,CAAC,MAAM,MAAM,IAAI,MAAM;IAE3B,IAAI,KAAK,MAAM,KAAK,GAAG,MAAM,IAAI,MAAM,CAAC,aAAa,CAAC;IAEtD,KAAK,OAAO,CAAC,CAAC;QACV,MAAM,OAAO,UAAU,QAAQ,IAAI,GAAG;QAEtC,IAAI,CAAC,KAAK,MAAM,EACZ,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE,KAAK,IAAI,CAAC,uBAAuB,CAAC;QAEhE,MAAM,gBAAgB,OAAO,IAAI,CAAC,KAAK,MAAM,EACxC,MAAM,CAAC,CAAC,IAAM,EAAE,UAAU,CAAC,QAC3B,MAAM,CACH,CAAC,OAAO;YACJ,MAAM,QAAQ,KAAK,MAAM,IAAI,KAAK,MAAM,CAAC,UAAU;YACnD,IAAI,OAAO,MAAM,IAAI,CAAC,MAAM,IAAI;YAChC,OAAO;QACX,GACA;YAAC;SAAK;QAGd,IAAI,QAA4B;QAEhC,cAAc,OAAO,CAAC,CAAC;YACnB,MAAM,QAAQ,KAAK,MAAM,IAAI,KAAK,MAAM,CAAC,EAAE;YAC3C,IAAI,OAAO,QAAQ;QACvB;QAEA,IAAI,CAAC,OACD,MAAM,IAAI,MACN,CAAC,OAAO,EAAE,KAAK,IAAI,CAAC,2BAA2B,EAAE,EAAE,EAAE,CAAC;QAG9D,UAAU;IACd;IAEA,OAAO;AACX", "debugId": null}}, {"offset": {"line": 554, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/cms/.basehub/runtime/_create-client.ts"], "sourcesContent": ["// This file was generated by basehub. Do not edit directly. Read more: https://basehub.com/docs/api-reference/basehub-sdk\n\n/* eslint-disable */\n/* eslint-disable eslint-comments/no-restricted-disable */\n/* tslint:disable */\n\n// @ts-nocheck\nimport { type BatchOptions, createFetcher } from './_fetcher'\nimport type { ExecutionResult, LinkedType } from './_types'\nimport {\n    generateGraphqlOperation,\n    type GraphqlOperation,\n} from './_generate-graphql-operation'\nimport { replaceSystemAliases } from './_aliasing'\n\nexport type Headers =\n    | HeadersInit\n    | (() => HeadersInit)\n    | (() => Promise<HeadersInit>)\n\nexport type BaseFetcher = (\n    operation: GraphqlOperation | GraphqlOperation[],\n    extraFetchOptions?: Partial<RequestInit>,\n) => Promise<ExecutionResult | ExecutionResult[]>\n\nexport type ClientOptions = Omit<RequestInit, 'body' | 'headers'> & {\n    url?: string\n    batch?: BatchOptions | boolean\n    fetcher?: BaseFetcher\n    fetch?: Function\n    headers?: Headers\n}\n\nexport const createClient = ({\n    queryRoot,\n    mutationRoot,\n    subscriptionRoot,\n    getExtraFetchOptions,\n    ...options\n}: ClientOptions & {\n    queryRoot?: LinkedType\n    mutationRoot?: LinkedType\n    subscriptionRoot?: LinkedType\n    getExtraFetchOptions?: (\n        op: 'query' | 'mutation',\n        body: GraphqlOperation,\n        originalRequest: any,\n    ) => Partial<RequestInit> | Promise<Partial<RequestInit>>\n}) => {\n    const fetcher = createFetcher(options)\n    const client: {\n        query?: Function\n        mutation?: Function\n    } = {}\n\n    if (queryRoot) {\n        client.query = async (request: any) => {\n            if (!queryRoot) throw new Error('queryRoot argument is missing')\n\n            const body = generateGraphqlOperation('query', queryRoot, request)\n            const extraFetchOptions = await getExtraFetchOptions?.(\n                'query',\n                body,\n                request,\n            )\n\n            return await fetcher(body, extraFetchOptions).then((result) =>\n                replaceSystemAliases(result),\n            )\n        }\n    }\n    if (mutationRoot) {\n        client.mutation = async (request: any) => {\n            if (!mutationRoot)\n                throw new Error('mutationRoot argument is missing')\n\n            const body = generateGraphqlOperation(\n                'mutation',\n                mutationRoot,\n                request,\n            )\n            const extraFetchOptions = await getExtraFetchOptions?.(\n                'mutation',\n                body,\n                request,\n            )\n            return await fetcher(\n                generateGraphqlOperation('mutation', mutationRoot, request),\n                extraFetchOptions,\n            )\n        }\n    }\n\n    return client as any\n}\n\ncreateClient.replaceSystemAliases = replaceSystemAliases\n"], "names": [], "mappings": "AAAA,0HAA0H;AAE1H,kBAAkB,GAClB,wDAAwD,GACxD,kBAAkB,GAElB,cAAc;;;;AACd;AAEA;AAIA;;;;AAoBO,MAAM,eAAe,CAAC,EACzB,SAAS,EACT,YAAY,EACZ,gBAAgB,EAChB,oBAAoB,EACpB,GAAG,SAUN;IACG,MAAM,UAAU,CAAA,GAAA,mJAAA,CAAA,gBAAa,AAAD,EAAE;IAC9B,MAAM,SAGF,CAAC;IAEL,IAAI,WAAW;QACX,OAAO,KAAK,GAAG,OAAO;YAClB,IAAI,CAAC,WAAW,MAAM,IAAI,MAAM;YAEhC,MAAM,OAAO,CAAA,GAAA,4KAAA,CAAA,2BAAwB,AAAD,EAAE,SAAS,WAAW;YAC1D,MAAM,oBAAoB,MAAM,uBAC5B,SACA,MACA;YAGJ,OAAO,MAAM,QAAQ,MAAM,mBAAmB,IAAI,CAAC,CAAC,SAChD,CAAA,GAAA,oJAAA,CAAA,uBAAoB,AAAD,EAAE;QAE7B;IACJ;IACA,IAAI,cAAc;QACd,OAAO,QAAQ,GAAG,OAAO;YACrB,IAAI,CAAC,cACD,MAAM,IAAI,MAAM;YAEpB,MAAM,OAAO,CAAA,GAAA,4KAAA,CAAA,2BAAwB,AAAD,EAChC,YACA,cACA;YAEJ,MAAM,oBAAoB,MAAM,uBAC5B,YACA,MACA;YAEJ,OAAO,MAAM,QACT,CAAA,GAAA,4KAAA,CAAA,2BAAwB,AAAD,EAAE,YAAY,cAAc,UACnD;QAER;IACJ;IAEA,OAAO;AACX;AAEA,aAAa,oBAAoB,GAAG,oJAAA,CAAA,uBAAoB", "debugId": null}}, {"offset": {"line": 593, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/cms/.basehub/runtime/_link-type-map.ts"], "sourcesContent": ["// This file was generated by basehub. Do not edit directly. Read more: https://basehub.com/docs/api-reference/basehub-sdk\n\n/* eslint-disable */\n/* eslint-disable eslint-comments/no-restricted-disable */\n/* tslint:disable */\n\n// @ts-nocheck\nimport type {\n    CompressedType,\n    CompressedTypeMap,\n    LinkedArgMap,\n    LinkedField,\n    LinkedType,\n    LinkedTypeMap,\n} from './_types'\n\nexport interface PartialLinkedFieldMap {\n    [field: string]: {\n        type: string\n        args?: LinkedArgMap\n    }\n}\n\nexport const linkTypeMap = (\n    typeMap: CompressedTypeMap<number>,\n): LinkedTypeMap => {\n    const indexToName: Record<number, string> = Object.assign(\n        {},\n        ...Object.keys(typeMap.types).map((k, i) => ({ [i]: k })),\n    )\n\n    let intermediaryTypeMap = Object.assign(\n        {},\n        ...Object.keys(typeMap.types || {}).map(\n            (k): Record<string, LinkedType> => {\n                const type: CompressedType = typeMap.types[k]!\n                const fields = type || {}\n                return {\n                    [k]: {\n                        name: k,\n                        // type scalar properties\n                        scalar: Object.keys(fields).filter((f) => {\n                            const [type] = fields[f] || []\n\n                            const isScalar =\n                                type && typeMap.scalars.includes(type)\n                            if (!isScalar) {\n                                return false\n                            }\n                            const args = fields[f]?.[1]\n                            const argTypes = Object.values(args || {})\n                                .map((x) => x?.[1])\n                                .filter(Boolean)\n\n                            const hasRequiredArgs = argTypes.some(\n                                (str) => str && str.endsWith('!'),\n                            )\n                            if (hasRequiredArgs) {\n                                return false\n                            }\n                            return true\n                        }),\n                        // fields with corresponding `type` and `args`\n                        fields: Object.assign(\n                            {},\n                            ...Object.keys(fields).map(\n                                (f): PartialLinkedFieldMap => {\n                                    const [typeIndex, args] = fields[f] || []\n                                    if (typeIndex == null) {\n                                        return {}\n                                    }\n                                    return {\n                                        [f]: {\n                                            // replace index with type name\n                                            type: indexToName[typeIndex],\n                                            args: Object.assign(\n                                                {},\n                                                ...Object.keys(args || {}).map(\n                                                    (k) => {\n                                                        // if argTypeString == argTypeName, argTypeString is missing, need to readd it\n                                                        if (!args || !args[k]) {\n                                                            return\n                                                        }\n                                                        const [\n                                                            argTypeName,\n                                                            argTypeString,\n                                                        ] = args[k] as any\n                                                        return {\n                                                            [k]: [\n                                                                indexToName[\n                                                                    argTypeName\n                                                                ],\n                                                                argTypeString ||\n                                                                    indexToName[\n                                                                        argTypeName\n                                                                    ],\n                                                            ],\n                                                        }\n                                                    },\n                                                ),\n                                            ),\n                                        },\n                                    }\n                                },\n                            ),\n                        ),\n                    },\n                }\n            },\n        ),\n    )\n    const res = resolveConcreteTypes(intermediaryTypeMap)\n    return res\n}\n\n// replace typename with concrete type\nexport const resolveConcreteTypes = (linkedTypeMap: LinkedTypeMap) => {\n    Object.keys(linkedTypeMap).forEach((typeNameFromKey) => {\n        const type: LinkedType = linkedTypeMap[typeNameFromKey]!\n        // type.name = typeNameFromKey\n        if (!type.fields) {\n            return\n        }\n\n        const fields = type.fields\n\n        Object.keys(fields).forEach((f) => {\n            const field: LinkedField = fields[f]!\n\n            if (field.args) {\n                const args = field.args\n                Object.keys(args).forEach((key) => {\n                    const arg = args[key]\n\n                    if (arg) {\n                        const [typeName] = arg\n\n                        if (typeof typeName === 'string') {\n                            if (!linkedTypeMap[typeName]) {\n                                linkedTypeMap[typeName] = { name: typeName }\n                            }\n\n                            arg[0] = linkedTypeMap[typeName]!\n                        }\n                    }\n                })\n            }\n\n            const typeName = field.type as LinkedType | string\n\n            if (typeof typeName === 'string') {\n                if (!linkedTypeMap[typeName]) {\n                    linkedTypeMap[typeName] = { name: typeName }\n                }\n\n                field.type = linkedTypeMap[typeName]!\n            }\n        })\n    })\n\n    return linkedTypeMap\n}\n"], "names": [], "mappings": "AAAA,0HAA0H;AAE1H,kBAAkB,GAClB,wDAAwD,GACxD,kBAAkB,GAElB,cAAc;;;;;AAiBP,MAAM,cAAc,CACvB;IAEA,MAAM,cAAsC,OAAO,MAAM,CACrD,CAAC,MACE,OAAO,IAAI,CAAC,QAAQ,KAAK,EAAE,GAAG,CAAC,CAAC,GAAG,IAAM,CAAC;YAAE,CAAC,EAAE,EAAE;QAAE,CAAC;IAG3D,IAAI,sBAAsB,OAAO,MAAM,CACnC,CAAC,MACE,OAAO,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,GAAG,GAAG,CACnC,CAAC;QACG,MAAM,OAAuB,QAAQ,KAAK,CAAC,EAAE;QAC7C,MAAM,SAAS,QAAQ,CAAC;QACxB,OAAO;YACH,CAAC,EAAE,EAAE;gBACD,MAAM;gBACN,yBAAyB;gBACzB,QAAQ,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,CAAC;oBAChC,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,EAAE,IAAI,EAAE;oBAE9B,MAAM,WACF,QAAQ,QAAQ,OAAO,CAAC,QAAQ,CAAC;oBACrC,IAAI,CAAC,UAAU;wBACX,OAAO;oBACX;oBACA,MAAM,OAAO,MAAM,CAAC,EAAE,EAAE,CAAC,EAAE;oBAC3B,MAAM,WAAW,OAAO,MAAM,CAAC,QAAQ,CAAC,GACnC,GAAG,CAAC,CAAC,IAAM,GAAG,CAAC,EAAE,EACjB,MAAM,CAAC;oBAEZ,MAAM,kBAAkB,SAAS,IAAI,CACjC,CAAC,MAAQ,OAAO,IAAI,QAAQ,CAAC;oBAEjC,IAAI,iBAAiB;wBACjB,OAAO;oBACX;oBACA,OAAO;gBACX;gBACA,8CAA8C;gBAC9C,QAAQ,OAAO,MAAM,CACjB,CAAC,MACE,OAAO,IAAI,CAAC,QAAQ,GAAG,CACtB,CAAC;oBACG,MAAM,CAAC,WAAW,KAAK,GAAG,MAAM,CAAC,EAAE,IAAI,EAAE;oBACzC,IAAI,aAAa,MAAM;wBACnB,OAAO,CAAC;oBACZ;oBACA,OAAO;wBACH,CAAC,EAAE,EAAE;4BACD,+BAA+B;4BAC/B,MAAM,WAAW,CAAC,UAAU;4BAC5B,MAAM,OAAO,MAAM,CACf,CAAC,MACE,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,GAAG,CAC1B,CAAC;gCACG,8EAA8E;gCAC9E,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE;oCACnB;gCACJ;gCACA,MAAM,CACF,aACA,cACH,GAAG,IAAI,CAAC,EAAE;gCACX,OAAO;oCACH,CAAC,EAAE,EAAE;wCACD,WAAW,CACP,YACH;wCACD,iBACI,WAAW,CACP,YACH;qCACR;gCACL;4BACJ;wBAGZ;oBACJ;gBACJ;YAGZ;QACJ;IACJ;IAGR,MAAM,MAAM,qBAAqB;IACjC,OAAO;AACX;AAGO,MAAM,uBAAuB,CAAC;IACjC,OAAO,IAAI,CAAC,eAAe,OAAO,CAAC,CAAC;QAChC,MAAM,OAAmB,aAAa,CAAC,gBAAgB;QACvD,8BAA8B;QAC9B,IAAI,CAAC,KAAK,MAAM,EAAE;YACd;QACJ;QAEA,MAAM,SAAS,KAAK,MAAM;QAE1B,OAAO,IAAI,CAAC,QAAQ,OAAO,CAAC,CAAC;YACzB,MAAM,QAAqB,MAAM,CAAC,EAAE;YAEpC,IAAI,MAAM,IAAI,EAAE;gBACZ,MAAM,OAAO,MAAM,IAAI;gBACvB,OAAO,IAAI,CAAC,MAAM,OAAO,CAAC,CAAC;oBACvB,MAAM,MAAM,IAAI,CAAC,IAAI;oBAErB,IAAI,KAAK;wBACL,MAAM,CAAC,SAAS,GAAG;wBAEnB,IAAI,OAAO,aAAa,UAAU;4BAC9B,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE;gCAC1B,aAAa,CAAC,SAAS,GAAG;oCAAE,MAAM;gCAAS;4BAC/C;4BAEA,GAAG,CAAC,EAAE,GAAG,aAAa,CAAC,SAAS;wBACpC;oBACJ;gBACJ;YACJ;YAEA,MAAM,WAAW,MAAM,IAAI;YAE3B,IAAI,OAAO,aAAa,UAAU;gBAC9B,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE;oBAC1B,aAAa,CAAC,SAAS,GAAG;wBAAE,MAAM;oBAAS;gBAC/C;gBAEA,MAAM,IAAI,GAAG,aAAa,CAAC,SAAS;YACxC;QACJ;IACJ;IAEA,OAAO;AACX", "debugId": null}}, {"offset": {"line": 702, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/cms/.basehub/runtime/index.ts"], "sourcesContent": ["// This file was generated by basehub. Do not edit directly. Read more: https://basehub.com/docs/api-reference/basehub-sdk\n\n/* eslint-disable */\n/* eslint-disable eslint-comments/no-restricted-disable */\n/* tslint:disable */\n\n// @ts-nocheck\nexport { createClient } from './_create-client'\nexport type { ClientOptions } from './_create-client'\nexport type { FieldsSelection } from './_type-selection'\nexport { generateGraphqlOperation } from './_generate-graphql-operation'\nexport type { GraphqlOperation } from './_generate-graphql-operation'\nexport { linkTypeMap } from './_link-type-map'\n// export { Observable } from 'zen-observable-ts'\nexport { createFetcher } from './_fetcher'\nexport { GenqlError } from './_error'\nexport const everything = {\n    __scalar: true,\n}\n"], "names": [], "mappings": "AAAA,0HAA0H;AAE1H,kBAAkB,GAClB,wDAAwD,GACxD,kBAAkB,GAElB,cAAc;;;;AACd;AAGA;AAEA;AACA,iDAAiD;AACjD;AACA;;;;;;AACO,MAAM,aAAa;IACtB,UAAU;AACd", "debugId": null}}, {"offset": {"line": 740, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/cms/.basehub/types.ts"], "sourcesContent": ["// This file was generated by basehub. Do not edit directly. Read more: https://basehub.com/docs/api-reference/basehub-sdk\n\n/* eslint-disable */\n/* eslint-disable eslint-comments/no-restricted-disable */\n/* tslint:disable */\n// @ts-nocheck\n\nexport default {\n    \"scalars\": [\n        0,\n        4,\n        5,\n        6,\n        7,\n        25,\n        29,\n        30,\n        32,\n        33,\n        35,\n        36,\n        37,\n        41,\n        53,\n        58,\n        63,\n        71,\n        72\n    ],\n    \"types\": {\n        \"AnalyticsKeyScope\": {},\n        \"Authors\": {\n            \"_analyticsKey\": [\n                58,\n                {\n                    \"scope\": [\n                        0\n                    ]\n                }\n            ],\n            \"_dashboardUrl\": [\n                58\n            ],\n            \"_id\": [\n                58\n            ],\n            \"_idPath\": [\n                58\n            ],\n            \"_meta\": [\n                43\n            ],\n            \"_searchKey\": [\n                58\n            ],\n            \"_slug\": [\n                58\n            ],\n            \"_slugPath\": [\n                58\n            ],\n            \"_sys\": [\n                13\n            ],\n            \"_title\": [\n                58\n            ],\n            \"item\": [\n                2\n            ],\n            \"items\": [\n                2\n            ],\n            \"__typename\": [\n                58\n            ]\n        },\n        \"AuthorsItem\": {\n            \"_analyticsKey\": [\n                58,\n                {\n                    \"scope\": [\n                        0\n                    ]\n                }\n            ],\n            \"_dashboardUrl\": [\n                58\n            ],\n            \"_id\": [\n                58\n            ],\n            \"_idPath\": [\n                58\n            ],\n            \"_slug\": [\n                58\n            ],\n            \"_slugPath\": [\n                58\n            ],\n            \"_sys\": [\n                13\n            ],\n            \"_title\": [\n                58\n            ],\n            \"avatar\": [\n                15\n            ],\n            \"xUrl\": [\n                58\n            ],\n            \"__typename\": [\n                58\n            ]\n        },\n        \"AuthorsItemFilterInput\": {\n            \"AND\": [\n                3\n            ],\n            \"OR\": [\n                3\n            ],\n            \"_id\": [\n                59\n            ],\n            \"_slug\": [\n                59\n            ],\n            \"_sys_apiNamePath\": [\n                59\n            ],\n            \"_sys_createdAt\": [\n                31\n            ],\n            \"_sys_hash\": [\n                59\n            ],\n            \"_sys_id\": [\n                59\n            ],\n            \"_sys_idPath\": [\n                59\n            ],\n            \"_sys_lastModifiedAt\": [\n                31\n            ],\n            \"_sys_slug\": [\n                59\n            ],\n            \"_sys_slugPath\": [\n                59\n            ],\n            \"_sys_title\": [\n                59\n            ],\n            \"_title\": [\n                59\n            ],\n            \"xUrl\": [\n                59\n            ],\n            \"__typename\": [\n                58\n            ]\n        },\n        \"AuthorsItemOrderByEnum\": {},\n        \"BSHBEventSchema\": {},\n        \"BSHBRichTextContentSchema\": {},\n        \"BSHBRichTextTOCSchema\": {},\n        \"BaseRichTextJson\": {\n            \"blocks\": [\n                58\n            ],\n            \"content\": [\n                6\n            ],\n            \"toc\": [\n                7\n            ],\n            \"__typename\": [\n                58\n            ]\n        },\n        \"BlockAudio\": {\n            \"duration\": [\n                33\n            ],\n            \"fileName\": [\n                58\n            ],\n            \"fileSize\": [\n                36\n            ],\n            \"lastModified\": [\n                33\n            ],\n            \"mimeType\": [\n                58\n            ],\n            \"url\": [\n                58\n            ],\n            \"__typename\": [\n                58\n            ]\n        },\n        \"BlockCodeSnippet\": {\n            \"allowedLanguages\": [\n                30\n            ],\n            \"code\": [\n                58\n            ],\n            \"html\": [\n                58,\n                {\n                    \"theme\": [\n                        58\n                    ]\n                }\n            ],\n            \"language\": [\n                30\n            ],\n            \"__typename\": [\n                58\n            ]\n        },\n        \"BlockColor\": {\n            \"b\": [\n                36\n            ],\n            \"g\": [\n                36\n            ],\n            \"hex\": [\n                58\n            ],\n            \"hsl\": [\n                58\n            ],\n            \"r\": [\n                36\n            ],\n            \"rgb\": [\n                58\n            ],\n            \"__typename\": [\n                58\n            ]\n        },\n        \"BlockDocument\": {\n            \"_analyticsKey\": [\n                58,\n                {\n                    \"scope\": [\n                        0\n                    ]\n                }\n            ],\n            \"_dashboardUrl\": [\n                58\n            ],\n            \"_id\": [\n                58\n            ],\n            \"_idPath\": [\n                58\n            ],\n            \"_slug\": [\n                58\n            ],\n            \"_slugPath\": [\n                58\n            ],\n            \"_sys\": [\n                13\n            ],\n            \"_title\": [\n                58\n            ],\n            \"on_Authors\": [\n                1\n            ],\n            \"on_AuthorsItem\": [\n                2\n            ],\n            \"on_Blog\": [\n                20\n            ],\n            \"on_Categories\": [\n                26\n            ],\n            \"on_CategoriesItem\": [\n                27\n            ],\n            \"on_LegalPages\": [\n                38\n            ],\n            \"on_LegalPagesItem\": [\n                39\n            ],\n            \"on_Posts\": [\n                48\n            ],\n            \"on_PostsItem\": [\n                49\n            ],\n            \"on__AgentSTART\": [\n                65\n            ],\n            \"on_authorsItem_AsList\": [\n                75\n            ],\n            \"on_categoriesItem_AsList\": [\n                76\n            ],\n            \"on_legalPagesItem_AsList\": [\n                77\n            ],\n            \"on_postsItem_AsList\": [\n                78\n            ],\n            \"__typename\": [\n                58\n            ]\n        },\n        \"BlockDocumentSys\": {\n            \"apiNamePath\": [\n                58\n            ],\n            \"createdAt\": [\n                58\n            ],\n            \"hash\": [\n                58\n            ],\n            \"id\": [\n                35\n            ],\n            \"idPath\": [\n                58\n            ],\n            \"lastModifiedAt\": [\n                58\n            ],\n            \"slug\": [\n                58\n            ],\n            \"slugPath\": [\n                58\n            ],\n            \"title\": [\n                58\n            ],\n            \"__typename\": [\n                58\n            ]\n        },\n        \"BlockFile\": {\n            \"fileName\": [\n                58\n            ],\n            \"fileSize\": [\n                36\n            ],\n            \"lastModified\": [\n                33\n            ],\n            \"mimeType\": [\n                58\n            ],\n            \"url\": [\n                58\n            ],\n            \"__typename\": [\n                58\n            ]\n        },\n        \"BlockImage\": {\n            \"alt\": [\n                58\n            ],\n            \"aspectRatio\": [\n                58\n            ],\n            \"blurDataURL\": [\n                58\n            ],\n            \"fileName\": [\n                58\n            ],\n            \"fileSize\": [\n                36\n            ],\n            \"height\": [\n                36\n            ],\n            \"lastModified\": [\n                33\n            ],\n            \"mimeType\": [\n                58\n            ],\n            \"placeholderURL\": [\n                58\n            ],\n            \"rawUrl\": [\n                58\n            ],\n            \"thumbhash\": [\n                58\n            ],\n            \"url\": [\n                58,\n                {\n                    \"anim\": [\n                        58\n                    ],\n                    \"background\": [\n                        58\n                    ],\n                    \"blur\": [\n                        36\n                    ],\n                    \"border\": [\n                        58\n                    ],\n                    \"brightness\": [\n                        36\n                    ],\n                    \"compression\": [\n                        58\n                    ],\n                    \"contrast\": [\n                        36\n                    ],\n                    \"dpr\": [\n                        36\n                    ],\n                    \"fit\": [\n                        58\n                    ],\n                    \"format\": [\n                        58\n                    ],\n                    \"gamma\": [\n                        58\n                    ],\n                    \"gravity\": [\n                        58\n                    ],\n                    \"height\": [\n                        36\n                    ],\n                    \"metadata\": [\n                        58\n                    ],\n                    \"quality\": [\n                        36\n                    ],\n                    \"rotate\": [\n                        58\n                    ],\n                    \"sharpen\": [\n                        58\n                    ],\n                    \"trim\": [\n                        58\n                    ],\n                    \"width\": [\n                        36\n                    ]\n                }\n            ],\n            \"width\": [\n                36\n            ],\n            \"__typename\": [\n                58\n            ]\n        },\n        \"BlockList\": {\n            \"_analyticsKey\": [\n                58,\n                {\n                    \"scope\": [\n                        0\n                    ]\n                }\n            ],\n            \"_dashboardUrl\": [\n                58\n            ],\n            \"_id\": [\n                58\n            ],\n            \"_idPath\": [\n                58\n            ],\n            \"_meta\": [\n                43\n            ],\n            \"_searchKey\": [\n                58\n            ],\n            \"_slug\": [\n                58\n            ],\n            \"_slugPath\": [\n                58\n            ],\n            \"_sys\": [\n                13\n            ],\n            \"_title\": [\n                58\n            ],\n            \"on_Authors\": [\n                1\n            ],\n            \"on_Categories\": [\n                26\n            ],\n            \"on_LegalPages\": [\n                38\n            ],\n            \"on_Posts\": [\n                48\n            ],\n            \"on_authorsItem_AsList\": [\n                75\n            ],\n            \"on_categoriesItem_AsList\": [\n                76\n            ],\n            \"on_legalPagesItem_AsList\": [\n                77\n            ],\n            \"on_postsItem_AsList\": [\n                78\n            ],\n            \"__typename\": [\n                58\n            ]\n        },\n        \"BlockOgImage\": {\n            \"height\": [\n                36\n            ],\n            \"url\": [\n                58\n            ],\n            \"width\": [\n                36\n            ],\n            \"__typename\": [\n                58\n            ]\n        },\n        \"BlockRichText\": {\n            \"html\": [\n                58,\n                {\n                    \"slugs\": [\n                        25\n                    ],\n                    \"toc\": [\n                        25\n                    ]\n                }\n            ],\n            \"json\": [\n                56\n            ],\n            \"markdown\": [\n                58\n            ],\n            \"plainText\": [\n                58\n            ],\n            \"readingTime\": [\n                36,\n                {\n                    \"wpm\": [\n                        36\n                    ]\n                }\n            ],\n            \"on_Body\": [\n                21\n            ],\n            \"on_Body_1\": [\n                23\n            ],\n            \"__typename\": [\n                58\n            ]\n        },\n        \"BlockVideo\": {\n            \"aspectRatio\": [\n                58\n            ],\n            \"duration\": [\n                33\n            ],\n            \"fileName\": [\n                58\n            ],\n            \"fileSize\": [\n                36\n            ],\n            \"height\": [\n                36\n            ],\n            \"lastModified\": [\n                33\n            ],\n            \"mimeType\": [\n                58\n            ],\n            \"url\": [\n                58\n            ],\n            \"width\": [\n                36\n            ],\n            \"__typename\": [\n                58\n            ]\n        },\n        \"Blog\": {\n            \"_analyticsKey\": [\n                58,\n                {\n                    \"scope\": [\n                        0\n                    ]\n                }\n            ],\n            \"_dashboardUrl\": [\n                58\n            ],\n            \"_id\": [\n                58\n            ],\n            \"_idPath\": [\n                58\n            ],\n            \"_slug\": [\n                58\n            ],\n            \"_slugPath\": [\n                58\n            ],\n            \"_sys\": [\n                13\n            ],\n            \"_title\": [\n                58\n            ],\n            \"authors\": [\n                1,\n                {\n                    \"filter\": [\n                        3\n                    ],\n                    \"first\": [\n                        36\n                    ],\n                    \"orderBy\": [\n                        4\n                    ],\n                    \"skip\": [\n                        36\n                    ]\n                }\n            ],\n            \"categories\": [\n                26,\n                {\n                    \"filter\": [\n                        28\n                    ],\n                    \"first\": [\n                        36\n                    ],\n                    \"orderBy\": [\n                        29\n                    ],\n                    \"skip\": [\n                        36\n                    ]\n                }\n            ],\n            \"posts\": [\n                48,\n                {\n                    \"filter\": [\n                        50\n                    ],\n                    \"first\": [\n                        36\n                    ],\n                    \"orderBy\": [\n                        53\n                    ],\n                    \"skip\": [\n                        36\n                    ]\n                }\n            ],\n            \"__typename\": [\n                58\n            ]\n        },\n        \"Body\": {\n            \"html\": [\n                58,\n                {\n                    \"slugs\": [\n                        25\n                    ],\n                    \"toc\": [\n                        25\n                    ]\n                }\n            ],\n            \"json\": [\n                22\n            ],\n            \"markdown\": [\n                58\n            ],\n            \"plainText\": [\n                58\n            ],\n            \"readingTime\": [\n                36,\n                {\n                    \"wpm\": [\n                        36\n                    ]\n                }\n            ],\n            \"__typename\": [\n                58\n            ]\n        },\n        \"BodyRichText\": {\n            \"content\": [\n                6\n            ],\n            \"toc\": [\n                7\n            ],\n            \"__typename\": [\n                58\n            ]\n        },\n        \"Body_1\": {\n            \"html\": [\n                58,\n                {\n                    \"slugs\": [\n                        25\n                    ],\n                    \"toc\": [\n                        25\n                    ]\n                }\n            ],\n            \"json\": [\n                24\n            ],\n            \"markdown\": [\n                58\n            ],\n            \"plainText\": [\n                58\n            ],\n            \"readingTime\": [\n                36,\n                {\n                    \"wpm\": [\n                        36\n                    ]\n                }\n            ],\n            \"__typename\": [\n                58\n            ]\n        },\n        \"Body_1RichText\": {\n            \"content\": [\n                6\n            ],\n            \"toc\": [\n                7\n            ],\n            \"__typename\": [\n                58\n            ]\n        },\n        \"Boolean\": {},\n        \"Categories\": {\n            \"_analyticsKey\": [\n                58,\n                {\n                    \"scope\": [\n                        0\n                    ]\n                }\n            ],\n            \"_dashboardUrl\": [\n                58\n            ],\n            \"_id\": [\n                58\n            ],\n            \"_idPath\": [\n                58\n            ],\n            \"_meta\": [\n                43\n            ],\n            \"_searchKey\": [\n                58\n            ],\n            \"_slug\": [\n                58\n            ],\n            \"_slugPath\": [\n                58\n            ],\n            \"_sys\": [\n                13\n            ],\n            \"_title\": [\n                58\n            ],\n            \"item\": [\n                27\n            ],\n            \"items\": [\n                27\n            ],\n            \"__typename\": [\n                58\n            ]\n        },\n        \"CategoriesItem\": {\n            \"_analyticsKey\": [\n                58,\n                {\n                    \"scope\": [\n                        0\n                    ]\n                }\n            ],\n            \"_dashboardUrl\": [\n                58\n            ],\n            \"_id\": [\n                58\n            ],\n            \"_idPath\": [\n                58\n            ],\n            \"_slug\": [\n                58\n            ],\n            \"_slugPath\": [\n                58\n            ],\n            \"_sys\": [\n                13\n            ],\n            \"_title\": [\n                58\n            ],\n            \"__typename\": [\n                58\n            ]\n        },\n        \"CategoriesItemFilterInput\": {\n            \"AND\": [\n                28\n            ],\n            \"OR\": [\n                28\n            ],\n            \"_id\": [\n                59\n            ],\n            \"_slug\": [\n                59\n            ],\n            \"_sys_apiNamePath\": [\n                59\n            ],\n            \"_sys_createdAt\": [\n                31\n            ],\n            \"_sys_hash\": [\n                59\n            ],\n            \"_sys_id\": [\n                59\n            ],\n            \"_sys_idPath\": [\n                59\n            ],\n            \"_sys_lastModifiedAt\": [\n                31\n            ],\n            \"_sys_slug\": [\n                59\n            ],\n            \"_sys_slugPath\": [\n                59\n            ],\n            \"_sys_title\": [\n                59\n            ],\n            \"_title\": [\n                59\n            ],\n            \"__typename\": [\n                58\n            ]\n        },\n        \"CategoriesItemOrderByEnum\": {},\n        \"CodeSnippetLanguage\": {},\n        \"DateFilter\": {\n            \"eq\": [\n                32\n            ],\n            \"isAfter\": [\n                32\n            ],\n            \"isBefore\": [\n                32\n            ],\n            \"isNull\": [\n                25\n            ],\n            \"neq\": [\n                32\n            ],\n            \"onOrAfter\": [\n                32\n            ],\n            \"onOrBefore\": [\n                32\n            ],\n            \"__typename\": [\n                58\n            ]\n        },\n        \"DateTime\": {},\n        \"Float\": {},\n        \"GetUploadSignedURL\": {\n            \"signedURL\": [\n                58\n            ],\n            \"uploadURL\": [\n                58\n            ],\n            \"__typename\": [\n                58\n            ]\n        },\n        \"ID\": {},\n        \"Int\": {},\n        \"JSON\": {},\n        \"LegalPages\": {\n            \"_analyticsKey\": [\n                58,\n                {\n                    \"scope\": [\n                        0\n                    ]\n                }\n            ],\n            \"_dashboardUrl\": [\n                58\n            ],\n            \"_id\": [\n                58\n            ],\n            \"_idPath\": [\n                58\n            ],\n            \"_meta\": [\n                43\n            ],\n            \"_searchKey\": [\n                58\n            ],\n            \"_slug\": [\n                58\n            ],\n            \"_slugPath\": [\n                58\n            ],\n            \"_sys\": [\n                13\n            ],\n            \"_title\": [\n                58\n            ],\n            \"item\": [\n                39\n            ],\n            \"items\": [\n                39\n            ],\n            \"__typename\": [\n                58\n            ]\n        },\n        \"LegalPagesItem\": {\n            \"_analyticsKey\": [\n                58,\n                {\n                    \"scope\": [\n                        0\n                    ]\n                }\n            ],\n            \"_dashboardUrl\": [\n                58\n            ],\n            \"_id\": [\n                58\n            ],\n            \"_idPath\": [\n                58\n            ],\n            \"_slug\": [\n                58\n            ],\n            \"_slugPath\": [\n                58\n            ],\n            \"_sys\": [\n                13\n            ],\n            \"_title\": [\n                58\n            ],\n            \"body\": [\n                23\n            ],\n            \"description\": [\n                58\n            ],\n            \"__typename\": [\n                58\n            ]\n        },\n        \"LegalPagesItemFilterInput\": {\n            \"AND\": [\n                40\n            ],\n            \"OR\": [\n                40\n            ],\n            \"_id\": [\n                59\n            ],\n            \"_slug\": [\n                59\n            ],\n            \"_sys_apiNamePath\": [\n                59\n            ],\n            \"_sys_createdAt\": [\n                31\n            ],\n            \"_sys_hash\": [\n                59\n            ],\n            \"_sys_id\": [\n                59\n            ],\n            \"_sys_idPath\": [\n                59\n            ],\n            \"_sys_lastModifiedAt\": [\n                31\n            ],\n            \"_sys_slug\": [\n                59\n            ],\n            \"_sys_slugPath\": [\n                59\n            ],\n            \"_sys_title\": [\n                59\n            ],\n            \"_title\": [\n                59\n            ],\n            \"description\": [\n                59\n            ],\n            \"__typename\": [\n                58\n            ]\n        },\n        \"LegalPagesItemOrderByEnum\": {},\n        \"ListFilter\": {\n            \"isEmpty\": [\n                25\n            ],\n            \"length\": [\n                36\n            ],\n            \"__typename\": [\n                58\n            ]\n        },\n        \"ListMeta\": {\n            \"filteredCount\": [\n                36\n            ],\n            \"totalCount\": [\n                36\n            ],\n            \"__typename\": [\n                58\n            ]\n        },\n        \"MediaBlock\": {\n            \"fileName\": [\n                58\n            ],\n            \"fileSize\": [\n                36\n            ],\n            \"lastModified\": [\n                33\n            ],\n            \"mimeType\": [\n                58\n            ],\n            \"url\": [\n                58\n            ],\n            \"on_BlockAudio\": [\n                9\n            ],\n            \"on_BlockFile\": [\n                14\n            ],\n            \"on_BlockImage\": [\n                15\n            ],\n            \"on_BlockVideo\": [\n                19\n            ],\n            \"__typename\": [\n                58\n            ]\n        },\n        \"MediaBlockUnion\": {\n            \"on_BlockAudio\": [\n                9\n            ],\n            \"on_BlockFile\": [\n                14\n            ],\n            \"on_BlockImage\": [\n                15\n            ],\n            \"on_BlockVideo\": [\n                19\n            ],\n            \"on_MediaBlock\": [\n                44\n            ],\n            \"__typename\": [\n                58\n            ]\n        },\n        \"Mutation\": {\n            \"getUploadSignedURL\": [\n                34,\n                {\n                    \"fileHash\": [\n                        58\n                    ],\n                    \"fileName\": [\n                        58,\n                        \"String!\"\n                    ]\n                }\n            ],\n            \"transaction\": [\n                62,\n                {\n                    \"authorId\": [\n                        58\n                    ],\n                    \"autoCommit\": [\n                        58\n                    ],\n                    \"data\": [\n                        58,\n                        \"String!\"\n                    ],\n                    \"skipWorkflows\": [\n                        25\n                    ],\n                    \"timeout\": [\n                        36\n                    ]\n                }\n            ],\n            \"transactionAsync\": [\n                58,\n                {\n                    \"authorId\": [\n                        58\n                    ],\n                    \"autoCommit\": [\n                        58\n                    ],\n                    \"data\": [\n                        58,\n                        \"String!\"\n                    ],\n                    \"skipWorkflows\": [\n                        25\n                    ]\n                }\n            ],\n            \"transactionStatus\": [\n                62,\n                {\n                    \"id\": [\n                        58,\n                        \"String!\"\n                    ]\n                }\n            ],\n            \"__typename\": [\n                58\n            ]\n        },\n        \"NumberFilter\": {\n            \"eq\": [\n                33\n            ],\n            \"gt\": [\n                33\n            ],\n            \"gte\": [\n                33\n            ],\n            \"isNull\": [\n                25\n            ],\n            \"lt\": [\n                33\n            ],\n            \"lte\": [\n                33\n            ],\n            \"neq\": [\n                33\n            ],\n            \"__typename\": [\n                58\n            ]\n        },\n        \"Posts\": {\n            \"_analyticsKey\": [\n                58,\n                {\n                    \"scope\": [\n                        0\n                    ]\n                }\n            ],\n            \"_dashboardUrl\": [\n                58\n            ],\n            \"_id\": [\n                58\n            ],\n            \"_idPath\": [\n                58\n            ],\n            \"_meta\": [\n                43\n            ],\n            \"_searchKey\": [\n                58\n            ],\n            \"_slug\": [\n                58\n            ],\n            \"_slugPath\": [\n                58\n            ],\n            \"_sys\": [\n                13\n            ],\n            \"_title\": [\n                58\n            ],\n            \"item\": [\n                49\n            ],\n            \"items\": [\n                49\n            ],\n            \"__typename\": [\n                58\n            ]\n        },\n        \"PostsItem\": {\n            \"_analyticsKey\": [\n                58,\n                {\n                    \"scope\": [\n                        0\n                    ]\n                }\n            ],\n            \"_dashboardUrl\": [\n                58\n            ],\n            \"_id\": [\n                58\n            ],\n            \"_idPath\": [\n                58\n            ],\n            \"_slug\": [\n                58\n            ],\n            \"_slugPath\": [\n                58\n            ],\n            \"_sys\": [\n                13\n            ],\n            \"_title\": [\n                58\n            ],\n            \"authors\": [\n                2\n            ],\n            \"body\": [\n                21\n            ],\n            \"categories\": [\n                27\n            ],\n            \"date\": [\n                58\n            ],\n            \"description\": [\n                58\n            ],\n            \"image\": [\n                15\n            ],\n            \"__typename\": [\n                58\n            ]\n        },\n        \"PostsItemFilterInput\": {\n            \"AND\": [\n                50\n            ],\n            \"OR\": [\n                50\n            ],\n            \"_id\": [\n                59\n            ],\n            \"_slug\": [\n                59\n            ],\n            \"_sys_apiNamePath\": [\n                59\n            ],\n            \"_sys_createdAt\": [\n                31\n            ],\n            \"_sys_hash\": [\n                59\n            ],\n            \"_sys_id\": [\n                59\n            ],\n            \"_sys_idPath\": [\n                59\n            ],\n            \"_sys_lastModifiedAt\": [\n                31\n            ],\n            \"_sys_slug\": [\n                59\n            ],\n            \"_sys_slugPath\": [\n                59\n            ],\n            \"_sys_title\": [\n                59\n            ],\n            \"_title\": [\n                59\n            ],\n            \"authors\": [\n                51\n            ],\n            \"categories\": [\n                52\n            ],\n            \"date\": [\n                31\n            ],\n            \"description\": [\n                59\n            ],\n            \"__typename\": [\n                58\n            ]\n        },\n        \"PostsItemFilterInput__authors_0___untitled\": {\n            \"_id\": [\n                59\n            ],\n            \"_slug\": [\n                59\n            ],\n            \"_sys_apiNamePath\": [\n                59\n            ],\n            \"_sys_createdAt\": [\n                31\n            ],\n            \"_sys_hash\": [\n                59\n            ],\n            \"_sys_id\": [\n                59\n            ],\n            \"_sys_idPath\": [\n                59\n            ],\n            \"_sys_lastModifiedAt\": [\n                31\n            ],\n            \"_sys_slug\": [\n                59\n            ],\n            \"_sys_slugPath\": [\n                59\n            ],\n            \"_sys_title\": [\n                59\n            ],\n            \"_title\": [\n                59\n            ],\n            \"xUrl\": [\n                59\n            ],\n            \"__typename\": [\n                58\n            ]\n        },\n        \"PostsItemFilterInput__categories_0___untitled\": {\n            \"_id\": [\n                59\n            ],\n            \"_slug\": [\n                59\n            ],\n            \"_sys_apiNamePath\": [\n                59\n            ],\n            \"_sys_createdAt\": [\n                31\n            ],\n            \"_sys_hash\": [\n                59\n            ],\n            \"_sys_id\": [\n                59\n            ],\n            \"_sys_idPath\": [\n                59\n            ],\n            \"_sys_lastModifiedAt\": [\n                31\n            ],\n            \"_sys_slug\": [\n                59\n            ],\n            \"_sys_slugPath\": [\n                59\n            ],\n            \"_sys_title\": [\n                59\n            ],\n            \"_title\": [\n                59\n            ],\n            \"__typename\": [\n                58\n            ]\n        },\n        \"PostsItemOrderByEnum\": {},\n        \"Query\": {\n            \"_agent\": [\n                65,\n                {\n                    \"id\": [\n                        58,\n                        \"String!\"\n                    ]\n                }\n            ],\n            \"_agents\": [\n                73\n            ],\n            \"_componentInstances\": [\n                74\n            ],\n            \"_structure\": [\n                37,\n                {\n                    \"format\": [\n                        72\n                    ],\n                    \"resolveTargetsWith\": [\n                        71\n                    ],\n                    \"targetBlock\": [\n                        61\n                    ],\n                    \"withConstraints\": [\n                        25\n                    ],\n                    \"withIDs\": [\n                        25\n                    ],\n                    \"withTypeOptions\": [\n                        25\n                    ]\n                }\n            ],\n            \"_sys\": [\n                55\n            ],\n            \"blog\": [\n                20\n            ],\n            \"legalPages\": [\n                38,\n                {\n                    \"filter\": [\n                        40\n                    ],\n                    \"first\": [\n                        36\n                    ],\n                    \"orderBy\": [\n                        41\n                    ],\n                    \"skip\": [\n                        36\n                    ]\n                }\n            ],\n            \"__typename\": [\n                58\n            ]\n        },\n        \"RepoSys\": {\n            \"branches\": [\n                67,\n                {\n                    \"limit\": [\n                        36\n                    ],\n                    \"offset\": [\n                        36\n                    ]\n                }\n            ],\n            \"hash\": [\n                58\n            ],\n            \"id\": [\n                35\n            ],\n            \"playgroundInfo\": [\n                70\n            ],\n            \"slug\": [\n                58\n            ],\n            \"title\": [\n                58\n            ],\n            \"__typename\": [\n                58\n            ]\n        },\n        \"RichTextJson\": {\n            \"content\": [\n                6\n            ],\n            \"toc\": [\n                7\n            ],\n            \"on_BaseRichTextJson\": [\n                8\n            ],\n            \"on_BodyRichText\": [\n                22\n            ],\n            \"on_Body_1RichText\": [\n                24\n            ],\n            \"__typename\": [\n                58\n            ]\n        },\n        \"SelectFilter\": {\n            \"excludes\": [\n                58\n            ],\n            \"excludesAll\": [\n                58\n            ],\n            \"includes\": [\n                58\n            ],\n            \"includesAll\": [\n                58\n            ],\n            \"includesAny\": [\n                58\n            ],\n            \"isEmpty\": [\n                25\n            ],\n            \"__typename\": [\n                58\n            ]\n        },\n        \"String\": {},\n        \"StringFilter\": {\n            \"contains\": [\n                58\n            ],\n            \"endsWith\": [\n                58\n            ],\n            \"eq\": [\n                58\n            ],\n            \"in\": [\n                58\n            ],\n            \"isNull\": [\n                25\n            ],\n            \"matches\": [\n                60\n            ],\n            \"notEq\": [\n                58\n            ],\n            \"notIn\": [\n                58\n            ],\n            \"startsWith\": [\n                58\n            ],\n            \"__typename\": [\n                58\n            ]\n        },\n        \"StringMatchesFilter\": {\n            \"caseSensitive\": [\n                25\n            ],\n            \"pattern\": [\n                58\n            ],\n            \"__typename\": [\n                58\n            ]\n        },\n        \"TargetBlock\": {\n            \"focus\": [\n                25\n            ],\n            \"id\": [\n                58\n            ],\n            \"label\": [\n                58\n            ],\n            \"__typename\": [\n                58\n            ]\n        },\n        \"TransactionStatus\": {\n            \"duration\": [\n                36\n            ],\n            \"endedAt\": [\n                58\n            ],\n            \"id\": [\n                58\n            ],\n            \"message\": [\n                58\n            ],\n            \"startedAt\": [\n                58\n            ],\n            \"status\": [\n                63\n            ],\n            \"__typename\": [\n                58\n            ]\n        },\n        \"TransactionStatusEnum\": {},\n        \"Variant\": {\n            \"apiName\": [\n                58\n            ],\n            \"color\": [\n                58\n            ],\n            \"id\": [\n                58\n            ],\n            \"isDefault\": [\n                25\n            ],\n            \"label\": [\n                58\n            ],\n            \"__typename\": [\n                58\n            ]\n        },\n        \"_AgentSTART\": {\n            \"_agentKey\": [\n                58\n            ],\n            \"_analyticsKey\": [\n                58,\n                {\n                    \"scope\": [\n                        0\n                    ]\n                }\n            ],\n            \"_dashboardUrl\": [\n                58\n            ],\n            \"_id\": [\n                58\n            ],\n            \"_idPath\": [\n                58\n            ],\n            \"_slug\": [\n                58\n            ],\n            \"_slugPath\": [\n                58\n            ],\n            \"_sys\": [\n                13\n            ],\n            \"_title\": [\n                58\n            ],\n            \"accent\": [\n                58\n            ],\n            \"avatar\": [\n                58\n            ],\n            \"chatUrl\": [\n                58\n            ],\n            \"commit\": [\n                25\n            ],\n            \"description\": [\n                58\n            ],\n            \"edit\": [\n                25\n            ],\n            \"embedUrl\": [\n                58\n            ],\n            \"getUserInfo\": [\n                25\n            ],\n            \"grayscale\": [\n                58\n            ],\n            \"manageBranches\": [\n                25\n            ],\n            \"mcpUrl\": [\n                58\n            ],\n            \"model\": [\n                58\n            ],\n            \"searchTheWeb\": [\n                25\n            ],\n            \"slackInstallUrl\": [\n                58\n            ],\n            \"systemPrompt\": [\n                58\n            ],\n            \"__typename\": [\n                58\n            ]\n        },\n        \"_BranchInfo\": {\n            \"archivedAt\": [\n                58\n            ],\n            \"archivedBy\": [\n                58\n            ],\n            \"authorId\": [\n                58\n            ],\n            \"contributors\": [\n                58\n            ],\n            \"createdAt\": [\n                58\n            ],\n            \"description\": [\n                58\n            ],\n            \"git\": [\n                69\n            ],\n            \"headCommit\": [\n                68\n            ],\n            \"headCommitId\": [\n                58\n            ],\n            \"id\": [\n                35\n            ],\n            \"inlineSuggestionAppliedAt\": [\n                58\n            ],\n            \"isDefault\": [\n                25\n            ],\n            \"isInlineSuggestion\": [\n                25\n            ],\n            \"name\": [\n                58\n            ],\n            \"playgroundId\": [\n                58\n            ],\n            \"rollbackCommitId\": [\n                58\n            ],\n            \"rollbackIsoDate\": [\n                58\n            ],\n            \"sourceBranchId\": [\n                58\n            ],\n            \"updatedAt\": [\n                58\n            ],\n            \"workingRootBlockId\": [\n                58\n            ],\n            \"__typename\": [\n                58\n            ]\n        },\n        \"_Branches\": {\n            \"_meta\": [\n                43\n            ],\n            \"items\": [\n                66\n            ],\n            \"__typename\": [\n                58\n            ]\n        },\n        \"_CommitInfo\": {\n            \"authorId\": [\n                58\n            ],\n            \"branchId\": [\n                58\n            ],\n            \"contributors\": [\n                58\n            ],\n            \"createdAt\": [\n                58\n            ],\n            \"hash\": [\n                58\n            ],\n            \"id\": [\n                58\n            ],\n            \"mergeParentCommitId\": [\n                58\n            ],\n            \"message\": [\n                58\n            ],\n            \"parentCommitId\": [\n                58\n            ],\n            \"playgroundId\": [\n                58\n            ],\n            \"repoId\": [\n                58\n            ],\n            \"rootBlockId\": [\n                58\n            ],\n            \"__typename\": [\n                58\n            ]\n        },\n        \"_GitInfo\": {\n            \"branch\": [\n                58\n            ],\n            \"deploymentUrl\": [\n                58\n            ],\n            \"__typename\": [\n                58\n            ]\n        },\n        \"_PlaygroundInfo\": {\n            \"claimUrl\": [\n                58\n            ],\n            \"editUrl\": [\n                58\n            ],\n            \"expiresAt\": [\n                58\n            ],\n            \"id\": [\n                58\n            ],\n            \"__typename\": [\n                58\n            ]\n        },\n        \"_ResolveTargetsWithEnum\": {},\n        \"_StructureFormatEnum\": {},\n        \"_agents\": {\n            \"start\": [\n                65\n            ],\n            \"__typename\": [\n                58\n            ]\n        },\n        \"_components\": {\n            \"authorsItem\": [\n                75,\n                {\n                    \"filter\": [\n                        3\n                    ],\n                    \"first\": [\n                        36\n                    ],\n                    \"orderBy\": [\n                        4\n                    ],\n                    \"skip\": [\n                        36\n                    ]\n                }\n            ],\n            \"categoriesItem\": [\n                76,\n                {\n                    \"filter\": [\n                        28\n                    ],\n                    \"first\": [\n                        36\n                    ],\n                    \"orderBy\": [\n                        29\n                    ],\n                    \"skip\": [\n                        36\n                    ]\n                }\n            ],\n            \"legalPagesItem\": [\n                77,\n                {\n                    \"filter\": [\n                        40\n                    ],\n                    \"first\": [\n                        36\n                    ],\n                    \"orderBy\": [\n                        41\n                    ],\n                    \"skip\": [\n                        36\n                    ]\n                }\n            ],\n            \"postsItem\": [\n                78,\n                {\n                    \"filter\": [\n                        50\n                    ],\n                    \"first\": [\n                        36\n                    ],\n                    \"orderBy\": [\n                        53\n                    ],\n                    \"skip\": [\n                        36\n                    ]\n                }\n            ],\n            \"__typename\": [\n                58\n            ]\n        },\n        \"authorsItem_AsList\": {\n            \"_analyticsKey\": [\n                58,\n                {\n                    \"scope\": [\n                        0\n                    ]\n                }\n            ],\n            \"_dashboardUrl\": [\n                58\n            ],\n            \"_id\": [\n                58\n            ],\n            \"_idPath\": [\n                58\n            ],\n            \"_meta\": [\n                43\n            ],\n            \"_searchKey\": [\n                58\n            ],\n            \"_slug\": [\n                58\n            ],\n            \"_slugPath\": [\n                58\n            ],\n            \"_sys\": [\n                13\n            ],\n            \"_title\": [\n                58\n            ],\n            \"item\": [\n                2\n            ],\n            \"items\": [\n                2\n            ],\n            \"__typename\": [\n                58\n            ]\n        },\n        \"categoriesItem_AsList\": {\n            \"_analyticsKey\": [\n                58,\n                {\n                    \"scope\": [\n                        0\n                    ]\n                }\n            ],\n            \"_dashboardUrl\": [\n                58\n            ],\n            \"_id\": [\n                58\n            ],\n            \"_idPath\": [\n                58\n            ],\n            \"_meta\": [\n                43\n            ],\n            \"_searchKey\": [\n                58\n            ],\n            \"_slug\": [\n                58\n            ],\n            \"_slugPath\": [\n                58\n            ],\n            \"_sys\": [\n                13\n            ],\n            \"_title\": [\n                58\n            ],\n            \"item\": [\n                27\n            ],\n            \"items\": [\n                27\n            ],\n            \"__typename\": [\n                58\n            ]\n        },\n        \"legalPagesItem_AsList\": {\n            \"_analyticsKey\": [\n                58,\n                {\n                    \"scope\": [\n                        0\n                    ]\n                }\n            ],\n            \"_dashboardUrl\": [\n                58\n            ],\n            \"_id\": [\n                58\n            ],\n            \"_idPath\": [\n                58\n            ],\n            \"_meta\": [\n                43\n            ],\n            \"_searchKey\": [\n                58\n            ],\n            \"_slug\": [\n                58\n            ],\n            \"_slugPath\": [\n                58\n            ],\n            \"_sys\": [\n                13\n            ],\n            \"_title\": [\n                58\n            ],\n            \"item\": [\n                39\n            ],\n            \"items\": [\n                39\n            ],\n            \"__typename\": [\n                58\n            ]\n        },\n        \"postsItem_AsList\": {\n            \"_analyticsKey\": [\n                58,\n                {\n                    \"scope\": [\n                        0\n                    ]\n                }\n            ],\n            \"_dashboardUrl\": [\n                58\n            ],\n            \"_id\": [\n                58\n            ],\n            \"_idPath\": [\n                58\n            ],\n            \"_meta\": [\n                43\n            ],\n            \"_searchKey\": [\n                58\n            ],\n            \"_slug\": [\n                58\n            ],\n            \"_slugPath\": [\n                58\n            ],\n            \"_sys\": [\n                13\n            ],\n            \"_title\": [\n                58\n            ],\n            \"item\": [\n                49\n            ],\n            \"items\": [\n                49\n            ],\n            \"__typename\": [\n                58\n            ]\n        }\n    }\n}"], "names": [], "mappings": "AAAA,0HAA0H;AAE1H,kBAAkB,GAClB,wDAAwD,GACxD,kBAAkB,GAClB,cAAc;;;;uCAEC;IACX,WAAW;QACP;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACH;IACD,SAAS;QACL,qBAAqB,CAAC;QACtB,WAAW;YACP,iBAAiB;gBACb;gBACA;oBACI,SAAS;wBACL;qBACH;gBACL;aACH;YACD,iBAAiB;gBACb;aACH;YACD,OAAO;gBACH;aACH;YACD,WAAW;gBACP;aACH;YACD,SAAS;gBACL;aACH;YACD,cAAc;gBACV;aACH;YACD,SAAS;gBACL;aACH;YACD,aAAa;gBACT;aACH;YACD,QAAQ;gBACJ;aACH;YACD,UAAU;gBACN;aACH;YACD,QAAQ;gBACJ;aACH;YACD,SAAS;gBACL;aACH;YACD,cAAc;gBACV;aACH;QACL;QACA,eAAe;YACX,iBAAiB;gBACb;gBACA;oBACI,SAAS;wBACL;qBACH;gBACL;aACH;YACD,iBAAiB;gBACb;aACH;YACD,OAAO;gBACH;aACH;YACD,WAAW;gBACP;aACH;YACD,SAAS;gBACL;aACH;YACD,aAAa;gBACT;aACH;YACD,QAAQ;gBACJ;aACH;YACD,UAAU;gBACN;aACH;YACD,UAAU;gBACN;aACH;YACD,QAAQ;gBACJ;aACH;YACD,cAAc;gBACV;aACH;QACL;QACA,0BAA0B;YACtB,OAAO;gBACH;aACH;YACD,MAAM;gBACF;aACH;YACD,OAAO;gBACH;aACH;YACD,SAAS;gBACL;aACH;YACD,oBAAoB;gBAChB;aACH;YACD,kBAAkB;gBACd;aACH;YACD,aAAa;gBACT;aACH;YACD,WAAW;gBACP;aACH;YACD,eAAe;gBACX;aACH;YACD,uBAAuB;gBACnB;aACH;YACD,aAAa;gBACT;aACH;YACD,iBAAiB;gBACb;aACH;YACD,cAAc;gBACV;aACH;YACD,UAAU;gBACN;aACH;YACD,QAAQ;gBACJ;aACH;YACD,cAAc;gBACV;aACH;QACL;QACA,0BAA0B,CAAC;QAC3B,mBAAmB,CAAC;QACpB,6BAA6B,CAAC;QAC9B,yBAAyB,CAAC;QAC1B,oBAAoB;YAChB,UAAU;gBACN;aACH;YACD,WAAW;gBACP;aACH;YACD,OAAO;gBACH;aACH;YACD,cAAc;gBACV;aACH;QACL;QACA,cAAc;YACV,YAAY;gBACR;aACH;YACD,YAAY;gBACR;aACH;YACD,YAAY;gBACR;aACH;YACD,gBAAgB;gBACZ;aACH;YACD,YAAY;gBACR;aACH;YACD,OAAO;gBACH;aACH;YACD,cAAc;gBACV;aACH;QACL;QACA,oBAAoB;YAChB,oBAAoB;gBAChB;aACH;YACD,QAAQ;gBACJ;aACH;YACD,QAAQ;gBACJ;gBACA;oBACI,SAAS;wBACL;qBACH;gBACL;aACH;YACD,YAAY;gBACR;aACH;YACD,cAAc;gBACV;aACH;QACL;QACA,cAAc;YACV,KAAK;gBACD;aACH;YACD,KAAK;gBACD;aACH;YACD,OAAO;gBACH;aACH;YACD,OAAO;gBACH;aACH;YACD,KAAK;gBACD;aACH;YACD,OAAO;gBACH;aACH;YACD,cAAc;gBACV;aACH;QACL;QACA,iBAAiB;YACb,iBAAiB;gBACb;gBACA;oBACI,SAAS;wBACL;qBACH;gBACL;aACH;YACD,iBAAiB;gBACb;aACH;YACD,OAAO;gBACH;aACH;YACD,WAAW;gBACP;aACH;YACD,SAAS;gBACL;aACH;YACD,aAAa;gBACT;aACH;YACD,QAAQ;gBACJ;aACH;YACD,UAAU;gBACN;aACH;YACD,cAAc;gBACV;aACH;YACD,kBAAkB;gBACd;aACH;YACD,WAAW;gBACP;aACH;YACD,iBAAiB;gBACb;aACH;YACD,qBAAqB;gBACjB;aACH;YACD,iBAAiB;gBACb;aACH;YACD,qBAAqB;gBACjB;aACH;YACD,YAAY;gBACR;aACH;YACD,gBAAgB;gBACZ;aACH;YACD,kBAAkB;gBACd;aACH;YACD,yBAAyB;gBACrB;aACH;YACD,4BAA4B;gBACxB;aACH;YACD,4BAA4B;gBACxB;aACH;YACD,uBAAuB;gBACnB;aACH;YACD,cAAc;gBACV;aACH;QACL;QACA,oBAAoB;YAChB,eAAe;gBACX;aACH;YACD,aAAa;gBACT;aACH;YACD,QAAQ;gBACJ;aACH;YACD,MAAM;gBACF;aACH;YACD,UAAU;gBACN;aACH;YACD,kBAAkB;gBACd;aACH;YACD,QAAQ;gBACJ;aACH;YACD,YAAY;gBACR;aACH;YACD,SAAS;gBACL;aACH;YACD,cAAc;gBACV;aACH;QACL;QACA,aAAa;YACT,YAAY;gBACR;aACH;YACD,YAAY;gBACR;aACH;YACD,gBAAgB;gBACZ;aACH;YACD,YAAY;gBACR;aACH;YACD,OAAO;gBACH;aACH;YACD,cAAc;gBACV;aACH;QACL;QACA,cAAc;YACV,OAAO;gBACH;aACH;YACD,eAAe;gBACX;aACH;YACD,eAAe;gBACX;aACH;YACD,YAAY;gBACR;aACH;YACD,YAAY;gBACR;aACH;YACD,UAAU;gBACN;aACH;YACD,gBAAgB;gBACZ;aACH;YACD,YAAY;gBACR;aACH;YACD,kBAAkB;gBACd;aACH;YACD,UAAU;gBACN;aACH;YACD,aAAa;gBACT;aACH;YACD,OAAO;gBACH;gBACA;oBACI,QAAQ;wBACJ;qBACH;oBACD,cAAc;wBACV;qBACH;oBACD,QAAQ;wBACJ;qBACH;oBACD,UAAU;wBACN;qBACH;oBACD,cAAc;wBACV;qBACH;oBACD,eAAe;wBACX;qBACH;oBACD,YAAY;wBACR;qBACH;oBACD,OAAO;wBACH;qBACH;oBACD,OAAO;wBACH;qBACH;oBACD,UAAU;wBACN;qBACH;oBACD,SAAS;wBACL;qBACH;oBACD,WAAW;wBACP;qBACH;oBACD,UAAU;wBACN;qBACH;oBACD,YAAY;wBACR;qBACH;oBACD,WAAW;wBACP;qBACH;oBACD,UAAU;wBACN;qBACH;oBACD,WAAW;wBACP;qBACH;oBACD,QAAQ;wBACJ;qBACH;oBACD,SAAS;wBACL;qBACH;gBACL;aACH;YACD,SAAS;gBACL;aACH;YACD,cAAc;gBACV;aACH;QACL;QACA,aAAa;YACT,iBAAiB;gBACb;gBACA;oBACI,SAAS;wBACL;qBACH;gBACL;aACH;YACD,iBAAiB;gBACb;aACH;YACD,OAAO;gBACH;aACH;YACD,WAAW;gBACP;aACH;YACD,SAAS;gBACL;aACH;YACD,cAAc;gBACV;aACH;YACD,SAAS;gBACL;aACH;YACD,aAAa;gBACT;aACH;YACD,QAAQ;gBACJ;aACH;YACD,UAAU;gBACN;aACH;YACD,cAAc;gBACV;aACH;YACD,iBAAiB;gBACb;aACH;YACD,iBAAiB;gBACb;aACH;YACD,YAAY;gBACR;aACH;YACD,yBAAyB;gBACrB;aACH;YACD,4BAA4B;gBACxB;aACH;YACD,4BAA4B;gBACxB;aACH;YACD,uBAAuB;gBACnB;aACH;YACD,cAAc;gBACV;aACH;QACL;QACA,gBAAgB;YACZ,UAAU;gBACN;aACH;YACD,OAAO;gBACH;aACH;YACD,SAAS;gBACL;aACH;YACD,cAAc;gBACV;aACH;QACL;QACA,iBAAiB;YACb,QAAQ;gBACJ;gBACA;oBACI,SAAS;wBACL;qBACH;oBACD,OAAO;wBACH;qBACH;gBACL;aACH;YACD,QAAQ;gBACJ;aACH;YACD,YAAY;gBACR;aACH;YACD,aAAa;gBACT;aACH;YACD,eAAe;gBACX;gBACA;oBACI,OAAO;wBACH;qBACH;gBACL;aACH;YACD,WAAW;gBACP;aACH;YACD,aAAa;gBACT;aACH;YACD,cAAc;gBACV;aACH;QACL;QACA,cAAc;YACV,eAAe;gBACX;aACH;YACD,YAAY;gBACR;aACH;YACD,YAAY;gBACR;aACH;YACD,YAAY;gBACR;aACH;YACD,UAAU;gBACN;aACH;YACD,gBAAgB;gBACZ;aACH;YACD,YAAY;gBACR;aACH;YACD,OAAO;gBACH;aACH;YACD,SAAS;gBACL;aACH;YACD,cAAc;gBACV;aACH;QACL;QACA,QAAQ;YACJ,iBAAiB;gBACb;gBACA;oBACI,SAAS;wBACL;qBACH;gBACL;aACH;YACD,iBAAiB;gBACb;aACH;YACD,OAAO;gBACH;aACH;YACD,WAAW;gBACP;aACH;YACD,SAAS;gBACL;aACH;YACD,aAAa;gBACT;aACH;YACD,QAAQ;gBACJ;aACH;YACD,UAAU;gBACN;aACH;YACD,WAAW;gBACP;gBACA;oBACI,UAAU;wBACN;qBACH;oBACD,SAAS;wBACL;qBACH;oBACD,WAAW;wBACP;qBACH;oBACD,QAAQ;wBACJ;qBACH;gBACL;aACH;YACD,cAAc;gBACV;gBACA;oBACI,UAAU;wBACN;qBACH;oBACD,SAAS;wBACL;qBACH;oBACD,WAAW;wBACP;qBACH;oBACD,QAAQ;wBACJ;qBACH;gBACL;aACH;YACD,SAAS;gBACL;gBACA;oBACI,UAAU;wBACN;qBACH;oBACD,SAAS;wBACL;qBACH;oBACD,WAAW;wBACP;qBACH;oBACD,QAAQ;wBACJ;qBACH;gBACL;aACH;YACD,cAAc;gBACV;aACH;QACL;QACA,QAAQ;YACJ,QAAQ;gBACJ;gBACA;oBACI,SAAS;wBACL;qBACH;oBACD,OAAO;wBACH;qBACH;gBACL;aACH;YACD,QAAQ;gBACJ;aACH;YACD,YAAY;gBACR;aACH;YACD,aAAa;gBACT;aACH;YACD,eAAe;gBACX;gBACA;oBACI,OAAO;wBACH;qBACH;gBACL;aACH;YACD,cAAc;gBACV;aACH;QACL;QACA,gBAAgB;YACZ,WAAW;gBACP;aACH;YACD,OAAO;gBACH;aACH;YACD,cAAc;gBACV;aACH;QACL;QACA,UAAU;YACN,QAAQ;gBACJ;gBACA;oBACI,SAAS;wBACL;qBACH;oBACD,OAAO;wBACH;qBACH;gBACL;aACH;YACD,QAAQ;gBACJ;aACH;YACD,YAAY;gBACR;aACH;YACD,aAAa;gBACT;aACH;YACD,eAAe;gBACX;gBACA;oBACI,OAAO;wBACH;qBACH;gBACL;aACH;YACD,cAAc;gBACV;aACH;QACL;QACA,kBAAkB;YACd,WAAW;gBACP;aACH;YACD,OAAO;gBACH;aACH;YACD,cAAc;gBACV;aACH;QACL;QACA,WAAW,CAAC;QACZ,cAAc;YACV,iBAAiB;gBACb;gBACA;oBACI,SAAS;wBACL;qBACH;gBACL;aACH;YACD,iBAAiB;gBACb;aACH;YACD,OAAO;gBACH;aACH;YACD,WAAW;gBACP;aACH;YACD,SAAS;gBACL;aACH;YACD,cAAc;gBACV;aACH;YACD,SAAS;gBACL;aACH;YACD,aAAa;gBACT;aACH;YACD,QAAQ;gBACJ;aACH;YACD,UAAU;gBACN;aACH;YACD,QAAQ;gBACJ;aACH;YACD,SAAS;gBACL;aACH;YACD,cAAc;gBACV;aACH;QACL;QACA,kBAAkB;YACd,iBAAiB;gBACb;gBACA;oBACI,SAAS;wBACL;qBACH;gBACL;aACH;YACD,iBAAiB;gBACb;aACH;YACD,OAAO;gBACH;aACH;YACD,WAAW;gBACP;aACH;YACD,SAAS;gBACL;aACH;YACD,aAAa;gBACT;aACH;YACD,QAAQ;gBACJ;aACH;YACD,UAAU;gBACN;aACH;YACD,cAAc;gBACV;aACH;QACL;QACA,6BAA6B;YACzB,OAAO;gBACH;aACH;YACD,MAAM;gBACF;aACH;YACD,OAAO;gBACH;aACH;YACD,SAAS;gBACL;aACH;YACD,oBAAoB;gBAChB;aACH;YACD,kBAAkB;gBACd;aACH;YACD,aAAa;gBACT;aACH;YACD,WAAW;gBACP;aACH;YACD,eAAe;gBACX;aACH;YACD,uBAAuB;gBACnB;aACH;YACD,aAAa;gBACT;aACH;YACD,iBAAiB;gBACb;aACH;YACD,cAAc;gBACV;aACH;YACD,UAAU;gBACN;aACH;YACD,cAAc;gBACV;aACH;QACL;QACA,6BAA6B,CAAC;QAC9B,uBAAuB,CAAC;QACxB,cAAc;YACV,MAAM;gBACF;aACH;YACD,WAAW;gBACP;aACH;YACD,YAAY;gBACR;aACH;YACD,UAAU;gBACN;aACH;YACD,OAAO;gBACH;aACH;YACD,aAAa;gBACT;aACH;YACD,cAAc;gBACV;aACH;YACD,cAAc;gBACV;aACH;QACL;QACA,YAAY,CAAC;QACb,SAAS,CAAC;QACV,sBAAsB;YAClB,aAAa;gBACT;aACH;YACD,aAAa;gBACT;aACH;YACD,cAAc;gBACV;aACH;QACL;QACA,MAAM,CAAC;QACP,OAAO,CAAC;QACR,QAAQ,CAAC;QACT,cAAc;YACV,iBAAiB;gBACb;gBACA;oBACI,SAAS;wBACL;qBACH;gBACL;aACH;YACD,iBAAiB;gBACb;aACH;YACD,OAAO;gBACH;aACH;YACD,WAAW;gBACP;aACH;YACD,SAAS;gBACL;aACH;YACD,cAAc;gBACV;aACH;YACD,SAAS;gBACL;aACH;YACD,aAAa;gBACT;aACH;YACD,QAAQ;gBACJ;aACH;YACD,UAAU;gBACN;aACH;YACD,QAAQ;gBACJ;aACH;YACD,SAAS;gBACL;aACH;YACD,cAAc;gBACV;aACH;QACL;QACA,kBAAkB;YACd,iBAAiB;gBACb;gBACA;oBACI,SAAS;wBACL;qBACH;gBACL;aACH;YACD,iBAAiB;gBACb;aACH;YACD,OAAO;gBACH;aACH;YACD,WAAW;gBACP;aACH;YACD,SAAS;gBACL;aACH;YACD,aAAa;gBACT;aACH;YACD,QAAQ;gBACJ;aACH;YACD,UAAU;gBACN;aACH;YACD,QAAQ;gBACJ;aACH;YACD,eAAe;gBACX;aACH;YACD,cAAc;gBACV;aACH;QACL;QACA,6BAA6B;YACzB,OAAO;gBACH;aACH;YACD,MAAM;gBACF;aACH;YACD,OAAO;gBACH;aACH;YACD,SAAS;gBACL;aACH;YACD,oBAAoB;gBAChB;aACH;YACD,kBAAkB;gBACd;aACH;YACD,aAAa;gBACT;aACH;YACD,WAAW;gBACP;aACH;YACD,eAAe;gBACX;aACH;YACD,uBAAuB;gBACnB;aACH;YACD,aAAa;gBACT;aACH;YACD,iBAAiB;gBACb;aACH;YACD,cAAc;gBACV;aACH;YACD,UAAU;gBACN;aACH;YACD,eAAe;gBACX;aACH;YACD,cAAc;gBACV;aACH;QACL;QACA,6BAA6B,CAAC;QAC9B,cAAc;YACV,WAAW;gBACP;aACH;YACD,UAAU;gBACN;aACH;YACD,cAAc;gBACV;aACH;QACL;QACA,YAAY;YACR,iBAAiB;gBACb;aACH;YACD,cAAc;gBACV;aACH;YACD,cAAc;gBACV;aACH;QACL;QACA,cAAc;YACV,YAAY;gBACR;aACH;YACD,YAAY;gBACR;aACH;YACD,gBAAgB;gBACZ;aACH;YACD,YAAY;gBACR;aACH;YACD,OAAO;gBACH;aACH;YACD,iBAAiB;gBACb;aACH;YACD,gBAAgB;gBACZ;aACH;YACD,iBAAiB;gBACb;aACH;YACD,iBAAiB;gBACb;aACH;YACD,cAAc;gBACV;aACH;QACL;QACA,mBAAmB;YACf,iBAAiB;gBACb;aACH;YACD,gBAAgB;gBACZ;aACH;YACD,iBAAiB;gBACb;aACH;YACD,iBAAiB;gBACb;aACH;YACD,iBAAiB;gBACb;aACH;YACD,cAAc;gBACV;aACH;QACL;QACA,YAAY;YACR,sBAAsB;gBAClB;gBACA;oBACI,YAAY;wBACR;qBACH;oBACD,YAAY;wBACR;wBACA;qBACH;gBACL;aACH;YACD,eAAe;gBACX;gBACA;oBACI,YAAY;wBACR;qBACH;oBACD,cAAc;wBACV;qBACH;oBACD,QAAQ;wBACJ;wBACA;qBACH;oBACD,iBAAiB;wBACb;qBACH;oBACD,WAAW;wBACP;qBACH;gBACL;aACH;YACD,oBAAoB;gBAChB;gBACA;oBACI,YAAY;wBACR;qBACH;oBACD,cAAc;wBACV;qBACH;oBACD,QAAQ;wBACJ;wBACA;qBACH;oBACD,iBAAiB;wBACb;qBACH;gBACL;aACH;YACD,qBAAqB;gBACjB;gBACA;oBACI,MAAM;wBACF;wBACA;qBACH;gBACL;aACH;YACD,cAAc;gBACV;aACH;QACL;QACA,gBAAgB;YACZ,MAAM;gBACF;aACH;YACD,MAAM;gBACF;aACH;YACD,OAAO;gBACH;aACH;YACD,UAAU;gBACN;aACH;YACD,MAAM;gBACF;aACH;YACD,OAAO;gBACH;aACH;YACD,OAAO;gBACH;aACH;YACD,cAAc;gBACV;aACH;QACL;QACA,SAAS;YACL,iBAAiB;gBACb;gBACA;oBACI,SAAS;wBACL;qBACH;gBACL;aACH;YACD,iBAAiB;gBACb;aACH;YACD,OAAO;gBACH;aACH;YACD,WAAW;gBACP;aACH;YACD,SAAS;gBACL;aACH;YACD,cAAc;gBACV;aACH;YACD,SAAS;gBACL;aACH;YACD,aAAa;gBACT;aACH;YACD,QAAQ;gBACJ;aACH;YACD,UAAU;gBACN;aACH;YACD,QAAQ;gBACJ;aACH;YACD,SAAS;gBACL;aACH;YACD,cAAc;gBACV;aACH;QACL;QACA,aAAa;YACT,iBAAiB;gBACb;gBACA;oBACI,SAAS;wBACL;qBACH;gBACL;aACH;YACD,iBAAiB;gBACb;aACH;YACD,OAAO;gBACH;aACH;YACD,WAAW;gBACP;aACH;YACD,SAAS;gBACL;aACH;YACD,aAAa;gBACT;aACH;YACD,QAAQ;gBACJ;aACH;YACD,UAAU;gBACN;aACH;YACD,WAAW;gBACP;aACH;YACD,QAAQ;gBACJ;aACH;YACD,cAAc;gBACV;aACH;YACD,QAAQ;gBACJ;aACH;YACD,eAAe;gBACX;aACH;YACD,SAAS;gBACL;aACH;YACD,cAAc;gBACV;aACH;QACL;QACA,wBAAwB;YACpB,OAAO;gBACH;aACH;YACD,MAAM;gBACF;aACH;YACD,OAAO;gBACH;aACH;YACD,SAAS;gBACL;aACH;YACD,oBAAoB;gBAChB;aACH;YACD,kBAAkB;gBACd;aACH;YACD,aAAa;gBACT;aACH;YACD,WAAW;gBACP;aACH;YACD,eAAe;gBACX;aACH;YACD,uBAAuB;gBACnB;aACH;YACD,aAAa;gBACT;aACH;YACD,iBAAiB;gBACb;aACH;YACD,cAAc;gBACV;aACH;YACD,UAAU;gBACN;aACH;YACD,WAAW;gBACP;aACH;YACD,cAAc;gBACV;aACH;YACD,QAAQ;gBACJ;aACH;YACD,eAAe;gBACX;aACH;YACD,cAAc;gBACV;aACH;QACL;QACA,8CAA8C;YAC1C,OAAO;gBACH;aACH;YACD,SAAS;gBACL;aACH;YACD,oBAAoB;gBAChB;aACH;YACD,kBAAkB;gBACd;aACH;YACD,aAAa;gBACT;aACH;YACD,WAAW;gBACP;aACH;YACD,eAAe;gBACX;aACH;YACD,uBAAuB;gBACnB;aACH;YACD,aAAa;gBACT;aACH;YACD,iBAAiB;gBACb;aACH;YACD,cAAc;gBACV;aACH;YACD,UAAU;gBACN;aACH;YACD,QAAQ;gBACJ;aACH;YACD,cAAc;gBACV;aACH;QACL;QACA,iDAAiD;YAC7C,OAAO;gBACH;aACH;YACD,SAAS;gBACL;aACH;YACD,oBAAoB;gBAChB;aACH;YACD,kBAAkB;gBACd;aACH;YACD,aAAa;gBACT;aACH;YACD,WAAW;gBACP;aACH;YACD,eAAe;gBACX;aACH;YACD,uBAAuB;gBACnB;aACH;YACD,aAAa;gBACT;aACH;YACD,iBAAiB;gBACb;aACH;YACD,cAAc;gBACV;aACH;YACD,UAAU;gBACN;aACH;YACD,cAAc;gBACV;aACH;QACL;QACA,wBAAwB,CAAC;QACzB,SAAS;YACL,UAAU;gBACN;gBACA;oBACI,MAAM;wBACF;wBACA;qBACH;gBACL;aACH;YACD,WAAW;gBACP;aACH;YACD,uBAAuB;gBACnB;aACH;YACD,cAAc;gBACV;gBACA;oBACI,UAAU;wBACN;qBACH;oBACD,sBAAsB;wBAClB;qBACH;oBACD,eAAe;wBACX;qBACH;oBACD,mBAAmB;wBACf;qBACH;oBACD,WAAW;wBACP;qBACH;oBACD,mBAAmB;wBACf;qBACH;gBACL;aACH;YACD,QAAQ;gBACJ;aACH;YACD,QAAQ;gBACJ;aACH;YACD,cAAc;gBACV;gBACA;oBACI,UAAU;wBACN;qBACH;oBACD,SAAS;wBACL;qBACH;oBACD,WAAW;wBACP;qBACH;oBACD,QAAQ;wBACJ;qBACH;gBACL;aACH;YACD,cAAc;gBACV;aACH;QACL;QACA,WAAW;YACP,YAAY;gBACR;gBACA;oBACI,SAAS;wBACL;qBACH;oBACD,UAAU;wBACN;qBACH;gBACL;aACH;YACD,QAAQ;gBACJ;aACH;YACD,MAAM;gBACF;aACH;YACD,kBAAkB;gBACd;aACH;YACD,QAAQ;gBACJ;aACH;YACD,SAAS;gBACL;aACH;YACD,cAAc;gBACV;aACH;QACL;QACA,gBAAgB;YACZ,WAAW;gBACP;aACH;YACD,OAAO;gBACH;aACH;YACD,uBAAuB;gBACnB;aACH;YACD,mBAAmB;gBACf;aACH;YACD,qBAAqB;gBACjB;aACH;YACD,cAAc;gBACV;aACH;QACL;QACA,gBAAgB;YACZ,YAAY;gBACR;aACH;YACD,eAAe;gBACX;aACH;YACD,YAAY;gBACR;aACH;YACD,eAAe;gBACX;aACH;YACD,eAAe;gBACX;aACH;YACD,WAAW;gBACP;aACH;YACD,cAAc;gBACV;aACH;QACL;QACA,UAAU,CAAC;QACX,gBAAgB;YACZ,YAAY;gBACR;aACH;YACD,YAAY;gBACR;aACH;YACD,MAAM;gBACF;aACH;YACD,MAAM;gBACF;aACH;YACD,UAAU;gBACN;aACH;YACD,WAAW;gBACP;aACH;YACD,SAAS;gBACL;aACH;YACD,SAAS;gBACL;aACH;YACD,cAAc;gBACV;aACH;YACD,cAAc;gBACV;aACH;QACL;QACA,uBAAuB;YACnB,iBAAiB;gBACb;aACH;YACD,WAAW;gBACP;aACH;YACD,cAAc;gBACV;aACH;QACL;QACA,eAAe;YACX,SAAS;gBACL;aACH;YACD,MAAM;gBACF;aACH;YACD,SAAS;gBACL;aACH;YACD,cAAc;gBACV;aACH;QACL;QACA,qBAAqB;YACjB,YAAY;gBACR;aACH;YACD,WAAW;gBACP;aACH;YACD,MAAM;gBACF;aACH;YACD,WAAW;gBACP;aACH;YACD,aAAa;gBACT;aACH;YACD,UAAU;gBACN;aACH;YACD,cAAc;gBACV;aACH;QACL;QACA,yBAAyB,CAAC;QAC1B,WAAW;YACP,WAAW;gBACP;aACH;YACD,SAAS;gBACL;aACH;YACD,MAAM;gBACF;aACH;YACD,aAAa;gBACT;aACH;YACD,SAAS;gBACL;aACH;YACD,cAAc;gBACV;aACH;QACL;QACA,eAAe;YACX,aAAa;gBACT;aACH;YACD,iBAAiB;gBACb;gBACA;oBACI,SAAS;wBACL;qBACH;gBACL;aACH;YACD,iBAAiB;gBACb;aACH;YACD,OAAO;gBACH;aACH;YACD,WAAW;gBACP;aACH;YACD,SAAS;gBACL;aACH;YACD,aAAa;gBACT;aACH;YACD,QAAQ;gBACJ;aACH;YACD,UAAU;gBACN;aACH;YACD,UAAU;gBACN;aACH;YACD,UAAU;gBACN;aACH;YACD,WAAW;gBACP;aACH;YACD,UAAU;gBACN;aACH;YACD,eAAe;gBACX;aACH;YACD,QAAQ;gBACJ;aACH;YACD,YAAY;gBACR;aACH;YACD,eAAe;gBACX;aACH;YACD,aAAa;gBACT;aACH;YACD,kBAAkB;gBACd;aACH;YACD,UAAU;gBACN;aACH;YACD,SAAS;gBACL;aACH;YACD,gBAAgB;gBACZ;aACH;YACD,mBAAmB;gBACf;aACH;YACD,gBAAgB;gBACZ;aACH;YACD,cAAc;gBACV;aACH;QACL;QACA,eAAe;YACX,cAAc;gBACV;aACH;YACD,cAAc;gBACV;aACH;YACD,YAAY;gBACR;aACH;YACD,gBAAgB;gBACZ;aACH;YACD,aAAa;gBACT;aACH;YACD,eAAe;gBACX;aACH;YACD,OAAO;gBACH;aACH;YACD,cAAc;gBACV;aACH;YACD,gBAAgB;gBACZ;aACH;YACD,MAAM;gBACF;aACH;YACD,6BAA6B;gBACzB;aACH;YACD,aAAa;gBACT;aACH;YACD,sBAAsB;gBAClB;aACH;YACD,QAAQ;gBACJ;aACH;YACD,gBAAgB;gBACZ;aACH;YACD,oBAAoB;gBAChB;aACH;YACD,mBAAmB;gBACf;aACH;YACD,kBAAkB;gBACd;aACH;YACD,aAAa;gBACT;aACH;YACD,sBAAsB;gBAClB;aACH;YACD,cAAc;gBACV;aACH;QACL;QACA,aAAa;YACT,SAAS;gBACL;aACH;YACD,SAAS;gBACL;aACH;YACD,cAAc;gBACV;aACH;QACL;QACA,eAAe;YACX,YAAY;gBACR;aACH;YACD,YAAY;gBACR;aACH;YACD,gBAAgB;gBACZ;aACH;YACD,aAAa;gBACT;aACH;YACD,QAAQ;gBACJ;aACH;YACD,MAAM;gBACF;aACH;YACD,uBAAuB;gBACnB;aACH;YACD,WAAW;gBACP;aACH;YACD,kBAAkB;gBACd;aACH;YACD,gBAAgB;gBACZ;aACH;YACD,UAAU;gBACN;aACH;YACD,eAAe;gBACX;aACH;YACD,cAAc;gBACV;aACH;QACL;QACA,YAAY;YACR,UAAU;gBACN;aACH;YACD,iBAAiB;gBACb;aACH;YACD,cAAc;gBACV;aACH;QACL;QACA,mBAAmB;YACf,YAAY;gBACR;aACH;YACD,WAAW;gBACP;aACH;YACD,aAAa;gBACT;aACH;YACD,MAAM;gBACF;aACH;YACD,cAAc;gBACV;aACH;QACL;QACA,2BAA2B,CAAC;QAC5B,wBAAwB,CAAC;QACzB,WAAW;YACP,SAAS;gBACL;aACH;YACD,cAAc;gBACV;aACH;QACL;QACA,eAAe;YACX,eAAe;gBACX;gBACA;oBACI,UAAU;wBACN;qBACH;oBACD,SAAS;wBACL;qBACH;oBACD,WAAW;wBACP;qBACH;oBACD,QAAQ;wBACJ;qBACH;gBACL;aACH;YACD,kBAAkB;gBACd;gBACA;oBACI,UAAU;wBACN;qBACH;oBACD,SAAS;wBACL;qBACH;oBACD,WAAW;wBACP;qBACH;oBACD,QAAQ;wBACJ;qBACH;gBACL;aACH;YACD,kBAAkB;gBACd;gBACA;oBACI,UAAU;wBACN;qBACH;oBACD,SAAS;wBACL;qBACH;oBACD,WAAW;wBACP;qBACH;oBACD,QAAQ;wBACJ;qBACH;gBACL;aACH;YACD,aAAa;gBACT;gBACA;oBACI,UAAU;wBACN;qBACH;oBACD,SAAS;wBACL;qBACH;oBACD,WAAW;wBACP;qBACH;oBACD,QAAQ;wBACJ;qBACH;gBACL;aACH;YACD,cAAc;gBACV;aACH;QACL;QACA,sBAAsB;YAClB,iBAAiB;gBACb;gBACA;oBACI,SAAS;wBACL;qBACH;gBACL;aACH;YACD,iBAAiB;gBACb;aACH;YACD,OAAO;gBACH;aACH;YACD,WAAW;gBACP;aACH;YACD,SAAS;gBACL;aACH;YACD,cAAc;gBACV;aACH;YACD,SAAS;gBACL;aACH;YACD,aAAa;gBACT;aACH;YACD,QAAQ;gBACJ;aACH;YACD,UAAU;gBACN;aACH;YACD,QAAQ;gBACJ;aACH;YACD,SAAS;gBACL;aACH;YACD,cAAc;gBACV;aACH;QACL;QACA,yBAAyB;YACrB,iBAAiB;gBACb;gBACA;oBACI,SAAS;wBACL;qBACH;gBACL;aACH;YACD,iBAAiB;gBACb;aACH;YACD,OAAO;gBACH;aACH;YACD,WAAW;gBACP;aACH;YACD,SAAS;gBACL;aACH;YACD,cAAc;gBACV;aACH;YACD,SAAS;gBACL;aACH;YACD,aAAa;gBACT;aACH;YACD,QAAQ;gBACJ;aACH;YACD,UAAU;gBACN;aACH;YACD,QAAQ;gBACJ;aACH;YACD,SAAS;gBACL;aACH;YACD,cAAc;gBACV;aACH;QACL;QACA,yBAAyB;YACrB,iBAAiB;gBACb;gBACA;oBACI,SAAS;wBACL;qBACH;gBACL;aACH;YACD,iBAAiB;gBACb;aACH;YACD,OAAO;gBACH;aACH;YACD,WAAW;gBACP;aACH;YACD,SAAS;gBACL;aACH;YACD,cAAc;gBACV;aACH;YACD,SAAS;gBACL;aACH;YACD,aAAa;gBACT;aACH;YACD,QAAQ;gBACJ;aACH;YACD,UAAU;gBACN;aACH;YACD,QAAQ;gBACJ;aACH;YACD,SAAS;gBACL;aACH;YACD,cAAc;gBACV;aACH;QACL;QACA,oBAAoB;YAChB,iBAAiB;gBACb;gBACA;oBACI,SAAS;wBACL;qBACH;gBACL;aACH;YACD,iBAAiB;gBACb;aACH;YACD,OAAO;gBACH;aACH;YACD,WAAW;gBACP;aACH;YACD,SAAS;gBACL;aACH;YACD,cAAc;gBACV;aACH;YACD,SAAS;gBACL;aACH;YACD,aAAa;gBACT;aACH;YACD,QAAQ;gBACJ;aACH;YACD,UAAU;gBACN;aACH;YACD,QAAQ;gBACJ;aACH;YACD,SAAS;gBACL;aACH;YACD,cAAc;gBACV;aACH;QACL;IACJ;AACJ", "debugId": null}}, {"offset": {"line": 3005, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/cms/.basehub/schema.ts"], "sourcesContent": ["// This file was generated by basehub. Do not edit directly. Read more: https://basehub.com/docs/api-reference/basehub-sdk\n\n/* eslint-disable */\n/* eslint-disable eslint-comments/no-restricted-disable */\n/* tslint:disable */\n\n// @ts-nocheck\n/* istanbul ignore file */\n/* tslint:disable */\n/* eslint-disable */\n\nexport type Scalars = {\n    BSHBEventSchema: ({\n  name: string;\n  required: boolean;\n  placeholder?: string;\n  defaultValue?: string;\n  helpText?: string\n} & {\n  id: string;\n  label: string\n} & ({\n  type: \"text\" | \"textarea\" | \"number\" | \"date\" | \"datetime\" | \"email\" | \"checkbox\" | \"hidden\"\n} | {\n  type: \"select\" | \"radio\";\n  options: string[];\n  multiple: boolean\n} | {\n  type: \"file\";\n  private: boolean\n}))[],\n    BSHBRichTextContentSchema: RichTextNode[],\n    BSHBRichTextTOCSchema: RichTextTocNode[],\n    Boolean: boolean,\n    CodeSnippetLanguage: B_Language,\n    DateTime: any,\n    Float: number,\n    ID: string,\n    Int: number,\n    JSON: any,\n    String: string,\n}\n\nexport type AnalyticsKeyScope = 'query' | 'send'\n\nexport interface Authors {\n    _analyticsKey: Scalars['String']\n    _dashboardUrl: Scalars['String']\n    _id: Scalars['String']\n    _idPath: Scalars['String']\n    _meta: ListMeta\n    /** The key used to search from the frontend. */\n    _searchKey: Scalars['String']\n    _slug: Scalars['String']\n    _slugPath: Scalars['String']\n    _sys: BlockDocumentSys\n    _title: Scalars['String']\n    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */\n    item: (AuthorsItem | null)\n    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */\n    items: AuthorsItem[]\n    __typename: 'Authors'\n}\n\nexport interface AuthorsItem {\n    _analyticsKey: Scalars['String']\n    _dashboardUrl: Scalars['String']\n    _id: Scalars['String']\n    _idPath: Scalars['String']\n    _slug: Scalars['String']\n    _slugPath: Scalars['String']\n    _sys: BlockDocumentSys\n    _title: Scalars['String']\n    avatar: BlockImage\n    xUrl: (Scalars['String'] | null)\n    __typename: 'AuthorsItem'\n}\n\nexport type AuthorsItemOrderByEnum = '_sys_createdAt__ASC' | '_sys_createdAt__DESC' | '_sys_hash__ASC' | '_sys_hash__DESC' | '_sys_id__ASC' | '_sys_id__DESC' | '_sys_lastModifiedAt__ASC' | '_sys_lastModifiedAt__DESC' | '_sys_slug__ASC' | '_sys_slug__DESC' | '_sys_title__ASC' | '_sys_title__DESC' | 'avatar__ASC' | 'avatar__DESC' | 'xUrl__ASC' | 'xUrl__DESC'\n\nexport interface BaseRichTextJson {\n    blocks: Scalars['String']\n    content: Scalars['BSHBRichTextContentSchema']\n    toc: Scalars['BSHBRichTextTOCSchema']\n    __typename: 'BaseRichTextJson'\n}\n\nexport interface BlockAudio {\n    /** The duration of the audio in seconds. If the duration is not available, it will be estimated based on the file size. */\n    duration: Scalars['Float']\n    fileName: Scalars['String']\n    fileSize: Scalars['Int']\n    lastModified: Scalars['Float']\n    mimeType: Scalars['String']\n    url: Scalars['String']\n    __typename: 'BlockAudio'\n}\n\nexport interface BlockCodeSnippet {\n    allowedLanguages: Scalars['CodeSnippetLanguage'][]\n    code: Scalars['String']\n    /** @deprecated Figuring out the correct api. */\n    html: Scalars['String']\n    language: Scalars['CodeSnippetLanguage']\n    __typename: 'BlockCodeSnippet'\n}\n\nexport interface BlockColor {\n    b: Scalars['Int']\n    g: Scalars['Int']\n    hex: Scalars['String']\n    hsl: Scalars['String']\n    r: Scalars['Int']\n    rgb: Scalars['String']\n    __typename: 'BlockColor'\n}\n\nexport type BlockDocument = (Authors | AuthorsItem | Blog | Categories | CategoriesItem | LegalPages | LegalPagesItem | Posts | PostsItem | _AgentSTART | authorsItem_AsList | categoriesItem_AsList | legalPagesItem_AsList | postsItem_AsList) & { __isUnion?: true }\n\nexport interface BlockDocumentSys {\n    apiNamePath: Scalars['String']\n    createdAt: Scalars['String']\n    hash: Scalars['String']\n    id: Scalars['ID']\n    idPath: Scalars['String']\n    lastModifiedAt: Scalars['String']\n    slug: Scalars['String']\n    slugPath: Scalars['String']\n    title: Scalars['String']\n    __typename: 'BlockDocumentSys'\n}\n\nexport interface BlockFile {\n    fileName: Scalars['String']\n    fileSize: Scalars['Int']\n    lastModified: Scalars['Float']\n    mimeType: Scalars['String']\n    url: Scalars['String']\n    __typename: 'BlockFile'\n}\n\nexport interface BlockImage {\n    alt: (Scalars['String'] | null)\n    aspectRatio: Scalars['String']\n    blurDataURL: Scalars['String']\n    fileName: Scalars['String']\n    fileSize: Scalars['Int']\n    height: Scalars['Int']\n    lastModified: Scalars['Float']\n    mimeType: Scalars['String']\n    /** @deprecated Renamed to `blurDataURL` to match Next.js Image's naming convention. */\n    placeholderURL: Scalars['String']\n    /** @deprecated Use `url` instead. */\n    rawUrl: Scalars['String']\n    thumbhash: Scalars['String']\n    /**\n     * This field is used to generate the image URL with the provided options. The options are passed as arguments. For example, if you want to resize the image to 200x200 pixels, you can use the following query:\n     * \n     * ```graphql\n     * {\n     *   imageBlock {\n     *     url(width: 200, height: 200)\n     *   }\n     * }\n     * ```\n     * \n     * This will return the URL with the width and height set to 200 pixels.\n     * \n     * BaseHub uses Cloudflare for image resizing. Check out [all available options in their docs](https://developers.cloudflare.com/images/transform-images/transform-via-workers/#fetch-options).\n     * \n     */\n    url: Scalars['String']\n    width: Scalars['Int']\n    __typename: 'BlockImage'\n}\n\nexport type BlockList = (Authors | Categories | LegalPages | Posts | authorsItem_AsList | categoriesItem_AsList | legalPagesItem_AsList | postsItem_AsList) & { __isUnion?: true }\n\nexport interface BlockOgImage {\n    height: Scalars['Int']\n    url: Scalars['String']\n    width: Scalars['Int']\n    __typename: 'BlockOgImage'\n}\n\n\n/** Rich text block */\nexport type BlockRichText = (Body | Body_1) & { __isUnion?: true }\n\nexport interface BlockVideo {\n    aspectRatio: Scalars['String']\n    /** The duration of the video in seconds. If the duration is not available, it will be estimated based on the file size. */\n    duration: Scalars['Float']\n    fileName: Scalars['String']\n    fileSize: Scalars['Int']\n    height: Scalars['Int']\n    lastModified: Scalars['Float']\n    mimeType: Scalars['String']\n    url: Scalars['String']\n    width: Scalars['Int']\n    __typename: 'BlockVideo'\n}\n\nexport interface Blog {\n    _analyticsKey: Scalars['String']\n    _dashboardUrl: Scalars['String']\n    _id: Scalars['String']\n    _idPath: Scalars['String']\n    _slug: Scalars['String']\n    _slugPath: Scalars['String']\n    _sys: BlockDocumentSys\n    _title: Scalars['String']\n    authors: Authors\n    categories: Categories\n    posts: Posts\n    __typename: 'Blog'\n}\n\nexport interface Body {\n    html: Scalars['String']\n    json: BodyRichText\n    markdown: Scalars['String']\n    plainText: Scalars['String']\n    readingTime: Scalars['Int']\n    __typename: 'Body'\n}\n\nexport interface BodyRichText {\n    content: Scalars['BSHBRichTextContentSchema']\n    toc: Scalars['BSHBRichTextTOCSchema']\n    __typename: 'BodyRichText'\n}\n\nexport interface Body_1 {\n    html: Scalars['String']\n    json: Body_1RichText\n    markdown: Scalars['String']\n    plainText: Scalars['String']\n    readingTime: Scalars['Int']\n    __typename: 'Body_1'\n}\n\nexport interface Body_1RichText {\n    content: Scalars['BSHBRichTextContentSchema']\n    toc: Scalars['BSHBRichTextTOCSchema']\n    __typename: 'Body_1RichText'\n}\n\nexport interface Categories {\n    _analyticsKey: Scalars['String']\n    _dashboardUrl: Scalars['String']\n    _id: Scalars['String']\n    _idPath: Scalars['String']\n    _meta: ListMeta\n    /** The key used to search from the frontend. */\n    _searchKey: Scalars['String']\n    _slug: Scalars['String']\n    _slugPath: Scalars['String']\n    _sys: BlockDocumentSys\n    _title: Scalars['String']\n    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */\n    item: (CategoriesItem | null)\n    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */\n    items: CategoriesItem[]\n    __typename: 'Categories'\n}\n\nexport interface CategoriesItem {\n    _analyticsKey: Scalars['String']\n    _dashboardUrl: Scalars['String']\n    _id: Scalars['String']\n    _idPath: Scalars['String']\n    _slug: Scalars['String']\n    _slugPath: Scalars['String']\n    _sys: BlockDocumentSys\n    _title: Scalars['String']\n    __typename: 'CategoriesItem'\n}\n\nexport type CategoriesItemOrderByEnum = '_sys_createdAt__ASC' | '_sys_createdAt__DESC' | '_sys_hash__ASC' | '_sys_hash__DESC' | '_sys_id__ASC' | '_sys_id__DESC' | '_sys_lastModifiedAt__ASC' | '_sys_lastModifiedAt__DESC' | '_sys_slug__ASC' | '_sys_slug__DESC' | '_sys_title__ASC' | '_sys_title__DESC'\n\nexport interface GetUploadSignedURL {\n    signedURL: Scalars['String']\n    uploadURL: Scalars['String']\n    __typename: 'GetUploadSignedURL'\n}\n\nexport interface LegalPages {\n    _analyticsKey: Scalars['String']\n    _dashboardUrl: Scalars['String']\n    _id: Scalars['String']\n    _idPath: Scalars['String']\n    _meta: ListMeta\n    /** The key used to search from the frontend. */\n    _searchKey: Scalars['String']\n    _slug: Scalars['String']\n    _slugPath: Scalars['String']\n    _sys: BlockDocumentSys\n    _title: Scalars['String']\n    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */\n    item: (LegalPagesItem | null)\n    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */\n    items: LegalPagesItem[]\n    __typename: 'LegalPages'\n}\n\nexport interface LegalPagesItem {\n    _analyticsKey: Scalars['String']\n    _dashboardUrl: Scalars['String']\n    _id: Scalars['String']\n    _idPath: Scalars['String']\n    _slug: Scalars['String']\n    _slugPath: Scalars['String']\n    _sys: BlockDocumentSys\n    _title: Scalars['String']\n    body: Body_1\n    description: Scalars['String']\n    __typename: 'LegalPagesItem'\n}\n\nexport type LegalPagesItemOrderByEnum = '_sys_createdAt__ASC' | '_sys_createdAt__DESC' | '_sys_hash__ASC' | '_sys_hash__DESC' | '_sys_id__ASC' | '_sys_id__DESC' | '_sys_lastModifiedAt__ASC' | '_sys_lastModifiedAt__DESC' | '_sys_slug__ASC' | '_sys_slug__DESC' | '_sys_title__ASC' | '_sys_title__DESC' | 'body__ASC' | 'body__DESC' | 'description__ASC' | 'description__DESC'\n\nexport interface ListMeta {\n    /** Number of items after applying filters but before pagination */\n    filteredCount: Scalars['Int']\n    /** Total number of items in collection before any filtering/pagination */\n    totalCount: Scalars['Int']\n    __typename: 'ListMeta'\n}\n\nexport type MediaBlock = (BlockAudio | BlockFile | BlockImage | BlockVideo) & { __isUnion?: true }\n\nexport type MediaBlockUnion = (BlockAudio | BlockFile | BlockImage | BlockVideo) & { __isUnion?: true }\n\nexport interface Mutation {\n    /**\n     * Returns a signed url and an upload url so that you can upload files into your repository.\n     * \n     * Example usage with JavaScript:\n     * ```js\n     * async function handleUpload(file: File) {\n     *   const { getUploadSignedURL } = await basehub().mutation({\n     *     getUploadSignedURL: {\n     *       __args: { fileName: file.name },\n     *       signedURL: true,\n     *       uploadURL: true,\n     *     }\n     *   })\n     * \n     *   const { signedURL, uploadURL } = getUploadSignedURL\n     * \n     *   await fetch(signedURL, { method: 'PUT', body: file })\n     * \n     *   // done! do something with the uploadURL now\n     * }\n     * ```\n     * \n     */\n    getUploadSignedURL: GetUploadSignedURL\n    /** Start a job that can be awaited and the result given directly. Under the hood, it runs `transactionAsync` and polls for the result until it is available. You can pass a `timeout` argument, the default being 30_000ms. */\n    transaction: TransactionStatus\n    /** Start an asynchronous job to mutate BaseHub data. Returns a transaction ID which you can use to get the result of the job. */\n    transactionAsync: Scalars['String']\n    transactionStatus: TransactionStatus\n    __typename: 'Mutation'\n}\n\nexport interface Posts {\n    _analyticsKey: Scalars['String']\n    _dashboardUrl: Scalars['String']\n    _id: Scalars['String']\n    _idPath: Scalars['String']\n    _meta: ListMeta\n    /** The key used to search from the frontend. */\n    _searchKey: Scalars['String']\n    _slug: Scalars['String']\n    _slugPath: Scalars['String']\n    _sys: BlockDocumentSys\n    _title: Scalars['String']\n    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */\n    item: (PostsItem | null)\n    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */\n    items: PostsItem[]\n    __typename: 'Posts'\n}\n\nexport interface PostsItem {\n    _analyticsKey: Scalars['String']\n    _dashboardUrl: Scalars['String']\n    _id: Scalars['String']\n    _idPath: Scalars['String']\n    _slug: Scalars['String']\n    _slugPath: Scalars['String']\n    _sys: BlockDocumentSys\n    _title: Scalars['String']\n    authors: AuthorsItem[]\n    body: Body\n    categories: (CategoriesItem[] | null)\n    /** ISO 8601 date string. */\n    date: Scalars['String']\n    description: Scalars['String']\n    image: BlockImage\n    __typename: 'PostsItem'\n}\n\nexport type PostsItemOrderByEnum = '_sys_createdAt__ASC' | '_sys_createdAt__DESC' | '_sys_hash__ASC' | '_sys_hash__DESC' | '_sys_id__ASC' | '_sys_id__DESC' | '_sys_lastModifiedAt__ASC' | '_sys_lastModifiedAt__DESC' | '_sys_slug__ASC' | '_sys_slug__DESC' | '_sys_title__ASC' | '_sys_title__DESC' | 'authors__ASC' | 'authors__DESC' | 'body__ASC' | 'body__DESC' | 'categories__ASC' | 'categories__DESC' | 'date__ASC' | 'date__DESC' | 'description__ASC' | 'description__DESC' | 'image__ASC' | 'image__DESC'\n\nexport interface Query {\n    _agent: (_AgentSTART | null)\n    /** Query across the custom AI agents in the repository. */\n    _agents: _agents\n    /** Query across all of the instances of a component. Pass in filters and sorts if you want, and get each instance via the `items` key. */\n    _componentInstances: _components\n    /** The structure of the repository. Used by START. */\n    _structure: Scalars['JSON']\n    _sys: RepoSys\n    blog: Blog\n    legalPages: LegalPages\n    __typename: 'Query'\n}\n\nexport interface RepoSys {\n    branches: _Branches\n    hash: Scalars['String']\n    id: Scalars['ID']\n    playgroundInfo: (_PlaygroundInfo | null)\n    slug: Scalars['String']\n    title: Scalars['String']\n    __typename: 'RepoSys'\n}\n\nexport type RichTextJson = (BaseRichTextJson | BodyRichText | Body_1RichText) & { __isUnion?: true }\n\nexport interface TransactionStatus {\n    /** Duration in milliseconds. */\n    duration: (Scalars['Int'] | null)\n    endedAt: (Scalars['String'] | null)\n    id: Scalars['String']\n    message: (Scalars['String'] | null)\n    startedAt: Scalars['String']\n    status: TransactionStatusEnum\n    __typename: 'TransactionStatus'\n}\n\nexport type TransactionStatusEnum = 'Cancelled' | 'Completed' | 'Failed' | 'Running' | 'Scheduled'\n\nexport interface Variant {\n    apiName: Scalars['String']\n    color: Scalars['String']\n    id: Scalars['String']\n    isDefault: Scalars['Boolean']\n    label: Scalars['String']\n    __typename: 'Variant'\n}\n\nexport interface _AgentSTART {\n    _agentKey: Scalars['String']\n    _analyticsKey: Scalars['String']\n    _dashboardUrl: Scalars['String']\n    _id: Scalars['String']\n    _idPath: Scalars['String']\n    _slug: Scalars['String']\n    _slugPath: Scalars['String']\n    _sys: BlockDocumentSys\n    _title: Scalars['String']\n    accent: Scalars['String']\n    avatar: Scalars['String']\n    chatUrl: Scalars['String']\n    commit: Scalars['Boolean']\n    description: Scalars['String']\n    edit: Scalars['Boolean']\n    embedUrl: Scalars['String']\n    getUserInfo: Scalars['Boolean']\n    grayscale: Scalars['String']\n    manageBranches: Scalars['Boolean']\n    mcpUrl: Scalars['String']\n    model: Scalars['String']\n    searchTheWeb: Scalars['Boolean']\n    slackInstallUrl: Scalars['String']\n    systemPrompt: Scalars['String']\n    __typename: '_AgentSTART'\n}\n\nexport interface _BranchInfo {\n    archivedAt: (Scalars['String'] | null)\n    archivedBy: (Scalars['String'] | null)\n    authorId: (Scalars['String'] | null)\n    contributors: (Scalars['String'][] | null)\n    createdAt: Scalars['String']\n    description: (Scalars['String'] | null)\n    git: (_GitInfo | null)\n    headCommit: (_CommitInfo | null)\n    headCommitId: (Scalars['String'] | null)\n    id: Scalars['ID']\n    inlineSuggestionAppliedAt: (Scalars['String'] | null)\n    isDefault: Scalars['Boolean']\n    isInlineSuggestion: (Scalars['Boolean'] | null)\n    name: Scalars['String']\n    playgroundId: (Scalars['String'] | null)\n    rollbackCommitId: (Scalars['String'] | null)\n    rollbackIsoDate: (Scalars['String'] | null)\n    sourceBranchId: (Scalars['String'] | null)\n    updatedAt: (Scalars['String'] | null)\n    workingRootBlockId: (Scalars['String'] | null)\n    __typename: '_BranchInfo'\n}\n\nexport interface _Branches {\n    _meta: ListMeta\n    items: _BranchInfo[]\n    __typename: '_Branches'\n}\n\nexport interface _CommitInfo {\n    authorId: Scalars['String']\n    branchId: Scalars['String']\n    contributors: (Scalars['String'][] | null)\n    createdAt: Scalars['String']\n    hash: Scalars['String']\n    id: Scalars['String']\n    mergeParentCommitId: (Scalars['String'] | null)\n    message: Scalars['String']\n    parentCommitId: (Scalars['String'] | null)\n    /** Whether this commit is from a playground branch. */\n    playgroundId: (Scalars['String'] | null)\n    repoId: Scalars['String']\n    rootBlockId: Scalars['String']\n    __typename: '_CommitInfo'\n}\n\nexport interface _GitInfo {\n    branch: Scalars['String']\n    deploymentUrl: (Scalars['String'] | null)\n    __typename: '_GitInfo'\n}\n\nexport interface _PlaygroundInfo {\n    claimUrl: (Scalars['String'] | null)\n    editUrl: Scalars['String']\n    expiresAt: (Scalars['String'] | null)\n    id: (Scalars['String'] | null)\n    __typename: '_PlaygroundInfo'\n}\n\nexport type _ResolveTargetsWithEnum = 'id' | 'objectName'\n\nexport type _StructureFormatEnum = 'json' | 'xml'\n\nexport interface _agents {\n    start: _AgentSTART\n    __typename: '_agents'\n}\n\nexport interface _components {\n    authorsItem: authorsItem_AsList\n    categoriesItem: categoriesItem_AsList\n    legalPagesItem: legalPagesItem_AsList\n    postsItem: postsItem_AsList\n    __typename: '_components'\n}\n\nexport interface authorsItem_AsList {\n    _analyticsKey: Scalars['String']\n    _dashboardUrl: Scalars['String']\n    _id: Scalars['String']\n    _idPath: Scalars['String']\n    _meta: ListMeta\n    /** The key used to search from the frontend. */\n    _searchKey: Scalars['String']\n    _slug: Scalars['String']\n    _slugPath: Scalars['String']\n    _sys: BlockDocumentSys\n    _title: Scalars['String']\n    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */\n    item: (AuthorsItem | null)\n    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */\n    items: AuthorsItem[]\n    __typename: 'authorsItem_AsList'\n}\n\nexport interface categoriesItem_AsList {\n    _analyticsKey: Scalars['String']\n    _dashboardUrl: Scalars['String']\n    _id: Scalars['String']\n    _idPath: Scalars['String']\n    _meta: ListMeta\n    /** The key used to search from the frontend. */\n    _searchKey: Scalars['String']\n    _slug: Scalars['String']\n    _slugPath: Scalars['String']\n    _sys: BlockDocumentSys\n    _title: Scalars['String']\n    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */\n    item: (CategoriesItem | null)\n    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */\n    items: CategoriesItem[]\n    __typename: 'categoriesItem_AsList'\n}\n\nexport interface legalPagesItem_AsList {\n    _analyticsKey: Scalars['String']\n    _dashboardUrl: Scalars['String']\n    _id: Scalars['String']\n    _idPath: Scalars['String']\n    _meta: ListMeta\n    /** The key used to search from the frontend. */\n    _searchKey: Scalars['String']\n    _slug: Scalars['String']\n    _slugPath: Scalars['String']\n    _sys: BlockDocumentSys\n    _title: Scalars['String']\n    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */\n    item: (LegalPagesItem | null)\n    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */\n    items: LegalPagesItem[]\n    __typename: 'legalPagesItem_AsList'\n}\n\nexport interface postsItem_AsList {\n    _analyticsKey: Scalars['String']\n    _dashboardUrl: Scalars['String']\n    _id: Scalars['String']\n    _idPath: Scalars['String']\n    _meta: ListMeta\n    /** The key used to search from the frontend. */\n    _searchKey: Scalars['String']\n    _slug: Scalars['String']\n    _slugPath: Scalars['String']\n    _sys: BlockDocumentSys\n    _title: Scalars['String']\n    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */\n    item: (PostsItem | null)\n    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */\n    items: PostsItem[]\n    __typename: 'postsItem_AsList'\n}\n\nexport interface AuthorsGenqlSelection{\n    _analyticsKey?: { __args: {\n    /**\n     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.\n     * \n     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.\n     */\n    scope?: (AnalyticsKeyScope | null)} } | boolean | number\n    _dashboardUrl?: boolean | number\n    _id?: boolean | number\n    _idPath?: boolean | number\n    _meta?: ListMetaGenqlSelection\n    /** The key used to search from the frontend. */\n    _searchKey?: boolean | number\n    _slug?: boolean | number\n    _slugPath?: boolean | number\n    _sys?: BlockDocumentSysGenqlSelection\n    _title?: boolean | number\n    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */\n    item?: AuthorsItemGenqlSelection\n    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */\n    items?: AuthorsItemGenqlSelection\n    __typename?: boolean | number\n    __scalar?: boolean | number\n}\n\nexport interface AuthorsItemGenqlSelection{\n    _analyticsKey?: { __args: {\n    /**\n     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.\n     * \n     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.\n     */\n    scope?: (AnalyticsKeyScope | null)} } | boolean | number\n    _dashboardUrl?: boolean | number\n    _id?: boolean | number\n    _idPath?: boolean | number\n    _slug?: boolean | number\n    _slugPath?: boolean | number\n    _sys?: BlockDocumentSysGenqlSelection\n    _title?: boolean | number\n    avatar?: BlockImageGenqlSelection\n    xUrl?: boolean | number\n    __typename?: boolean | number\n    __scalar?: boolean | number\n}\n\nexport interface AuthorsItemFilterInput {AND?: (AuthorsItemFilterInput | null),OR?: (AuthorsItemFilterInput | null),_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),xUrl?: (StringFilter | null)}\n\nexport interface BaseRichTextJsonGenqlSelection{\n    blocks?: boolean | number\n    content?: boolean | number\n    toc?: boolean | number\n    __typename?: boolean | number\n    __scalar?: boolean | number\n}\n\nexport interface BlockAudioGenqlSelection{\n    /** The duration of the audio in seconds. If the duration is not available, it will be estimated based on the file size. */\n    duration?: boolean | number\n    fileName?: boolean | number\n    fileSize?: boolean | number\n    lastModified?: boolean | number\n    mimeType?: boolean | number\n    url?: boolean | number\n    __typename?: boolean | number\n    __scalar?: boolean | number\n}\n\nexport interface BlockCodeSnippetGenqlSelection{\n    allowedLanguages?: boolean | number\n    code?: boolean | number\n    /** @deprecated Figuring out the correct api. */\n    html?: { __args: {\n    /** Theme for the code snippet */\n    theme?: (Scalars['String'] | null)} } | boolean | number\n    language?: boolean | number\n    __typename?: boolean | number\n    __scalar?: boolean | number\n}\n\nexport interface BlockColorGenqlSelection{\n    b?: boolean | number\n    g?: boolean | number\n    hex?: boolean | number\n    hsl?: boolean | number\n    r?: boolean | number\n    rgb?: boolean | number\n    __typename?: boolean | number\n    __scalar?: boolean | number\n}\n\nexport interface BlockDocumentGenqlSelection{\n    _analyticsKey?: { __args: {\n    /**\n     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.\n     * \n     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.\n     */\n    scope?: (AnalyticsKeyScope | null)} } | boolean | number\n    _dashboardUrl?: boolean | number\n    _id?: boolean | number\n    _idPath?: boolean | number\n    _slug?: boolean | number\n    _slugPath?: boolean | number\n    _sys?: BlockDocumentSysGenqlSelection\n    _title?: boolean | number\n    on_Authors?: AuthorsGenqlSelection\n    on_AuthorsItem?: AuthorsItemGenqlSelection\n    on_Blog?: BlogGenqlSelection\n    on_Categories?: CategoriesGenqlSelection\n    on_CategoriesItem?: CategoriesItemGenqlSelection\n    on_LegalPages?: LegalPagesGenqlSelection\n    on_LegalPagesItem?: LegalPagesItemGenqlSelection\n    on_Posts?: PostsGenqlSelection\n    on_PostsItem?: PostsItemGenqlSelection\n    on__AgentSTART?: _AgentSTARTGenqlSelection\n    on_authorsItem_AsList?: authorsItem_AsListGenqlSelection\n    on_categoriesItem_AsList?: categoriesItem_AsListGenqlSelection\n    on_legalPagesItem_AsList?: legalPagesItem_AsListGenqlSelection\n    on_postsItem_AsList?: postsItem_AsListGenqlSelection\n    __typename?: boolean | number\n    __scalar?: boolean | number\n}\n\nexport interface BlockDocumentSysGenqlSelection{\n    apiNamePath?: boolean | number\n    createdAt?: boolean | number\n    hash?: boolean | number\n    id?: boolean | number\n    idPath?: boolean | number\n    lastModifiedAt?: boolean | number\n    slug?: boolean | number\n    slugPath?: boolean | number\n    title?: boolean | number\n    __typename?: boolean | number\n    __scalar?: boolean | number\n}\n\nexport interface BlockFileGenqlSelection{\n    fileName?: boolean | number\n    fileSize?: boolean | number\n    lastModified?: boolean | number\n    mimeType?: boolean | number\n    url?: boolean | number\n    __typename?: boolean | number\n    __scalar?: boolean | number\n}\n\nexport interface BlockImageGenqlSelection{\n    alt?: boolean | number\n    aspectRatio?: boolean | number\n    blurDataURL?: boolean | number\n    fileName?: boolean | number\n    fileSize?: boolean | number\n    height?: boolean | number\n    lastModified?: boolean | number\n    mimeType?: boolean | number\n    /** @deprecated Renamed to `blurDataURL` to match Next.js Image's naming convention. */\n    placeholderURL?: boolean | number\n    /** @deprecated Use `url` instead. */\n    rawUrl?: boolean | number\n    thumbhash?: boolean | number\n    /**\n     * This field is used to generate the image URL with the provided options. The options are passed as arguments. For example, if you want to resize the image to 200x200 pixels, you can use the following query:\n     * \n     * ```graphql\n     * {\n     *   imageBlock {\n     *     url(width: 200, height: 200)\n     *   }\n     * }\n     * ```\n     * \n     * This will return the URL with the width and height set to 200 pixels.\n     * \n     * BaseHub uses Cloudflare for image resizing. Check out [all available options in their docs](https://developers.cloudflare.com/images/transform-images/transform-via-workers/#fetch-options).\n     * \n     */\n    url?: { __args: {anim?: (Scalars['String'] | null), background?: (Scalars['String'] | null), blur?: (Scalars['Int'] | null), border?: (Scalars['String'] | null), brightness?: (Scalars['Int'] | null), compression?: (Scalars['String'] | null), contrast?: (Scalars['Int'] | null), dpr?: (Scalars['Int'] | null), fit?: (Scalars['String'] | null), format?: (Scalars['String'] | null), gamma?: (Scalars['String'] | null), gravity?: (Scalars['String'] | null), height?: (Scalars['Int'] | null), metadata?: (Scalars['String'] | null), quality?: (Scalars['Int'] | null), rotate?: (Scalars['String'] | null), sharpen?: (Scalars['String'] | null), trim?: (Scalars['String'] | null), width?: (Scalars['Int'] | null)} } | boolean | number\n    width?: boolean | number\n    __typename?: boolean | number\n    __scalar?: boolean | number\n}\n\nexport interface BlockListGenqlSelection{\n    _analyticsKey?: { __args: {\n    /**\n     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.\n     * \n     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.\n     */\n    scope?: (AnalyticsKeyScope | null)} } | boolean | number\n    _dashboardUrl?: boolean | number\n    _id?: boolean | number\n    _idPath?: boolean | number\n    _meta?: ListMetaGenqlSelection\n    /** The key used to search from the frontend. */\n    _searchKey?: boolean | number\n    _slug?: boolean | number\n    _slugPath?: boolean | number\n    _sys?: BlockDocumentSysGenqlSelection\n    _title?: boolean | number\n    on_Authors?: AuthorsGenqlSelection\n    on_Categories?: CategoriesGenqlSelection\n    on_LegalPages?: LegalPagesGenqlSelection\n    on_Posts?: PostsGenqlSelection\n    on_authorsItem_AsList?: authorsItem_AsListGenqlSelection\n    on_categoriesItem_AsList?: categoriesItem_AsListGenqlSelection\n    on_legalPagesItem_AsList?: legalPagesItem_AsListGenqlSelection\n    on_postsItem_AsList?: postsItem_AsListGenqlSelection\n    __typename?: boolean | number\n    __scalar?: boolean | number\n}\n\nexport interface BlockOgImageGenqlSelection{\n    height?: boolean | number\n    url?: boolean | number\n    width?: boolean | number\n    __typename?: boolean | number\n    __scalar?: boolean | number\n}\n\n\n/** Rich text block */\nexport interface BlockRichTextGenqlSelection{\n    html?: { __args: {\n    /** It automatically generates a unique id for each heading present in the HTML. Enabled by default. */\n    slugs?: (Scalars['Boolean'] | null), \n    /** Inserts a table of contents at the beginning of the HTML. */\n    toc?: (Scalars['Boolean'] | null)} } | boolean | number\n    json?: RichTextJsonGenqlSelection\n    markdown?: boolean | number\n    plainText?: boolean | number\n    readingTime?: { __args: {\n    /** Words per minute, defaults to average 183wpm */\n    wpm?: (Scalars['Int'] | null)} } | boolean | number\n    on_Body?: BodyGenqlSelection\n    on_Body_1?: Body_1GenqlSelection\n    __typename?: boolean | number\n    __scalar?: boolean | number\n}\n\nexport interface BlockVideoGenqlSelection{\n    aspectRatio?: boolean | number\n    /** The duration of the video in seconds. If the duration is not available, it will be estimated based on the file size. */\n    duration?: boolean | number\n    fileName?: boolean | number\n    fileSize?: boolean | number\n    height?: boolean | number\n    lastModified?: boolean | number\n    mimeType?: boolean | number\n    url?: boolean | number\n    width?: boolean | number\n    __typename?: boolean | number\n    __scalar?: boolean | number\n}\n\nexport interface BlogGenqlSelection{\n    _analyticsKey?: { __args: {\n    /**\n     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.\n     * \n     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.\n     */\n    scope?: (AnalyticsKeyScope | null)} } | boolean | number\n    _dashboardUrl?: boolean | number\n    _id?: boolean | number\n    _idPath?: boolean | number\n    _slug?: boolean | number\n    _slugPath?: boolean | number\n    _sys?: BlockDocumentSysGenqlSelection\n    _title?: boolean | number\n    authors?: (AuthorsGenqlSelection & { __args?: {\n    /** Filter by a field. */\n    filter?: (AuthorsItemFilterInput | null), \n    /** Limit the number of items returned. Defaults to 500. */\n    first?: (Scalars['Int'] | null), \n    /** Order by a field. */\n    orderBy?: (AuthorsItemOrderByEnum | null), \n    /** Skip the first n items. */\n    skip?: (Scalars['Int'] | null)} })\n    categories?: (CategoriesGenqlSelection & { __args?: {\n    /** Filter by a field. */\n    filter?: (CategoriesItemFilterInput | null), \n    /** Limit the number of items returned. Defaults to 500. */\n    first?: (Scalars['Int'] | null), \n    /** Order by a field. */\n    orderBy?: (CategoriesItemOrderByEnum | null), \n    /** Skip the first n items. */\n    skip?: (Scalars['Int'] | null)} })\n    posts?: (PostsGenqlSelection & { __args?: {\n    /** Filter by a field. */\n    filter?: (PostsItemFilterInput | null), \n    /** Limit the number of items returned. Defaults to 500. */\n    first?: (Scalars['Int'] | null), \n    /** Order by a field. */\n    orderBy?: (PostsItemOrderByEnum | null), \n    /** Skip the first n items. */\n    skip?: (Scalars['Int'] | null)} })\n    __typename?: boolean | number\n    __scalar?: boolean | number\n}\n\nexport interface BodyGenqlSelection{\n    html?: { __args: {\n    /** It automatically generates a unique id for each heading present in the HTML. Enabled by default. */\n    slugs?: (Scalars['Boolean'] | null), \n    /** Inserts a table of contents at the beginning of the HTML. */\n    toc?: (Scalars['Boolean'] | null)} } | boolean | number\n    json?: BodyRichTextGenqlSelection\n    markdown?: boolean | number\n    plainText?: boolean | number\n    readingTime?: { __args: {\n    /** Words per minute, defaults to average 183wpm */\n    wpm?: (Scalars['Int'] | null)} } | boolean | number\n    __typename?: boolean | number\n    __scalar?: boolean | number\n}\n\nexport interface BodyRichTextGenqlSelection{\n    content?: boolean | number\n    toc?: boolean | number\n    __typename?: boolean | number\n    __scalar?: boolean | number\n}\n\nexport interface Body_1GenqlSelection{\n    html?: { __args: {\n    /** It automatically generates a unique id for each heading present in the HTML. Enabled by default. */\n    slugs?: (Scalars['Boolean'] | null), \n    /** Inserts a table of contents at the beginning of the HTML. */\n    toc?: (Scalars['Boolean'] | null)} } | boolean | number\n    json?: Body_1RichTextGenqlSelection\n    markdown?: boolean | number\n    plainText?: boolean | number\n    readingTime?: { __args: {\n    /** Words per minute, defaults to average 183wpm */\n    wpm?: (Scalars['Int'] | null)} } | boolean | number\n    __typename?: boolean | number\n    __scalar?: boolean | number\n}\n\nexport interface Body_1RichTextGenqlSelection{\n    content?: boolean | number\n    toc?: boolean | number\n    __typename?: boolean | number\n    __scalar?: boolean | number\n}\n\nexport interface CategoriesGenqlSelection{\n    _analyticsKey?: { __args: {\n    /**\n     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.\n     * \n     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.\n     */\n    scope?: (AnalyticsKeyScope | null)} } | boolean | number\n    _dashboardUrl?: boolean | number\n    _id?: boolean | number\n    _idPath?: boolean | number\n    _meta?: ListMetaGenqlSelection\n    /** The key used to search from the frontend. */\n    _searchKey?: boolean | number\n    _slug?: boolean | number\n    _slugPath?: boolean | number\n    _sys?: BlockDocumentSysGenqlSelection\n    _title?: boolean | number\n    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */\n    item?: CategoriesItemGenqlSelection\n    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */\n    items?: CategoriesItemGenqlSelection\n    __typename?: boolean | number\n    __scalar?: boolean | number\n}\n\nexport interface CategoriesItemGenqlSelection{\n    _analyticsKey?: { __args: {\n    /**\n     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.\n     * \n     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.\n     */\n    scope?: (AnalyticsKeyScope | null)} } | boolean | number\n    _dashboardUrl?: boolean | number\n    _id?: boolean | number\n    _idPath?: boolean | number\n    _slug?: boolean | number\n    _slugPath?: boolean | number\n    _sys?: BlockDocumentSysGenqlSelection\n    _title?: boolean | number\n    __typename?: boolean | number\n    __scalar?: boolean | number\n}\n\nexport interface CategoriesItemFilterInput {AND?: (CategoriesItemFilterInput | null),OR?: (CategoriesItemFilterInput | null),_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null)}\n\nexport interface DateFilter {eq?: (Scalars['DateTime'] | null),isAfter?: (Scalars['DateTime'] | null),isBefore?: (Scalars['DateTime'] | null),isNull?: (Scalars['Boolean'] | null),neq?: (Scalars['DateTime'] | null),onOrAfter?: (Scalars['DateTime'] | null),onOrBefore?: (Scalars['DateTime'] | null)}\n\nexport interface GetUploadSignedURLGenqlSelection{\n    signedURL?: boolean | number\n    uploadURL?: boolean | number\n    __typename?: boolean | number\n    __scalar?: boolean | number\n}\n\nexport interface LegalPagesGenqlSelection{\n    _analyticsKey?: { __args: {\n    /**\n     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.\n     * \n     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.\n     */\n    scope?: (AnalyticsKeyScope | null)} } | boolean | number\n    _dashboardUrl?: boolean | number\n    _id?: boolean | number\n    _idPath?: boolean | number\n    _meta?: ListMetaGenqlSelection\n    /** The key used to search from the frontend. */\n    _searchKey?: boolean | number\n    _slug?: boolean | number\n    _slugPath?: boolean | number\n    _sys?: BlockDocumentSysGenqlSelection\n    _title?: boolean | number\n    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */\n    item?: LegalPagesItemGenqlSelection\n    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */\n    items?: LegalPagesItemGenqlSelection\n    __typename?: boolean | number\n    __scalar?: boolean | number\n}\n\nexport interface LegalPagesItemGenqlSelection{\n    _analyticsKey?: { __args: {\n    /**\n     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.\n     * \n     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.\n     */\n    scope?: (AnalyticsKeyScope | null)} } | boolean | number\n    _dashboardUrl?: boolean | number\n    _id?: boolean | number\n    _idPath?: boolean | number\n    _slug?: boolean | number\n    _slugPath?: boolean | number\n    _sys?: BlockDocumentSysGenqlSelection\n    _title?: boolean | number\n    body?: Body_1GenqlSelection\n    description?: boolean | number\n    __typename?: boolean | number\n    __scalar?: boolean | number\n}\n\nexport interface LegalPagesItemFilterInput {AND?: (LegalPagesItemFilterInput | null),OR?: (LegalPagesItemFilterInput | null),_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),description?: (StringFilter | null)}\n\nexport interface ListFilter {isEmpty?: (Scalars['Boolean'] | null),length?: (Scalars['Int'] | null)}\n\nexport interface ListMetaGenqlSelection{\n    /** Number of items after applying filters but before pagination */\n    filteredCount?: boolean | number\n    /** Total number of items in collection before any filtering/pagination */\n    totalCount?: boolean | number\n    __typename?: boolean | number\n    __scalar?: boolean | number\n}\n\nexport interface MediaBlockGenqlSelection{\n    fileName?: boolean | number\n    fileSize?: boolean | number\n    lastModified?: boolean | number\n    mimeType?: boolean | number\n    url?: boolean | number\n    on_BlockAudio?: BlockAudioGenqlSelection\n    on_BlockFile?: BlockFileGenqlSelection\n    on_BlockImage?: BlockImageGenqlSelection\n    on_BlockVideo?: BlockVideoGenqlSelection\n    __typename?: boolean | number\n    __scalar?: boolean | number\n}\n\nexport interface MediaBlockUnionGenqlSelection{\n    on_BlockAudio?:BlockAudioGenqlSelection,\n    on_BlockFile?:BlockFileGenqlSelection,\n    on_BlockImage?:BlockImageGenqlSelection,\n    on_BlockVideo?:BlockVideoGenqlSelection,\n    on_MediaBlock?: MediaBlockGenqlSelection,\n    __typename?: boolean | number\n}\n\nexport interface MutationGenqlSelection{\n    /**\n     * Returns a signed url and an upload url so that you can upload files into your repository.\n     * \n     * Example usage with JavaScript:\n     * ```js\n     * async function handleUpload(file: File) {\n     *   const { getUploadSignedURL } = await basehub().mutation({\n     *     getUploadSignedURL: {\n     *       __args: { fileName: file.name },\n     *       signedURL: true,\n     *       uploadURL: true,\n     *     }\n     *   })\n     * \n     *   const { signedURL, uploadURL } = getUploadSignedURL\n     * \n     *   await fetch(signedURL, { method: 'PUT', body: file })\n     * \n     *   // done! do something with the uploadURL now\n     * }\n     * ```\n     * \n     */\n    getUploadSignedURL?: (GetUploadSignedURLGenqlSelection & { __args: {\n    /** SHA256 hash of the file. Used for reusing existing files. */\n    fileHash?: (Scalars['String'] | null), \n    /** The file name */\n    fileName: Scalars['String']} })\n    /** Start a job that can be awaited and the result given directly. Under the hood, it runs `transactionAsync` and polls for the result until it is available. You can pass a `timeout` argument, the default being 30_000ms. */\n    transaction?: (TransactionStatusGenqlSelection & { __args: {\n    /** The ID of the author of the transaction. If not provided, the API Token will be used. */\n    authorId?: (Scalars['String'] | null), \n    /** Auto make a commit in your Repo with the specified message. */\n    autoCommit?: (Scalars['String'] | null), \n    /** Transaction data. */\n    data: Scalars['String'], \n    /** Skip running workflows and event subscribers. Defaults to false. */\n    skipWorkflows?: (Scalars['Boolean'] | null), \n    /** Timeout in milliseconds. */\n    timeout?: (Scalars['Int'] | null)} })\n    /** Start an asynchronous job to mutate BaseHub data. Returns a transaction ID which you can use to get the result of the job. */\n    transactionAsync?: { __args: {\n    /** The ID of the author of the transaction. If not provided, the API Token will be used. */\n    authorId?: (Scalars['String'] | null), \n    /** Auto make a commit in your Repo with the specified message. */\n    autoCommit?: (Scalars['String'] | null), \n    /** Transaction data. */\n    data: Scalars['String'], \n    /** Skip running workflows and event subscribers. Defaults to false. */\n    skipWorkflows?: (Scalars['Boolean'] | null)} }\n    transactionStatus?: (TransactionStatusGenqlSelection & { __args: {\n    /** Transaction ID */\n    id: Scalars['String']} })\n    __typename?: boolean | number\n    __scalar?: boolean | number\n}\n\nexport interface NumberFilter {eq?: (Scalars['Float'] | null),gt?: (Scalars['Float'] | null),gte?: (Scalars['Float'] | null),isNull?: (Scalars['Boolean'] | null),lt?: (Scalars['Float'] | null),lte?: (Scalars['Float'] | null),neq?: (Scalars['Float'] | null)}\n\nexport interface PostsGenqlSelection{\n    _analyticsKey?: { __args: {\n    /**\n     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.\n     * \n     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.\n     */\n    scope?: (AnalyticsKeyScope | null)} } | boolean | number\n    _dashboardUrl?: boolean | number\n    _id?: boolean | number\n    _idPath?: boolean | number\n    _meta?: ListMetaGenqlSelection\n    /** The key used to search from the frontend. */\n    _searchKey?: boolean | number\n    _slug?: boolean | number\n    _slugPath?: boolean | number\n    _sys?: BlockDocumentSysGenqlSelection\n    _title?: boolean | number\n    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */\n    item?: PostsItemGenqlSelection\n    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */\n    items?: PostsItemGenqlSelection\n    __typename?: boolean | number\n    __scalar?: boolean | number\n}\n\nexport interface PostsItemGenqlSelection{\n    _analyticsKey?: { __args: {\n    /**\n     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.\n     * \n     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.\n     */\n    scope?: (AnalyticsKeyScope | null)} } | boolean | number\n    _dashboardUrl?: boolean | number\n    _id?: boolean | number\n    _idPath?: boolean | number\n    _slug?: boolean | number\n    _slugPath?: boolean | number\n    _sys?: BlockDocumentSysGenqlSelection\n    _title?: boolean | number\n    authors?: AuthorsItemGenqlSelection\n    body?: BodyGenqlSelection\n    categories?: CategoriesItemGenqlSelection\n    /** ISO 8601 date string. */\n    date?: boolean | number\n    description?: boolean | number\n    image?: BlockImageGenqlSelection\n    __typename?: boolean | number\n    __scalar?: boolean | number\n}\n\nexport interface PostsItemFilterInput {AND?: (PostsItemFilterInput | null),OR?: (PostsItemFilterInput | null),_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),authors?: (PostsItemFilterInput__authors_0___untitled | null),categories?: (PostsItemFilterInput__categories_0___untitled | null),date?: (DateFilter | null),description?: (StringFilter | null)}\n\nexport interface PostsItemFilterInput__authors_0___untitled {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null),xUrl?: (StringFilter | null)}\n\nexport interface PostsItemFilterInput__categories_0___untitled {_id?: (StringFilter | null),_slug?: (StringFilter | null),_sys_apiNamePath?: (StringFilter | null),_sys_createdAt?: (DateFilter | null),_sys_hash?: (StringFilter | null),_sys_id?: (StringFilter | null),_sys_idPath?: (StringFilter | null),_sys_lastModifiedAt?: (DateFilter | null),_sys_slug?: (StringFilter | null),_sys_slugPath?: (StringFilter | null),_sys_title?: (StringFilter | null),_title?: (StringFilter | null)}\n\nexport interface QueryGenqlSelection{\n    _agent?: (_AgentSTARTGenqlSelection & { __args: {\n    /** The ID of the agent. */\n    id: Scalars['String']} })\n    /** Query across the custom AI agents in the repository. */\n    _agents?: _agentsGenqlSelection\n    /** Query across all of the instances of a component. Pass in filters and sorts if you want, and get each instance via the `items` key. */\n    _componentInstances?: _componentsGenqlSelection\n    /** The structure of the repository. Used by START. */\n    _structure?: { __args: {\n    /** The format of the structure. */\n    format?: (_StructureFormatEnum | null), \n    /** The format of the structure. */\n    resolveTargetsWith?: (_ResolveTargetsWithEnum | null), \n    /** A target block to forcefully resolve in the schema. */\n    targetBlock?: (TargetBlock | null), \n    /** Whether to include constraints in the structure. */\n    withConstraints?: (Scalars['Boolean'] | null), \n    /** Whether to include IDs in the structure. */\n    withIDs?: (Scalars['Boolean'] | null), \n    /** Whether to include type options in the structure. */\n    withTypeOptions?: (Scalars['Boolean'] | null)} } | boolean | number\n    _sys?: RepoSysGenqlSelection\n    blog?: BlogGenqlSelection\n    legalPages?: (LegalPagesGenqlSelection & { __args?: {\n    /** Filter by a field. */\n    filter?: (LegalPagesItemFilterInput | null), \n    /** Limit the number of items returned. Defaults to 500. */\n    first?: (Scalars['Int'] | null), \n    /** Order by a field. */\n    orderBy?: (LegalPagesItemOrderByEnum | null), \n    /** Skip the first n items. */\n    skip?: (Scalars['Int'] | null)} })\n    __typename?: boolean | number\n    __scalar?: boolean | number\n}\n\nexport interface RepoSysGenqlSelection{\n    branches?: (_BranchesGenqlSelection & { __args?: {limit?: (Scalars['Int'] | null), offset?: (Scalars['Int'] | null)} })\n    hash?: boolean | number\n    id?: boolean | number\n    playgroundInfo?: _PlaygroundInfoGenqlSelection\n    slug?: boolean | number\n    title?: boolean | number\n    __typename?: boolean | number\n    __scalar?: boolean | number\n}\n\nexport interface RichTextJsonGenqlSelection{\n    content?: boolean | number\n    toc?: boolean | number\n    on_BaseRichTextJson?: BaseRichTextJsonGenqlSelection\n    on_BodyRichText?: BodyRichTextGenqlSelection\n    on_Body_1RichText?: Body_1RichTextGenqlSelection\n    __typename?: boolean | number\n    __scalar?: boolean | number\n}\n\nexport interface SelectFilter {excludes?: (Scalars['String'] | null),excludesAll?: (Scalars['String'][] | null),includes?: (Scalars['String'] | null),includesAll?: (Scalars['String'][] | null),includesAny?: (Scalars['String'][] | null),isEmpty?: (Scalars['Boolean'] | null)}\n\nexport interface StringFilter {contains?: (Scalars['String'] | null),endsWith?: (Scalars['String'] | null),eq?: (Scalars['String'] | null),in?: (Scalars['String'][] | null),isNull?: (Scalars['Boolean'] | null),matches?: (StringMatchesFilter | null),notEq?: (Scalars['String'] | null),notIn?: (Scalars['String'][] | null),startsWith?: (Scalars['String'] | null)}\n\nexport interface StringMatchesFilter {caseSensitive?: (Scalars['Boolean'] | null),pattern: Scalars['String']}\n\nexport interface TargetBlock {focus?: (Scalars['Boolean'] | null),id: Scalars['String'],label: Scalars['String']}\n\nexport interface TransactionStatusGenqlSelection{\n    /** Duration in milliseconds. */\n    duration?: boolean | number\n    endedAt?: boolean | number\n    id?: boolean | number\n    message?: boolean | number\n    startedAt?: boolean | number\n    status?: boolean | number\n    __typename?: boolean | number\n    __scalar?: boolean | number\n}\n\nexport interface VariantGenqlSelection{\n    apiName?: boolean | number\n    color?: boolean | number\n    id?: boolean | number\n    isDefault?: boolean | number\n    label?: boolean | number\n    __typename?: boolean | number\n    __scalar?: boolean | number\n}\n\nexport interface _AgentSTARTGenqlSelection{\n    _agentKey?: boolean | number\n    _analyticsKey?: { __args: {\n    /**\n     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.\n     * \n     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.\n     */\n    scope?: (AnalyticsKeyScope | null)} } | boolean | number\n    _dashboardUrl?: boolean | number\n    _id?: boolean | number\n    _idPath?: boolean | number\n    _slug?: boolean | number\n    _slugPath?: boolean | number\n    _sys?: BlockDocumentSysGenqlSelection\n    _title?: boolean | number\n    accent?: boolean | number\n    avatar?: boolean | number\n    chatUrl?: boolean | number\n    commit?: boolean | number\n    description?: boolean | number\n    edit?: boolean | number\n    embedUrl?: boolean | number\n    getUserInfo?: boolean | number\n    grayscale?: boolean | number\n    manageBranches?: boolean | number\n    mcpUrl?: boolean | number\n    model?: boolean | number\n    searchTheWeb?: boolean | number\n    slackInstallUrl?: boolean | number\n    systemPrompt?: boolean | number\n    __typename?: boolean | number\n    __scalar?: boolean | number\n}\n\nexport interface _BranchInfoGenqlSelection{\n    archivedAt?: boolean | number\n    archivedBy?: boolean | number\n    authorId?: boolean | number\n    contributors?: boolean | number\n    createdAt?: boolean | number\n    description?: boolean | number\n    git?: _GitInfoGenqlSelection\n    headCommit?: _CommitInfoGenqlSelection\n    headCommitId?: boolean | number\n    id?: boolean | number\n    inlineSuggestionAppliedAt?: boolean | number\n    isDefault?: boolean | number\n    isInlineSuggestion?: boolean | number\n    name?: boolean | number\n    playgroundId?: boolean | number\n    rollbackCommitId?: boolean | number\n    rollbackIsoDate?: boolean | number\n    sourceBranchId?: boolean | number\n    updatedAt?: boolean | number\n    workingRootBlockId?: boolean | number\n    __typename?: boolean | number\n    __scalar?: boolean | number\n}\n\nexport interface _BranchesGenqlSelection{\n    _meta?: ListMetaGenqlSelection\n    items?: _BranchInfoGenqlSelection\n    __typename?: boolean | number\n    __scalar?: boolean | number\n}\n\nexport interface _CommitInfoGenqlSelection{\n    authorId?: boolean | number\n    branchId?: boolean | number\n    contributors?: boolean | number\n    createdAt?: boolean | number\n    hash?: boolean | number\n    id?: boolean | number\n    mergeParentCommitId?: boolean | number\n    message?: boolean | number\n    parentCommitId?: boolean | number\n    /** Whether this commit is from a playground branch. */\n    playgroundId?: boolean | number\n    repoId?: boolean | number\n    rootBlockId?: boolean | number\n    __typename?: boolean | number\n    __scalar?: boolean | number\n}\n\nexport interface _GitInfoGenqlSelection{\n    branch?: boolean | number\n    deploymentUrl?: boolean | number\n    __typename?: boolean | number\n    __scalar?: boolean | number\n}\n\nexport interface _PlaygroundInfoGenqlSelection{\n    claimUrl?: boolean | number\n    editUrl?: boolean | number\n    expiresAt?: boolean | number\n    id?: boolean | number\n    __typename?: boolean | number\n    __scalar?: boolean | number\n}\n\nexport interface _agentsGenqlSelection{\n    start?: _AgentSTARTGenqlSelection\n    __typename?: boolean | number\n    __scalar?: boolean | number\n}\n\nexport interface _componentsGenqlSelection{\n    authorsItem?: (authorsItem_AsListGenqlSelection & { __args?: {\n    /** Filter by a field. */\n    filter?: (AuthorsItemFilterInput | null), \n    /** Limit the number of items returned. Defaults to 500. */\n    first?: (Scalars['Int'] | null), \n    /** Order by a field. */\n    orderBy?: (AuthorsItemOrderByEnum | null), \n    /** Skip the first n items. */\n    skip?: (Scalars['Int'] | null)} })\n    categoriesItem?: (categoriesItem_AsListGenqlSelection & { __args?: {\n    /** Filter by a field. */\n    filter?: (CategoriesItemFilterInput | null), \n    /** Limit the number of items returned. Defaults to 500. */\n    first?: (Scalars['Int'] | null), \n    /** Order by a field. */\n    orderBy?: (CategoriesItemOrderByEnum | null), \n    /** Skip the first n items. */\n    skip?: (Scalars['Int'] | null)} })\n    legalPagesItem?: (legalPagesItem_AsListGenqlSelection & { __args?: {\n    /** Filter by a field. */\n    filter?: (LegalPagesItemFilterInput | null), \n    /** Limit the number of items returned. Defaults to 500. */\n    first?: (Scalars['Int'] | null), \n    /** Order by a field. */\n    orderBy?: (LegalPagesItemOrderByEnum | null), \n    /** Skip the first n items. */\n    skip?: (Scalars['Int'] | null)} })\n    postsItem?: (postsItem_AsListGenqlSelection & { __args?: {\n    /** Filter by a field. */\n    filter?: (PostsItemFilterInput | null), \n    /** Limit the number of items returned. Defaults to 500. */\n    first?: (Scalars['Int'] | null), \n    /** Order by a field. */\n    orderBy?: (PostsItemOrderByEnum | null), \n    /** Skip the first n items. */\n    skip?: (Scalars['Int'] | null)} })\n    __typename?: boolean | number\n    __scalar?: boolean | number\n}\n\nexport interface authorsItem_AsListGenqlSelection{\n    _analyticsKey?: { __args: {\n    /**\n     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.\n     * \n     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.\n     */\n    scope?: (AnalyticsKeyScope | null)} } | boolean | number\n    _dashboardUrl?: boolean | number\n    _id?: boolean | number\n    _idPath?: boolean | number\n    _meta?: ListMetaGenqlSelection\n    /** The key used to search from the frontend. */\n    _searchKey?: boolean | number\n    _slug?: boolean | number\n    _slugPath?: boolean | number\n    _sys?: BlockDocumentSysGenqlSelection\n    _title?: boolean | number\n    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */\n    item?: AuthorsItemGenqlSelection\n    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */\n    items?: AuthorsItemGenqlSelection\n    __typename?: boolean | number\n    __scalar?: boolean | number\n}\n\nexport interface categoriesItem_AsListGenqlSelection{\n    _analyticsKey?: { __args: {\n    /**\n     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.\n     * \n     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.\n     */\n    scope?: (AnalyticsKeyScope | null)} } | boolean | number\n    _dashboardUrl?: boolean | number\n    _id?: boolean | number\n    _idPath?: boolean | number\n    _meta?: ListMetaGenqlSelection\n    /** The key used to search from the frontend. */\n    _searchKey?: boolean | number\n    _slug?: boolean | number\n    _slugPath?: boolean | number\n    _sys?: BlockDocumentSysGenqlSelection\n    _title?: boolean | number\n    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */\n    item?: CategoriesItemGenqlSelection\n    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */\n    items?: CategoriesItemGenqlSelection\n    __typename?: boolean | number\n    __scalar?: boolean | number\n}\n\nexport interface legalPagesItem_AsListGenqlSelection{\n    _analyticsKey?: { __args: {\n    /**\n     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.\n     * \n     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.\n     */\n    scope?: (AnalyticsKeyScope | null)} } | boolean | number\n    _dashboardUrl?: boolean | number\n    _id?: boolean | number\n    _idPath?: boolean | number\n    _meta?: ListMetaGenqlSelection\n    /** The key used to search from the frontend. */\n    _searchKey?: boolean | number\n    _slug?: boolean | number\n    _slugPath?: boolean | number\n    _sys?: BlockDocumentSysGenqlSelection\n    _title?: boolean | number\n    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */\n    item?: LegalPagesItemGenqlSelection\n    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */\n    items?: LegalPagesItemGenqlSelection\n    __typename?: boolean | number\n    __scalar?: boolean | number\n}\n\nexport interface postsItem_AsListGenqlSelection{\n    _analyticsKey?: { __args: {\n    /**\n     * The scope of the analytics key. Use `send` for just ingesting data. Use `query` if you need to show an analytics data in your website.\n     * \n     * Have in mind, if you expose your `query` analytics key in the frontend, you'll be exposing all of this block's analytics data to the public. This is generally safe, but it might not be in your case.\n     */\n    scope?: (AnalyticsKeyScope | null)} } | boolean | number\n    _dashboardUrl?: boolean | number\n    _id?: boolean | number\n    _idPath?: boolean | number\n    _meta?: ListMetaGenqlSelection\n    /** The key used to search from the frontend. */\n    _searchKey?: boolean | number\n    _slug?: boolean | number\n    _slugPath?: boolean | number\n    _sys?: BlockDocumentSysGenqlSelection\n    _title?: boolean | number\n    /** Returns the first item in the list, or null if the list is empty. Useful when you expect only one result. */\n    item?: PostsItemGenqlSelection\n    /** Returns the list of items after filtering and paginating according to the arguments sent by the client. */\n    items?: PostsItemGenqlSelection\n    __typename?: boolean | number\n    __scalar?: boolean | number\n}\n\ntype FragmentsMap = {\n  Authors: {\n    root: Authors,\n    selection: AuthorsGenqlSelection,\n}\n  AuthorsItem: {\n    root: AuthorsItem,\n    selection: AuthorsItemGenqlSelection,\n}\n  BaseRichTextJson: {\n    root: BaseRichTextJson,\n    selection: BaseRichTextJsonGenqlSelection,\n}\n  BlockAudio: {\n    root: BlockAudio,\n    selection: BlockAudioGenqlSelection,\n}\n  BlockCodeSnippet: {\n    root: BlockCodeSnippet,\n    selection: BlockCodeSnippetGenqlSelection,\n}\n  BlockColor: {\n    root: BlockColor,\n    selection: BlockColorGenqlSelection,\n}\n  BlockDocument: {\n    root: BlockDocument,\n    selection: BlockDocumentGenqlSelection,\n}\n  BlockDocumentSys: {\n    root: BlockDocumentSys,\n    selection: BlockDocumentSysGenqlSelection,\n}\n  BlockFile: {\n    root: BlockFile,\n    selection: BlockFileGenqlSelection,\n}\n  BlockImage: {\n    root: BlockImage,\n    selection: BlockImageGenqlSelection,\n}\n  BlockList: {\n    root: BlockList,\n    selection: BlockListGenqlSelection,\n}\n  BlockOgImage: {\n    root: BlockOgImage,\n    selection: BlockOgImageGenqlSelection,\n}\n  BlockRichText: {\n    root: BlockRichText,\n    selection: BlockRichTextGenqlSelection,\n}\n  BlockVideo: {\n    root: BlockVideo,\n    selection: BlockVideoGenqlSelection,\n}\n  Blog: {\n    root: Blog,\n    selection: BlogGenqlSelection,\n}\n  Body: {\n    root: Body,\n    selection: BodyGenqlSelection,\n}\n  BodyRichText: {\n    root: BodyRichText,\n    selection: BodyRichTextGenqlSelection,\n}\n  Body_1: {\n    root: Body_1,\n    selection: Body_1GenqlSelection,\n}\n  Body_1RichText: {\n    root: Body_1RichText,\n    selection: Body_1RichTextGenqlSelection,\n}\n  Categories: {\n    root: Categories,\n    selection: CategoriesGenqlSelection,\n}\n  CategoriesItem: {\n    root: CategoriesItem,\n    selection: CategoriesItemGenqlSelection,\n}\n  GetUploadSignedURL: {\n    root: GetUploadSignedURL,\n    selection: GetUploadSignedURLGenqlSelection,\n}\n  LegalPages: {\n    root: LegalPages,\n    selection: LegalPagesGenqlSelection,\n}\n  LegalPagesItem: {\n    root: LegalPagesItem,\n    selection: LegalPagesItemGenqlSelection,\n}\n  ListMeta: {\n    root: ListMeta,\n    selection: ListMetaGenqlSelection,\n}\n  MediaBlock: {\n    root: MediaBlock,\n    selection: MediaBlockGenqlSelection,\n}\n  Mutation: {\n    root: Mutation,\n    selection: MutationGenqlSelection,\n}\n  Posts: {\n    root: Posts,\n    selection: PostsGenqlSelection,\n}\n  PostsItem: {\n    root: PostsItem,\n    selection: PostsItemGenqlSelection,\n}\n  Query: {\n    root: Query,\n    selection: QueryGenqlSelection,\n}\n  RepoSys: {\n    root: RepoSys,\n    selection: RepoSysGenqlSelection,\n}\n  RichTextJson: {\n    root: RichTextJson,\n    selection: RichTextJsonGenqlSelection,\n}\n  TransactionStatus: {\n    root: TransactionStatus,\n    selection: TransactionStatusGenqlSelection,\n}\n  Variant: {\n    root: Variant,\n    selection: VariantGenqlSelection,\n}\n  _AgentSTART: {\n    root: _AgentSTART,\n    selection: _AgentSTARTGenqlSelection,\n}\n  _BranchInfo: {\n    root: _BranchInfo,\n    selection: _BranchInfoGenqlSelection,\n}\n  _Branches: {\n    root: _Branches,\n    selection: _BranchesGenqlSelection,\n}\n  _CommitInfo: {\n    root: _CommitInfo,\n    selection: _CommitInfoGenqlSelection,\n}\n  _GitInfo: {\n    root: _GitInfo,\n    selection: _GitInfoGenqlSelection,\n}\n  _PlaygroundInfo: {\n    root: _PlaygroundInfo,\n    selection: _PlaygroundInfoGenqlSelection,\n}\n  _agents: {\n    root: _agents,\n    selection: _agentsGenqlSelection,\n}\n  _components: {\n    root: _components,\n    selection: _componentsGenqlSelection,\n}\n  authorsItem_AsList: {\n    root: authorsItem_AsList,\n    selection: authorsItem_AsListGenqlSelection,\n}\n  categoriesItem_AsList: {\n    root: categoriesItem_AsList,\n    selection: categoriesItem_AsListGenqlSelection,\n}\n  legalPagesItem_AsList: {\n    root: legalPagesItem_AsList,\n    selection: legalPagesItem_AsListGenqlSelection,\n}\n  postsItem_AsList: {\n    root: postsItem_AsList,\n    selection: postsItem_AsListGenqlSelection,\n}\n}\n\nimport { FieldsSelection } from \"./runtime\";\n\nexport function fragmentOn<\n    TypeName extends keyof FragmentsMap,\n    Selection extends FragmentsMap[TypeName][\"selection\"],\n>(name: TypeName, fields: Selection) {\n  return { __fragmentOn: name, ...fields } as const;\n}\n\n// credits: https://stackoverflow.com/a/54487392\ntype OmitDistributive<T, K extends PropertyKey> = T extends any\n    ? T extends object\n        ? Id<OmitRecursively<T, K>>\n        : T\n    : never\ntype Id<T> = {} & { [P in keyof T]: T[P] } // Cosmetic use only makes the tooltips expad the type can be removed\ntype OmitRecursively<T, K extends PropertyKey> = Omit<\n    { [P in keyof T]: OmitDistributive<T[P], K> },\n    K\n>\n\nexport namespace fragmentOn {\n    export type infer<T> = T extends {\n      __fragmentOn: infer U extends keyof FragmentsMap;\n    }\n      ? OmitRecursively<FieldsSelection<FragmentsMap[U][\"root\"], Omit<T, \"__fragmentOn\">>, \"__fragmentOn\">\n      : never;\n  }\n\n\n// This is a BaseHub-specific thing:\n\ntype RecursiveCollection<T, Key extends keyof T> = T & {\n[key in Key]: { items: RecursiveCollection<T, Key> };\n};\n\nexport function fragmentOnRecursiveCollection<\n  TypeName extends keyof FragmentsMap,\n  Selection extends FragmentsMap[TypeName][\"selection\"],\n  RecursiveKey extends keyof FragmentsMap[TypeName][\"selection\"]\n>(\n  name: TypeName,\n  fields: Selection,\n  options: {\n    recursiveKey: RecursiveKey;\n    levels: number;\n    getLevelArgs?: (level: number) => unknown;\n  }\n) {\n  let current = {\n    ...fields,\n  } as RecursiveCollection<\n    { readonly __fragmentOn: TypeName } & Selection,\n    RecursiveKey\n  >;\n  if (options.levels > 0) {\n    current[options.recursiveKey] = {\n      ...(options.getLevelArgs\n        ? { __args: options.getLevelArgs(options.levels) }\n        : {}),\n      items: fragmentOnRecursiveCollection(name, fields, {\n        ...options,\n        levels: options.levels - 1,\n      }),\n    } as any;\n  }\n  return current;\n}\n\n\n\n\n    const Authors_possibleTypes: string[] = ['Authors']\n    export const isAuthors = (obj?: { __typename?: any } | null): obj is Authors => {\n      if (!obj?.__typename) throw new Error('__typename is missing in \"isAuthors\"')\n      return Authors_possibleTypes.includes(obj.__typename)\n    }\n    \n\n\n    const AuthorsItem_possibleTypes: string[] = ['AuthorsItem']\n    export const isAuthorsItem = (obj?: { __typename?: any } | null): obj is AuthorsItem => {\n      if (!obj?.__typename) throw new Error('__typename is missing in \"isAuthorsItem\"')\n      return AuthorsItem_possibleTypes.includes(obj.__typename)\n    }\n    \n\n\n    const BaseRichTextJson_possibleTypes: string[] = ['BaseRichTextJson']\n    export const isBaseRichTextJson = (obj?: { __typename?: any } | null): obj is BaseRichTextJson => {\n      if (!obj?.__typename) throw new Error('__typename is missing in \"isBaseRichTextJson\"')\n      return BaseRichTextJson_possibleTypes.includes(obj.__typename)\n    }\n    \n\n\n    const BlockAudio_possibleTypes: string[] = ['BlockAudio']\n    export const isBlockAudio = (obj?: { __typename?: any } | null): obj is BlockAudio => {\n      if (!obj?.__typename) throw new Error('__typename is missing in \"isBlockAudio\"')\n      return BlockAudio_possibleTypes.includes(obj.__typename)\n    }\n    \n\n\n    const BlockCodeSnippet_possibleTypes: string[] = ['BlockCodeSnippet']\n    export const isBlockCodeSnippet = (obj?: { __typename?: any } | null): obj is BlockCodeSnippet => {\n      if (!obj?.__typename) throw new Error('__typename is missing in \"isBlockCodeSnippet\"')\n      return BlockCodeSnippet_possibleTypes.includes(obj.__typename)\n    }\n    \n\n\n    const BlockColor_possibleTypes: string[] = ['BlockColor']\n    export const isBlockColor = (obj?: { __typename?: any } | null): obj is BlockColor => {\n      if (!obj?.__typename) throw new Error('__typename is missing in \"isBlockColor\"')\n      return BlockColor_possibleTypes.includes(obj.__typename)\n    }\n    \n\n\n    const BlockDocument_possibleTypes: string[] = ['Authors','AuthorsItem','Blog','Categories','CategoriesItem','LegalPages','LegalPagesItem','Posts','PostsItem','_AgentSTART','authorsItem_AsList','categoriesItem_AsList','legalPagesItem_AsList','postsItem_AsList']\n    export const isBlockDocument = (obj?: { __typename?: any } | null): obj is BlockDocument => {\n      if (!obj?.__typename) throw new Error('__typename is missing in \"isBlockDocument\"')\n      return BlockDocument_possibleTypes.includes(obj.__typename)\n    }\n    \n\n\n    const BlockDocumentSys_possibleTypes: string[] = ['BlockDocumentSys']\n    export const isBlockDocumentSys = (obj?: { __typename?: any } | null): obj is BlockDocumentSys => {\n      if (!obj?.__typename) throw new Error('__typename is missing in \"isBlockDocumentSys\"')\n      return BlockDocumentSys_possibleTypes.includes(obj.__typename)\n    }\n    \n\n\n    const BlockFile_possibleTypes: string[] = ['BlockFile']\n    export const isBlockFile = (obj?: { __typename?: any } | null): obj is BlockFile => {\n      if (!obj?.__typename) throw new Error('__typename is missing in \"isBlockFile\"')\n      return BlockFile_possibleTypes.includes(obj.__typename)\n    }\n    \n\n\n    const BlockImage_possibleTypes: string[] = ['BlockImage']\n    export const isBlockImage = (obj?: { __typename?: any } | null): obj is BlockImage => {\n      if (!obj?.__typename) throw new Error('__typename is missing in \"isBlockImage\"')\n      return BlockImage_possibleTypes.includes(obj.__typename)\n    }\n    \n\n\n    const BlockList_possibleTypes: string[] = ['Authors','Categories','LegalPages','Posts','authorsItem_AsList','categoriesItem_AsList','legalPagesItem_AsList','postsItem_AsList']\n    export const isBlockList = (obj?: { __typename?: any } | null): obj is BlockList => {\n      if (!obj?.__typename) throw new Error('__typename is missing in \"isBlockList\"')\n      return BlockList_possibleTypes.includes(obj.__typename)\n    }\n    \n\n\n    const BlockOgImage_possibleTypes: string[] = ['BlockOgImage']\n    export const isBlockOgImage = (obj?: { __typename?: any } | null): obj is BlockOgImage => {\n      if (!obj?.__typename) throw new Error('__typename is missing in \"isBlockOgImage\"')\n      return BlockOgImage_possibleTypes.includes(obj.__typename)\n    }\n    \n\n\n    const BlockRichText_possibleTypes: string[] = ['Body','Body_1']\n    export const isBlockRichText = (obj?: { __typename?: any } | null): obj is BlockRichText => {\n      if (!obj?.__typename) throw new Error('__typename is missing in \"isBlockRichText\"')\n      return BlockRichText_possibleTypes.includes(obj.__typename)\n    }\n    \n\n\n    const BlockVideo_possibleTypes: string[] = ['BlockVideo']\n    export const isBlockVideo = (obj?: { __typename?: any } | null): obj is BlockVideo => {\n      if (!obj?.__typename) throw new Error('__typename is missing in \"isBlockVideo\"')\n      return BlockVideo_possibleTypes.includes(obj.__typename)\n    }\n    \n\n\n    const Blog_possibleTypes: string[] = ['Blog']\n    export const isBlog = (obj?: { __typename?: any } | null): obj is Blog => {\n      if (!obj?.__typename) throw new Error('__typename is missing in \"isBlog\"')\n      return Blog_possibleTypes.includes(obj.__typename)\n    }\n    \n\n\n    const Body_possibleTypes: string[] = ['Body']\n    export const isBody = (obj?: { __typename?: any } | null): obj is Body => {\n      if (!obj?.__typename) throw new Error('__typename is missing in \"isBody\"')\n      return Body_possibleTypes.includes(obj.__typename)\n    }\n    \n\n\n    const BodyRichText_possibleTypes: string[] = ['BodyRichText']\n    export const isBodyRichText = (obj?: { __typename?: any } | null): obj is BodyRichText => {\n      if (!obj?.__typename) throw new Error('__typename is missing in \"isBodyRichText\"')\n      return BodyRichText_possibleTypes.includes(obj.__typename)\n    }\n    \n\n\n    const Body_1_possibleTypes: string[] = ['Body_1']\n    export const isBody_1 = (obj?: { __typename?: any } | null): obj is Body_1 => {\n      if (!obj?.__typename) throw new Error('__typename is missing in \"isBody_1\"')\n      return Body_1_possibleTypes.includes(obj.__typename)\n    }\n    \n\n\n    const Body_1RichText_possibleTypes: string[] = ['Body_1RichText']\n    export const isBody_1RichText = (obj?: { __typename?: any } | null): obj is Body_1RichText => {\n      if (!obj?.__typename) throw new Error('__typename is missing in \"isBody_1RichText\"')\n      return Body_1RichText_possibleTypes.includes(obj.__typename)\n    }\n    \n\n\n    const Categories_possibleTypes: string[] = ['Categories']\n    export const isCategories = (obj?: { __typename?: any } | null): obj is Categories => {\n      if (!obj?.__typename) throw new Error('__typename is missing in \"isCategories\"')\n      return Categories_possibleTypes.includes(obj.__typename)\n    }\n    \n\n\n    const CategoriesItem_possibleTypes: string[] = ['CategoriesItem']\n    export const isCategoriesItem = (obj?: { __typename?: any } | null): obj is CategoriesItem => {\n      if (!obj?.__typename) throw new Error('__typename is missing in \"isCategoriesItem\"')\n      return CategoriesItem_possibleTypes.includes(obj.__typename)\n    }\n    \n\n\n    const GetUploadSignedURL_possibleTypes: string[] = ['GetUploadSignedURL']\n    export const isGetUploadSignedURL = (obj?: { __typename?: any } | null): obj is GetUploadSignedURL => {\n      if (!obj?.__typename) throw new Error('__typename is missing in \"isGetUploadSignedURL\"')\n      return GetUploadSignedURL_possibleTypes.includes(obj.__typename)\n    }\n    \n\n\n    const LegalPages_possibleTypes: string[] = ['LegalPages']\n    export const isLegalPages = (obj?: { __typename?: any } | null): obj is LegalPages => {\n      if (!obj?.__typename) throw new Error('__typename is missing in \"isLegalPages\"')\n      return LegalPages_possibleTypes.includes(obj.__typename)\n    }\n    \n\n\n    const LegalPagesItem_possibleTypes: string[] = ['LegalPagesItem']\n    export const isLegalPagesItem = (obj?: { __typename?: any } | null): obj is LegalPagesItem => {\n      if (!obj?.__typename) throw new Error('__typename is missing in \"isLegalPagesItem\"')\n      return LegalPagesItem_possibleTypes.includes(obj.__typename)\n    }\n    \n\n\n    const ListMeta_possibleTypes: string[] = ['ListMeta']\n    export const isListMeta = (obj?: { __typename?: any } | null): obj is ListMeta => {\n      if (!obj?.__typename) throw new Error('__typename is missing in \"isListMeta\"')\n      return ListMeta_possibleTypes.includes(obj.__typename)\n    }\n    \n\n\n    const MediaBlock_possibleTypes: string[] = ['BlockAudio','BlockFile','BlockImage','BlockVideo']\n    export const isMediaBlock = (obj?: { __typename?: any } | null): obj is MediaBlock => {\n      if (!obj?.__typename) throw new Error('__typename is missing in \"isMediaBlock\"')\n      return MediaBlock_possibleTypes.includes(obj.__typename)\n    }\n    \n\n\n    const MediaBlockUnion_possibleTypes: string[] = ['BlockAudio','BlockFile','BlockImage','BlockVideo']\n    export const isMediaBlockUnion = (obj?: { __typename?: any } | null): obj is MediaBlockUnion => {\n      if (!obj?.__typename) throw new Error('__typename is missing in \"isMediaBlockUnion\"')\n      return MediaBlockUnion_possibleTypes.includes(obj.__typename)\n    }\n    \n\n\n    const Mutation_possibleTypes: string[] = ['Mutation']\n    export const isMutation = (obj?: { __typename?: any } | null): obj is Mutation => {\n      if (!obj?.__typename) throw new Error('__typename is missing in \"isMutation\"')\n      return Mutation_possibleTypes.includes(obj.__typename)\n    }\n    \n\n\n    const Posts_possibleTypes: string[] = ['Posts']\n    export const isPosts = (obj?: { __typename?: any } | null): obj is Posts => {\n      if (!obj?.__typename) throw new Error('__typename is missing in \"isPosts\"')\n      return Posts_possibleTypes.includes(obj.__typename)\n    }\n    \n\n\n    const PostsItem_possibleTypes: string[] = ['PostsItem']\n    export const isPostsItem = (obj?: { __typename?: any } | null): obj is PostsItem => {\n      if (!obj?.__typename) throw new Error('__typename is missing in \"isPostsItem\"')\n      return PostsItem_possibleTypes.includes(obj.__typename)\n    }\n    \n\n\n    const Query_possibleTypes: string[] = ['Query']\n    export const isQuery = (obj?: { __typename?: any } | null): obj is Query => {\n      if (!obj?.__typename) throw new Error('__typename is missing in \"isQuery\"')\n      return Query_possibleTypes.includes(obj.__typename)\n    }\n    \n\n\n    const RepoSys_possibleTypes: string[] = ['RepoSys']\n    export const isRepoSys = (obj?: { __typename?: any } | null): obj is RepoSys => {\n      if (!obj?.__typename) throw new Error('__typename is missing in \"isRepoSys\"')\n      return RepoSys_possibleTypes.includes(obj.__typename)\n    }\n    \n\n\n    const RichTextJson_possibleTypes: string[] = ['BaseRichTextJson','BodyRichText','Body_1RichText']\n    export const isRichTextJson = (obj?: { __typename?: any } | null): obj is RichTextJson => {\n      if (!obj?.__typename) throw new Error('__typename is missing in \"isRichTextJson\"')\n      return RichTextJson_possibleTypes.includes(obj.__typename)\n    }\n    \n\n\n    const TransactionStatus_possibleTypes: string[] = ['TransactionStatus']\n    export const isTransactionStatus = (obj?: { __typename?: any } | null): obj is TransactionStatus => {\n      if (!obj?.__typename) throw new Error('__typename is missing in \"isTransactionStatus\"')\n      return TransactionStatus_possibleTypes.includes(obj.__typename)\n    }\n    \n\n\n    const Variant_possibleTypes: string[] = ['Variant']\n    export const isVariant = (obj?: { __typename?: any } | null): obj is Variant => {\n      if (!obj?.__typename) throw new Error('__typename is missing in \"isVariant\"')\n      return Variant_possibleTypes.includes(obj.__typename)\n    }\n    \n\n\n    const _AgentSTART_possibleTypes: string[] = ['_AgentSTART']\n    export const is_AgentSTART = (obj?: { __typename?: any } | null): obj is _AgentSTART => {\n      if (!obj?.__typename) throw new Error('__typename is missing in \"is_AgentSTART\"')\n      return _AgentSTART_possibleTypes.includes(obj.__typename)\n    }\n    \n\n\n    const _BranchInfo_possibleTypes: string[] = ['_BranchInfo']\n    export const is_BranchInfo = (obj?: { __typename?: any } | null): obj is _BranchInfo => {\n      if (!obj?.__typename) throw new Error('__typename is missing in \"is_BranchInfo\"')\n      return _BranchInfo_possibleTypes.includes(obj.__typename)\n    }\n    \n\n\n    const _Branches_possibleTypes: string[] = ['_Branches']\n    export const is_Branches = (obj?: { __typename?: any } | null): obj is _Branches => {\n      if (!obj?.__typename) throw new Error('__typename is missing in \"is_Branches\"')\n      return _Branches_possibleTypes.includes(obj.__typename)\n    }\n    \n\n\n    const _CommitInfo_possibleTypes: string[] = ['_CommitInfo']\n    export const is_CommitInfo = (obj?: { __typename?: any } | null): obj is _CommitInfo => {\n      if (!obj?.__typename) throw new Error('__typename is missing in \"is_CommitInfo\"')\n      return _CommitInfo_possibleTypes.includes(obj.__typename)\n    }\n    \n\n\n    const _GitInfo_possibleTypes: string[] = ['_GitInfo']\n    export const is_GitInfo = (obj?: { __typename?: any } | null): obj is _GitInfo => {\n      if (!obj?.__typename) throw new Error('__typename is missing in \"is_GitInfo\"')\n      return _GitInfo_possibleTypes.includes(obj.__typename)\n    }\n    \n\n\n    const _PlaygroundInfo_possibleTypes: string[] = ['_PlaygroundInfo']\n    export const is_PlaygroundInfo = (obj?: { __typename?: any } | null): obj is _PlaygroundInfo => {\n      if (!obj?.__typename) throw new Error('__typename is missing in \"is_PlaygroundInfo\"')\n      return _PlaygroundInfo_possibleTypes.includes(obj.__typename)\n    }\n    \n\n\n    const _agents_possibleTypes: string[] = ['_agents']\n    export const is_agents = (obj?: { __typename?: any } | null): obj is _agents => {\n      if (!obj?.__typename) throw new Error('__typename is missing in \"is_agents\"')\n      return _agents_possibleTypes.includes(obj.__typename)\n    }\n    \n\n\n    const _components_possibleTypes: string[] = ['_components']\n    export const is_components = (obj?: { __typename?: any } | null): obj is _components => {\n      if (!obj?.__typename) throw new Error('__typename is missing in \"is_components\"')\n      return _components_possibleTypes.includes(obj.__typename)\n    }\n    \n\n\n    const authorsItem_AsList_possibleTypes: string[] = ['authorsItem_AsList']\n    export const isauthorsItem_AsList = (obj?: { __typename?: any } | null): obj is authorsItem_AsList => {\n      if (!obj?.__typename) throw new Error('__typename is missing in \"isauthorsItem_AsList\"')\n      return authorsItem_AsList_possibleTypes.includes(obj.__typename)\n    }\n    \n\n\n    const categoriesItem_AsList_possibleTypes: string[] = ['categoriesItem_AsList']\n    export const iscategoriesItem_AsList = (obj?: { __typename?: any } | null): obj is categoriesItem_AsList => {\n      if (!obj?.__typename) throw new Error('__typename is missing in \"iscategoriesItem_AsList\"')\n      return categoriesItem_AsList_possibleTypes.includes(obj.__typename)\n    }\n    \n\n\n    const legalPagesItem_AsList_possibleTypes: string[] = ['legalPagesItem_AsList']\n    export const islegalPagesItem_AsList = (obj?: { __typename?: any } | null): obj is legalPagesItem_AsList => {\n      if (!obj?.__typename) throw new Error('__typename is missing in \"islegalPagesItem_AsList\"')\n      return legalPagesItem_AsList_possibleTypes.includes(obj.__typename)\n    }\n    \n\n\n    const postsItem_AsList_possibleTypes: string[] = ['postsItem_AsList']\n    export const ispostsItem_AsList = (obj?: { __typename?: any } | null): obj is postsItem_AsList => {\n      if (!obj?.__typename) throw new Error('__typename is missing in \"ispostsItem_AsList\"')\n      return postsItem_AsList_possibleTypes.includes(obj.__typename)\n    }\n    \n\nexport const enumAnalyticsKeyScope = {\n   query: 'query' as const,\n   send: 'send' as const\n}\n\nexport const enumAuthorsItemOrderByEnum = {\n   _sys_createdAt__ASC: '_sys_createdAt__ASC' as const,\n   _sys_createdAt__DESC: '_sys_createdAt__DESC' as const,\n   _sys_hash__ASC: '_sys_hash__ASC' as const,\n   _sys_hash__DESC: '_sys_hash__DESC' as const,\n   _sys_id__ASC: '_sys_id__ASC' as const,\n   _sys_id__DESC: '_sys_id__DESC' as const,\n   _sys_lastModifiedAt__ASC: '_sys_lastModifiedAt__ASC' as const,\n   _sys_lastModifiedAt__DESC: '_sys_lastModifiedAt__DESC' as const,\n   _sys_slug__ASC: '_sys_slug__ASC' as const,\n   _sys_slug__DESC: '_sys_slug__DESC' as const,\n   _sys_title__ASC: '_sys_title__ASC' as const,\n   _sys_title__DESC: '_sys_title__DESC' as const,\n   avatar__ASC: 'avatar__ASC' as const,\n   avatar__DESC: 'avatar__DESC' as const,\n   xUrl__ASC: 'xUrl__ASC' as const,\n   xUrl__DESC: 'xUrl__DESC' as const\n}\n\nexport const enumCategoriesItemOrderByEnum = {\n   _sys_createdAt__ASC: '_sys_createdAt__ASC' as const,\n   _sys_createdAt__DESC: '_sys_createdAt__DESC' as const,\n   _sys_hash__ASC: '_sys_hash__ASC' as const,\n   _sys_hash__DESC: '_sys_hash__DESC' as const,\n   _sys_id__ASC: '_sys_id__ASC' as const,\n   _sys_id__DESC: '_sys_id__DESC' as const,\n   _sys_lastModifiedAt__ASC: '_sys_lastModifiedAt__ASC' as const,\n   _sys_lastModifiedAt__DESC: '_sys_lastModifiedAt__DESC' as const,\n   _sys_slug__ASC: '_sys_slug__ASC' as const,\n   _sys_slug__DESC: '_sys_slug__DESC' as const,\n   _sys_title__ASC: '_sys_title__ASC' as const,\n   _sys_title__DESC: '_sys_title__DESC' as const\n}\n\nexport const enumLegalPagesItemOrderByEnum = {\n   _sys_createdAt__ASC: '_sys_createdAt__ASC' as const,\n   _sys_createdAt__DESC: '_sys_createdAt__DESC' as const,\n   _sys_hash__ASC: '_sys_hash__ASC' as const,\n   _sys_hash__DESC: '_sys_hash__DESC' as const,\n   _sys_id__ASC: '_sys_id__ASC' as const,\n   _sys_id__DESC: '_sys_id__DESC' as const,\n   _sys_lastModifiedAt__ASC: '_sys_lastModifiedAt__ASC' as const,\n   _sys_lastModifiedAt__DESC: '_sys_lastModifiedAt__DESC' as const,\n   _sys_slug__ASC: '_sys_slug__ASC' as const,\n   _sys_slug__DESC: '_sys_slug__DESC' as const,\n   _sys_title__ASC: '_sys_title__ASC' as const,\n   _sys_title__DESC: '_sys_title__DESC' as const,\n   body__ASC: 'body__ASC' as const,\n   body__DESC: 'body__DESC' as const,\n   description__ASC: 'description__ASC' as const,\n   description__DESC: 'description__DESC' as const\n}\n\nexport const enumPostsItemOrderByEnum = {\n   _sys_createdAt__ASC: '_sys_createdAt__ASC' as const,\n   _sys_createdAt__DESC: '_sys_createdAt__DESC' as const,\n   _sys_hash__ASC: '_sys_hash__ASC' as const,\n   _sys_hash__DESC: '_sys_hash__DESC' as const,\n   _sys_id__ASC: '_sys_id__ASC' as const,\n   _sys_id__DESC: '_sys_id__DESC' as const,\n   _sys_lastModifiedAt__ASC: '_sys_lastModifiedAt__ASC' as const,\n   _sys_lastModifiedAt__DESC: '_sys_lastModifiedAt__DESC' as const,\n   _sys_slug__ASC: '_sys_slug__ASC' as const,\n   _sys_slug__DESC: '_sys_slug__DESC' as const,\n   _sys_title__ASC: '_sys_title__ASC' as const,\n   _sys_title__DESC: '_sys_title__DESC' as const,\n   authors__ASC: 'authors__ASC' as const,\n   authors__DESC: 'authors__DESC' as const,\n   body__ASC: 'body__ASC' as const,\n   body__DESC: 'body__DESC' as const,\n   categories__ASC: 'categories__ASC' as const,\n   categories__DESC: 'categories__DESC' as const,\n   date__ASC: 'date__ASC' as const,\n   date__DESC: 'date__DESC' as const,\n   description__ASC: 'description__ASC' as const,\n   description__DESC: 'description__DESC' as const,\n   image__ASC: 'image__ASC' as const,\n   image__DESC: 'image__DESC' as const\n}\n\nexport const enumTransactionStatusEnum = {\n   Cancelled: 'Cancelled' as const,\n   Completed: 'Completed' as const,\n   Failed: 'Failed' as const,\n   Running: 'Running' as const,\n   Scheduled: 'Scheduled' as const\n}\n\nexport const enum_resolveTargetsWithEnum = {\n   id: 'id' as const,\n   objectName: 'objectName' as const\n}\n\nexport const enum_structureFormatEnum = {\n   json: 'json' as const,\n   xml: 'xml' as const\n}\n\nimport type { RichTextNode, RichTextTocNode } from './api-transaction';\nimport type { Language as B_Language } from './react-code-block';\n"], "names": [], "mappings": "AAAA,0HAA0H;AAE1H,kBAAkB,GAClB,wDAAwD,GACxD,kBAAkB,GAElB,cAAc;AACd,wBAAwB,GACxB,kBAAkB,GAClB,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAouDX,SAAS,WAGd,IAAc,EAAE,MAAiB;IACjC,OAAO;QAAE,cAAc;QAAM,GAAG,MAAM;IAAC;AACzC;AA6BO,SAAS,8BAKd,IAAc,EACd,MAAiB,EACjB,OAIC;IAED,IAAI,UAAU;QACZ,GAAG,MAAM;IACX;IAIA,IAAI,QAAQ,MAAM,GAAG,GAAG;QACtB,OAAO,CAAC,QAAQ,YAAY,CAAC,GAAG;YAC9B,GAAI,QAAQ,YAAY,GACpB;gBAAE,QAAQ,QAAQ,YAAY,CAAC,QAAQ,MAAM;YAAE,IAC/C,CAAC,CAAC;YACN,OAAO,8BAA8B,MAAM,QAAQ;gBACjD,GAAG,OAAO;gBACV,QAAQ,QAAQ,MAAM,GAAG;YAC3B;QACF;IACF;IACA,OAAO;AACT;AAKI,MAAM,wBAAkC;IAAC;CAAU;AAC5C,MAAM,YAAY,CAAC;IACxB,IAAI,CAAC,KAAK,YAAY,MAAM,IAAI,MAAM;IACtC,OAAO,sBAAsB,QAAQ,CAAC,IAAI,UAAU;AACtD;AAIA,MAAM,4BAAsC;IAAC;CAAc;AACpD,MAAM,gBAAgB,CAAC;IAC5B,IAAI,CAAC,KAAK,YAAY,MAAM,IAAI,MAAM;IACtC,OAAO,0BAA0B,QAAQ,CAAC,IAAI,UAAU;AAC1D;AAIA,MAAM,iCAA2C;IAAC;CAAmB;AAC9D,MAAM,qBAAqB,CAAC;IACjC,IAAI,CAAC,KAAK,YAAY,MAAM,IAAI,MAAM;IACtC,OAAO,+BAA+B,QAAQ,CAAC,IAAI,UAAU;AAC/D;AAIA,MAAM,2BAAqC;IAAC;CAAa;AAClD,MAAM,eAAe,CAAC;IAC3B,IAAI,CAAC,KAAK,YAAY,MAAM,IAAI,MAAM;IACtC,OAAO,yBAAyB,QAAQ,CAAC,IAAI,UAAU;AACzD;AAIA,MAAM,iCAA2C;IAAC;CAAmB;AAC9D,MAAM,qBAAqB,CAAC;IACjC,IAAI,CAAC,KAAK,YAAY,MAAM,IAAI,MAAM;IACtC,OAAO,+BAA+B,QAAQ,CAAC,IAAI,UAAU;AAC/D;AAIA,MAAM,2BAAqC;IAAC;CAAa;AAClD,MAAM,eAAe,CAAC;IAC3B,IAAI,CAAC,KAAK,YAAY,MAAM,IAAI,MAAM;IACtC,OAAO,yBAAyB,QAAQ,CAAC,IAAI,UAAU;AACzD;AAIA,MAAM,8BAAwC;IAAC;IAAU;IAAc;IAAO;IAAa;IAAiB;IAAa;IAAiB;IAAQ;IAAY;IAAc;IAAqB;IAAwB;IAAwB;CAAmB;AAC7P,MAAM,kBAAkB,CAAC;IAC9B,IAAI,CAAC,KAAK,YAAY,MAAM,IAAI,MAAM;IACtC,OAAO,4BAA4B,QAAQ,CAAC,IAAI,UAAU;AAC5D;AAIA,MAAM,iCAA2C;IAAC;CAAmB;AAC9D,MAAM,qBAAqB,CAAC;IACjC,IAAI,CAAC,KAAK,YAAY,MAAM,IAAI,MAAM;IACtC,OAAO,+BAA+B,QAAQ,CAAC,IAAI,UAAU;AAC/D;AAIA,MAAM,0BAAoC;IAAC;CAAY;AAChD,MAAM,cAAc,CAAC;IAC1B,IAAI,CAAC,KAAK,YAAY,MAAM,IAAI,MAAM;IACtC,OAAO,wBAAwB,QAAQ,CAAC,IAAI,UAAU;AACxD;AAIA,MAAM,2BAAqC;IAAC;CAAa;AAClD,MAAM,eAAe,CAAC;IAC3B,IAAI,CAAC,KAAK,YAAY,MAAM,IAAI,MAAM;IACtC,OAAO,yBAAyB,QAAQ,CAAC,IAAI,UAAU;AACzD;AAIA,MAAM,0BAAoC;IAAC;IAAU;IAAa;IAAa;IAAQ;IAAqB;IAAwB;IAAwB;CAAmB;AACxK,MAAM,cAAc,CAAC;IAC1B,IAAI,CAAC,KAAK,YAAY,MAAM,IAAI,MAAM;IACtC,OAAO,wBAAwB,QAAQ,CAAC,IAAI,UAAU;AACxD;AAIA,MAAM,6BAAuC;IAAC;CAAe;AACtD,MAAM,iBAAiB,CAAC;IAC7B,IAAI,CAAC,KAAK,YAAY,MAAM,IAAI,MAAM;IACtC,OAAO,2BAA2B,QAAQ,CAAC,IAAI,UAAU;AAC3D;AAIA,MAAM,8BAAwC;IAAC;IAAO;CAAS;AACxD,MAAM,kBAAkB,CAAC;IAC9B,IAAI,CAAC,KAAK,YAAY,MAAM,IAAI,MAAM;IACtC,OAAO,4BAA4B,QAAQ,CAAC,IAAI,UAAU;AAC5D;AAIA,MAAM,2BAAqC;IAAC;CAAa;AAClD,MAAM,eAAe,CAAC;IAC3B,IAAI,CAAC,KAAK,YAAY,MAAM,IAAI,MAAM;IACtC,OAAO,yBAAyB,QAAQ,CAAC,IAAI,UAAU;AACzD;AAIA,MAAM,qBAA+B;IAAC;CAAO;AACtC,MAAM,SAAS,CAAC;IACrB,IAAI,CAAC,KAAK,YAAY,MAAM,IAAI,MAAM;IACtC,OAAO,mBAAmB,QAAQ,CAAC,IAAI,UAAU;AACnD;AAIA,MAAM,qBAA+B;IAAC;CAAO;AACtC,MAAM,SAAS,CAAC;IACrB,IAAI,CAAC,KAAK,YAAY,MAAM,IAAI,MAAM;IACtC,OAAO,mBAAmB,QAAQ,CAAC,IAAI,UAAU;AACnD;AAIA,MAAM,6BAAuC;IAAC;CAAe;AACtD,MAAM,iBAAiB,CAAC;IAC7B,IAAI,CAAC,KAAK,YAAY,MAAM,IAAI,MAAM;IACtC,OAAO,2BAA2B,QAAQ,CAAC,IAAI,UAAU;AAC3D;AAIA,MAAM,uBAAiC;IAAC;CAAS;AAC1C,MAAM,WAAW,CAAC;IACvB,IAAI,CAAC,KAAK,YAAY,MAAM,IAAI,MAAM;IACtC,OAAO,qBAAqB,QAAQ,CAAC,IAAI,UAAU;AACrD;AAIA,MAAM,+BAAyC;IAAC;CAAiB;AAC1D,MAAM,mBAAmB,CAAC;IAC/B,IAAI,CAAC,KAAK,YAAY,MAAM,IAAI,MAAM;IACtC,OAAO,6BAA6B,QAAQ,CAAC,IAAI,UAAU;AAC7D;AAIA,MAAM,2BAAqC;IAAC;CAAa;AAClD,MAAM,eAAe,CAAC;IAC3B,IAAI,CAAC,KAAK,YAAY,MAAM,IAAI,MAAM;IACtC,OAAO,yBAAyB,QAAQ,CAAC,IAAI,UAAU;AACzD;AAIA,MAAM,+BAAyC;IAAC;CAAiB;AAC1D,MAAM,mBAAmB,CAAC;IAC/B,IAAI,CAAC,KAAK,YAAY,MAAM,IAAI,MAAM;IACtC,OAAO,6BAA6B,QAAQ,CAAC,IAAI,UAAU;AAC7D;AAIA,MAAM,mCAA6C;IAAC;CAAqB;AAClE,MAAM,uBAAuB,CAAC;IACnC,IAAI,CAAC,KAAK,YAAY,MAAM,IAAI,MAAM;IACtC,OAAO,iCAAiC,QAAQ,CAAC,IAAI,UAAU;AACjE;AAIA,MAAM,2BAAqC;IAAC;CAAa;AAClD,MAAM,eAAe,CAAC;IAC3B,IAAI,CAAC,KAAK,YAAY,MAAM,IAAI,MAAM;IACtC,OAAO,yBAAyB,QAAQ,CAAC,IAAI,UAAU;AACzD;AAIA,MAAM,+BAAyC;IAAC;CAAiB;AAC1D,MAAM,mBAAmB,CAAC;IAC/B,IAAI,CAAC,KAAK,YAAY,MAAM,IAAI,MAAM;IACtC,OAAO,6BAA6B,QAAQ,CAAC,IAAI,UAAU;AAC7D;AAIA,MAAM,yBAAmC;IAAC;CAAW;AAC9C,MAAM,aAAa,CAAC;IACzB,IAAI,CAAC,KAAK,YAAY,MAAM,IAAI,MAAM;IACtC,OAAO,uBAAuB,QAAQ,CAAC,IAAI,UAAU;AACvD;AAIA,MAAM,2BAAqC;IAAC;IAAa;IAAY;IAAa;CAAa;AACxF,MAAM,eAAe,CAAC;IAC3B,IAAI,CAAC,KAAK,YAAY,MAAM,IAAI,MAAM;IACtC,OAAO,yBAAyB,QAAQ,CAAC,IAAI,UAAU;AACzD;AAIA,MAAM,gCAA0C;IAAC;IAAa;IAAY;IAAa;CAAa;AAC7F,MAAM,oBAAoB,CAAC;IAChC,IAAI,CAAC,KAAK,YAAY,MAAM,IAAI,MAAM;IACtC,OAAO,8BAA8B,QAAQ,CAAC,IAAI,UAAU;AAC9D;AAIA,MAAM,yBAAmC;IAAC;CAAW;AAC9C,MAAM,aAAa,CAAC;IACzB,IAAI,CAAC,KAAK,YAAY,MAAM,IAAI,MAAM;IACtC,OAAO,uBAAuB,QAAQ,CAAC,IAAI,UAAU;AACvD;AAIA,MAAM,sBAAgC;IAAC;CAAQ;AACxC,MAAM,UAAU,CAAC;IACtB,IAAI,CAAC,KAAK,YAAY,MAAM,IAAI,MAAM;IACtC,OAAO,oBAAoB,QAAQ,CAAC,IAAI,UAAU;AACpD;AAIA,MAAM,0BAAoC;IAAC;CAAY;AAChD,MAAM,cAAc,CAAC;IAC1B,IAAI,CAAC,KAAK,YAAY,MAAM,IAAI,MAAM;IACtC,OAAO,wBAAwB,QAAQ,CAAC,IAAI,UAAU;AACxD;AAIA,MAAM,sBAAgC;IAAC;CAAQ;AACxC,MAAM,UAAU,CAAC;IACtB,IAAI,CAAC,KAAK,YAAY,MAAM,IAAI,MAAM;IACtC,OAAO,oBAAoB,QAAQ,CAAC,IAAI,UAAU;AACpD;AAIA,MAAM,wBAAkC;IAAC;CAAU;AAC5C,MAAM,YAAY,CAAC;IACxB,IAAI,CAAC,KAAK,YAAY,MAAM,IAAI,MAAM;IACtC,OAAO,sBAAsB,QAAQ,CAAC,IAAI,UAAU;AACtD;AAIA,MAAM,6BAAuC;IAAC;IAAmB;IAAe;CAAiB;AAC1F,MAAM,iBAAiB,CAAC;IAC7B,IAAI,CAAC,KAAK,YAAY,MAAM,IAAI,MAAM;IACtC,OAAO,2BAA2B,QAAQ,CAAC,IAAI,UAAU;AAC3D;AAIA,MAAM,kCAA4C;IAAC;CAAoB;AAChE,MAAM,sBAAsB,CAAC;IAClC,IAAI,CAAC,KAAK,YAAY,MAAM,IAAI,MAAM;IACtC,OAAO,gCAAgC,QAAQ,CAAC,IAAI,UAAU;AAChE;AAIA,MAAM,wBAAkC;IAAC;CAAU;AAC5C,MAAM,YAAY,CAAC;IACxB,IAAI,CAAC,KAAK,YAAY,MAAM,IAAI,MAAM;IACtC,OAAO,sBAAsB,QAAQ,CAAC,IAAI,UAAU;AACtD;AAIA,MAAM,4BAAsC;IAAC;CAAc;AACpD,MAAM,gBAAgB,CAAC;IAC5B,IAAI,CAAC,KAAK,YAAY,MAAM,IAAI,MAAM;IACtC,OAAO,0BAA0B,QAAQ,CAAC,IAAI,UAAU;AAC1D;AAIA,MAAM,4BAAsC;IAAC;CAAc;AACpD,MAAM,gBAAgB,CAAC;IAC5B,IAAI,CAAC,KAAK,YAAY,MAAM,IAAI,MAAM;IACtC,OAAO,0BAA0B,QAAQ,CAAC,IAAI,UAAU;AAC1D;AAIA,MAAM,0BAAoC;IAAC;CAAY;AAChD,MAAM,cAAc,CAAC;IAC1B,IAAI,CAAC,KAAK,YAAY,MAAM,IAAI,MAAM;IACtC,OAAO,wBAAwB,QAAQ,CAAC,IAAI,UAAU;AACxD;AAIA,MAAM,4BAAsC;IAAC;CAAc;AACpD,MAAM,gBAAgB,CAAC;IAC5B,IAAI,CAAC,KAAK,YAAY,MAAM,IAAI,MAAM;IACtC,OAAO,0BAA0B,QAAQ,CAAC,IAAI,UAAU;AAC1D;AAIA,MAAM,yBAAmC;IAAC;CAAW;AAC9C,MAAM,aAAa,CAAC;IACzB,IAAI,CAAC,KAAK,YAAY,MAAM,IAAI,MAAM;IACtC,OAAO,uBAAuB,QAAQ,CAAC,IAAI,UAAU;AACvD;AAIA,MAAM,gCAA0C;IAAC;CAAkB;AAC5D,MAAM,oBAAoB,CAAC;IAChC,IAAI,CAAC,KAAK,YAAY,MAAM,IAAI,MAAM;IACtC,OAAO,8BAA8B,QAAQ,CAAC,IAAI,UAAU;AAC9D;AAIA,MAAM,wBAAkC;IAAC;CAAU;AAC5C,MAAM,YAAY,CAAC;IACxB,IAAI,CAAC,KAAK,YAAY,MAAM,IAAI,MAAM;IACtC,OAAO,sBAAsB,QAAQ,CAAC,IAAI,UAAU;AACtD;AAIA,MAAM,4BAAsC;IAAC;CAAc;AACpD,MAAM,gBAAgB,CAAC;IAC5B,IAAI,CAAC,KAAK,YAAY,MAAM,IAAI,MAAM;IACtC,OAAO,0BAA0B,QAAQ,CAAC,IAAI,UAAU;AAC1D;AAIA,MAAM,mCAA6C;IAAC;CAAqB;AAClE,MAAM,uBAAuB,CAAC;IACnC,IAAI,CAAC,KAAK,YAAY,MAAM,IAAI,MAAM;IACtC,OAAO,iCAAiC,QAAQ,CAAC,IAAI,UAAU;AACjE;AAIA,MAAM,sCAAgD;IAAC;CAAwB;AACxE,MAAM,0BAA0B,CAAC;IACtC,IAAI,CAAC,KAAK,YAAY,MAAM,IAAI,MAAM;IACtC,OAAO,oCAAoC,QAAQ,CAAC,IAAI,UAAU;AACpE;AAIA,MAAM,sCAAgD;IAAC;CAAwB;AACxE,MAAM,0BAA0B,CAAC;IACtC,IAAI,CAAC,KAAK,YAAY,MAAM,IAAI,MAAM;IACtC,OAAO,oCAAoC,QAAQ,CAAC,IAAI,UAAU;AACpE;AAIA,MAAM,iCAA2C;IAAC;CAAmB;AAC9D,MAAM,qBAAqB,CAAC;IACjC,IAAI,CAAC,KAAK,YAAY,MAAM,IAAI,MAAM;IACtC,OAAO,+BAA+B,QAAQ,CAAC,IAAI,UAAU;AAC/D;AAGG,MAAM,wBAAwB;IAClC,OAAO;IACP,MAAM;AACT;AAEO,MAAM,6BAA6B;IACvC,qBAAqB;IACrB,sBAAsB;IACtB,gBAAgB;IAChB,iBAAiB;IACjB,cAAc;IACd,eAAe;IACf,0BAA0B;IAC1B,2BAA2B;IAC3B,gBAAgB;IAChB,iBAAiB;IACjB,iBAAiB;IACjB,kBAAkB;IAClB,aAAa;IACb,cAAc;IACd,WAAW;IACX,YAAY;AACf;AAEO,MAAM,gCAAgC;IAC1C,qBAAqB;IACrB,sBAAsB;IACtB,gBAAgB;IAChB,iBAAiB;IACjB,cAAc;IACd,eAAe;IACf,0BAA0B;IAC1B,2BAA2B;IAC3B,gBAAgB;IAChB,iBAAiB;IACjB,iBAAiB;IACjB,kBAAkB;AACrB;AAEO,MAAM,gCAAgC;IAC1C,qBAAqB;IACrB,sBAAsB;IACtB,gBAAgB;IAChB,iBAAiB;IACjB,cAAc;IACd,eAAe;IACf,0BAA0B;IAC1B,2BAA2B;IAC3B,gBAAgB;IAChB,iBAAiB;IACjB,iBAAiB;IACjB,kBAAkB;IAClB,WAAW;IACX,YAAY;IACZ,kBAAkB;IAClB,mBAAmB;AACtB;AAEO,MAAM,2BAA2B;IACrC,qBAAqB;IACrB,sBAAsB;IACtB,gBAAgB;IAChB,iBAAiB;IACjB,cAAc;IACd,eAAe;IACf,0BAA0B;IAC1B,2BAA2B;IAC3B,gBAAgB;IAChB,iBAAiB;IACjB,iBAAiB;IACjB,kBAAkB;IAClB,cAAc;IACd,eAAe;IACf,WAAW;IACX,YAAY;IACZ,iBAAiB;IACjB,kBAAkB;IAClB,WAAW;IACX,YAAY;IACZ,kBAAkB;IAClB,mBAAmB;IACnB,YAAY;IACZ,aAAa;AAChB;AAEO,MAAM,4BAA4B;IACtC,WAAW;IACX,WAAW;IACX,QAAQ;IACR,SAAS;IACT,WAAW;AACd;AAEO,MAAM,8BAA8B;IACxC,IAAI;IACJ,YAAY;AACf;AAEO,MAAM,2BAA2B;IACrC,MAAM;IACN,KAAK;AACR", "debugId": null}}, {"offset": {"line": 3548, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/cms/.basehub/index.ts"], "sourcesContent": ["// This file was generated by basehub. Do not edit directly. Read more: https://basehub.com/docs/api-reference/basehub-sdk\n\n/* eslint-disable */\n/* eslint-disable eslint-comments/no-restricted-disable */\n/* tslint:disable */\n\n// @ts-nocheck\nimport type {\n  QueryGenqlSelection,\n  Query,\n  MutationGenqlSelection,\n  Mutation,\n} from './schema'\nimport {\n  linkTypeMap,\n  createClient as createClientOriginal,\n  generateGraphqlOperation,\n  type FieldsSelection,\n  type GraphqlOperation,\n  type ClientOptions,\n  GenqlError,\n} from './runtime'\nexport type { FieldsSelection } from './runtime'\nexport { GenqlError }\n\nimport types from './types'\nexport {\n  fragmentOn,\n  fragmentOnRecursiveCollection,\n  type QueryGenqlSelection,\n} from './schema'\nconst typeMap = linkTypeMap(types as any)\n\nexport interface Client {\n  query<R extends QueryGenqlSelection>(\n    request: R & { __name?: string },\n  ): Promise<FieldsSelection<Query, R>>\n\n  mutation<\nR extends Omit<MutationGenqlSelection, \"transaction\" | \"transactionAsync\"> & {\n      transaction?: TransactionStatusGenqlSelection & {\n        __args: Omit<\n          NonNullable<MutationGenqlSelection[\"transaction\"]>[\"__args\"],\n          \"data\"\n        > & { data: Transaction | string };\n      };\n      transactionAsync?: {\n        __args: Omit<\n          NonNullable<MutationGenqlSelection[\"transactionAsync\"]>[\"__args\"],\n          \"data\"\n        > & { data: Transaction | string };\n      };\n    },\n>(\n    request: R & { __name?: string },\n  ): Promise<FieldsSelection<Mutation, R>>\n}\n\nconst createClient = function (options?: ClientOptions): Client {\n  const { url, headers } = getStuffFromEnv(options)\n  return createClientOriginal({\n    url: url.toString(),\n    ...options,\n    headers: { ...options?.headers, ...headers },\n    queryRoot: typeMap.Query!,\n    mutationRoot: typeMap.Mutation!,\n    subscriptionRoot: typeMap.Subscription!,\n  }) as any\n}\n\nexport const everything = {\n  __scalar: true,\n}\n\nexport type QueryResult<fields extends QueryGenqlSelection> = FieldsSelection<\n  Query,\n  fields\n>\nexport const generateQueryOp: (\n  fields: QueryGenqlSelection & { __name?: string },\n) => GraphqlOperation = function (fields) {\n  return generateGraphqlOperation('query', typeMap.Query!, fields as any)\n}\n\nexport type MutationResult<fields extends MutationGenqlSelection> =\n  FieldsSelection<Mutation, fields>\nexport const generateMutationOp: (\n  fields: MutationGenqlSelection & { __name?: string },\n) => GraphqlOperation = function (fields) {\n  return generateGraphqlOperation('mutation', typeMap.Mutation!, fields as any)\n}\n\n\nexport const getStuffFromEnv = (options) => {\n    const defaultEnvVarPrefix = \"BASEHUB\";\n\n    options = options || {};\n    if (options.token === undefined) {\n      options.token = undefined || null;\n    }\n    if (options.prefix === undefined) {\n      options.prefix = undefined || null;\n    }\n    // we'll use the draft from .env if available\n    if (!options.draft && true) {\n      options.draft = true;\n    }\n\n    const buildEnvVarName = (name) => {\n      let prefix = defaultEnvVarPrefix;\n      if (options.prefix) {\n        if (options.prefix.endsWith(\"_\")) {\n          options.prefix = options.prefix.slice(0, -1); // we remove the trailing _\n        }\n  \n        if (options.prefix.endsWith(name)) {\n          // remove the name from the prefix\n          options.prefix = options.prefix.slice(0, -name.length);\n        }\n  \n        // the user may include BASEHUB in their prefix...\n        if (options.prefix.endsWith(defaultEnvVarPrefix)) {\n          prefix = options.prefix;\n        } else {\n          // ... if they don't, we'll add it ourselves.\n          prefix = `${options.prefix}_${defaultEnvVarPrefix}`;\n        }\n      }\n      // this should result in something like <prefix>_BASEHUB_{TOKEN,REF,DRAFT} or BASEHUB_{TOKEN,REF,DRAFT}\n      return `${prefix}_${name}`;\n    };\n\n    const getEnvVar = (name: EnvVarName) => {\n      if (typeof process === 'undefined') {\n        return undefined;\n      }\n      return process?.env?.[buildEnvVarName(name)];\n    };\n\n    const parsedDebugForcedURL = getEnvVar(\"DEBUG_FORCED_URL\");\n    const parsedBackwardsCompatURL = getEnvVar(\"URL\");\n\n    const backwardsCompatURL = parsedBackwardsCompatURL\n      ? new URL(parsedBackwardsCompatURL)\n      : undefined;\n\n\n    const basehubUrl = new URL(\n      parsedDebugForcedURL\n        ? parsedDebugForcedURL\n        : `https://api.basehub.com/graphql`\n    );\n\n    // These params can either come disambiguated, or in the URL.\n    // Params that come from the URL take precedence.\n\n    const parsedBasehubTokenEnv = getEnvVar(\"TOKEN\");\n    const parsedBasehubRefEnv = getEnvVar(\"REF\");\n    const parsedBasehubDraftEnv = getEnvVar(\"DRAFT\");\n    const parsedBasehubApiVersionEnv = getEnvVar(\"API_VERSION\");\n\n    let tokenNotFoundErrorMessage = `🔴 Token not found. Make sure to include the ${buildEnvVarName(\n      \"TOKEN\"\n    )} env var.`;\n\n    const resolveTokenParam = (token) => {\n      if (!token) return null;\n      const isRaw = token.startsWith(\"bshb_\");\n      if (isRaw) return token;\n      tokenNotFoundErrorMessage = `🔴 Token not found. Make sure to include the ${token} env var.`;\n      if (typeof process === 'undefined') {\n        return undefined;\n      }\n      return process?.env?.[token] ?? ''; // empty string to prevent fallback\n    };\n\n    const resolvedToken = resolveTokenParam(options?.token ?? null);\n\n    const token =\n      resolvedToken ?? basehubUrl.searchParams.get(\"token\") ??\n      parsedBasehubTokenEnv ??\n      (backwardsCompatURL\n        ? backwardsCompatURL.searchParams.get(\"token\")\n        : undefined) ??\n      null;\n\n    if (!token) {\n      throw new Error(tokenNotFoundErrorMessage);\n    }\n\n    let draft =\n       basehubUrl.searchParams.get(\"draft\") ??\n      parsedBasehubDraftEnv ??\n      (backwardsCompatURL\n        ? backwardsCompatURL.searchParams.get(\"draft\")\n        : undefined) ??\n      false;\n\n    if (options?.draft !== undefined) {\n      draft = options.draft;\n    }\n\n    let apiVersion =\n      basehubUrl.searchParams.get(\"api-version\") ??\n      parsedBasehubApiVersionEnv ??\n      (backwardsCompatURL\n        ? backwardsCompatURL.searchParams.get(\"api-version\")\n        : undefined) ??\n      \"4\";\n\n      if (options?.apiVersion !== undefined) {\n        apiVersion = options.apiVersion;\n      }\n  \n    // 2. let's validate the URL\n\n    if (basehubUrl.pathname.split(\"/\")[1] !== \"graphql\") {\n        throw new Error(`🔴 Invalid URL. The URL needs to point your repo's GraphQL endpoint, so the pathname should end with /graphql.`);\n    }\n\n    // we'll pass these via headers\n    basehubUrl.searchParams.delete(\"token\");\n    basehubUrl.searchParams.delete(\"ref\");\n    basehubUrl.searchParams.delete(\"draft\");\n    basehubUrl.searchParams.delete(\"api-version\");\n\n    // 3.\n    const gitBranch = \"main\";\n    const gitCommitSHA = \"efa227feaf6af5cd1fe218018977f8fa57e6521a\";\n\n    const sdkBuildId = \"bshb_sdk_5b34ff3ab23a9\";\n\n    return {\n      isForcedDraft: true,\n      draft,\n      url: basehubUrl,\n      sdkBuildId,\n      headers: {\n        \"x-basehub-token\": token,\n        \"x-basehub-ref\": options?.ref ?? resolvedRef.ref,\n        \"x-basehub-sdk-build-id\": sdkBuildId,\n        ...(gitBranch ? { \"x-basehub-git-branch\": gitBranch } : {}),\n        ...(gitCommitSHA ? { \"x-basehub-git-commit-sha\": gitCommitSHA } : {}),\n        ...(gitBranchDeploymentURL ? { \"x-basehub-git-branch-deployment-url\": gitBranchDeploymentURL } : {}),\n        ...(productionDeploymentURL ? { \"x-basehub-production-deployment-url\": productionDeploymentURL } : {}),\n        ...(draft ? { \"x-basehub-draft\": \"true\" } : {}),\n        ...(apiVersion ? { \"x-basehub-api-version\": apiVersion } : {}),\n      },\n    };\n}\nimport type { Transaction } from './api-transaction';\nimport type { TransactionStatusGenqlSelection } from './schema';\n\n\nexport type * from \"basehub/api-transaction\";\nimport { createFetcher } from \"./runtime\";\n\nexport const sdkBuildId = \"bshb_sdk_5b34ff3ab23a9\";\nexport const resolvedRef = {\"repoHash\":\"57ec52db\",\"type\":\"branch\",\"ref\":\"main\",\"createSuggestedBranchLink\":null,\"id\":\"KluwvFPvKCxusUOmSQG4q\",\"name\":\"main\",\"git\":null,\"createdAt\":\"2025-06-16T00:30:26.760Z\",\"archivedAt\":null,\"archivedBy\":null,\"headCommitId\":\"qNNz4p8JMipdRXk4579YJ\",\"isDefault\":true,\"deletedAt\":null,\"workingRootBlockId\":\"a8Oul5Re6jsffvG4Ab5XZ\"};\nexport const gitBranchDeploymentURL = null;\nexport const productionDeploymentURL = null;\nexport const isNextjs = true;\n\n/**\n * Returns a hash code from an object\n * @param  {Object} obj The object to hash.\n * @return {String}    A hash string\n */\nfunction hashObject(obj: Record<string, unknown>): string {\n    const sortObjectKeys = <O extends Record<string, unknown>>(obj: O): O => {\n        if (!isObjectAsWeCommonlyCallIt(obj)) return obj;\n        return Object.keys(obj)\n            .sort()\n            .reduce((acc, key) => {\n                acc[key as keyof O] = obj[key as keyof O];\n                return acc;\n            }, {} as O);\n    };\n\n    const recursiveSortObjectKeys = <O extends Record<string, unknown>>(obj: O): O => {\n        const sortedObj = sortObjectKeys(obj);\n\n        if (!isObjectAsWeCommonlyCallIt(sortedObj)) return sortedObj;\n\n        Object.keys(sortedObj).forEach((key) => {\n            if (isObjectAsWeCommonlyCallIt(sortedObj[key as keyof O])) {\n                sortedObj[key as keyof O] = recursiveSortObjectKeys(\n                    sortedObj[key as keyof O] as O\n                ) as O[keyof O];\n            } else if (Array.isArray(sortedObj[key as keyof O])) {\n                sortedObj[key as keyof O] = (sortedObj[key as keyof O] as unknown[]).map(\n                    (item) => {\n                        if (isObjectAsWeCommonlyCallIt(item)) {\n                            return recursiveSortObjectKeys(item);\n                        } else {\n                            return item;\n                        }\n                    }\n                ) as O[keyof O];\n            }\n        });\n\n        return sortedObj;\n    };\n\n    const isObjectAsWeCommonlyCallIt = (\n        obj: unknown\n    ): obj is Record<string, unknown> => {\n        return Object.prototype.toString.call(obj) === '[object Object]';\n    };\n\n    const sortedObj = recursiveSortObjectKeys(obj);\n    const str = JSON.stringify(sortedObj);\n\n    let hash = 0;\n    for (let i = 0, len = str.length; i < len; i++) {\n        let chr = str.charCodeAt(i);\n        hash = (hash << 5) - hash + chr;\n        hash |= 0; // Convert to 32bit integer\n    }\n    return Math.abs(hash).toString();\n}\n\n// we limit options to only the ones we want to expose.\ntype Options = Omit<ClientOptions, 'url' | 'method' | 'batch' | 'credentials' | 'fetch' | 'fetcher' | 'headers' | 'integrity' | 'keepalive' | 'mode' | 'redirect' | 'referrer' | 'referrerPolicy' | 'window'> & { draft?: boolean, token?: string, ref?: string }\n\n// we include the resolvedRef.id to make sure the cache tag is unique per basehub ref\n// solves a nice problem which we'd otherwise have, being that if the dev wants to hit a different basehub branch, we don't want to respond with the same cache tag as the previous branch\nexport function cacheTagFromQuery(query: QueryGenqlSelection, apiVersion: string | undefined) {\n  const now = performance.now();\n  const result = \"basehub-\" + hashObject({ ...query, refId: resolvedRef.id, ...(apiVersion ? { apiVersion } : {}) });\n  return result;\n}\n\n/**\n * Create a basehub client.\n *\n * @param options (optional) Options for the `fetch` request; for example in Next.js, you can do `{ next: { revalidate: 60 } }` to tweak your app's cache.\n * @returns A basehub client.\n *\n * @example\n * import { basehub } from 'basehub'\n *\n * const firstQuery = await basehub().query({\n *   __typename: true,\n * });\n *\n * console.log(firstQuery.__typename) // => 'Query'\n *\n */\nexport const basehub = (options?: Options) => {\n  const { url, headers } = getStuffFromEnv(options);\n\n  if (!options) {\n    options = {};\n  }\n\n  options.getExtraFetchOptions = async (op, _body, originalRequest) => {\n    if (op !== 'query') return {}\n\n    let extra = {\n      headers: {\n        \"x-basehub-sdk-build-id\": sdkBuildId,\n      },\n    };\n\n    let isNextjsDraftMode = false;\n\n    \n      if (options.draft === undefined) {\n        // try to auto-detect (only if draft is not explicitly set by the user)\n        try {\n          const { draftMode } = await import(\"next/headers\");\n          isNextjsDraftMode = (await draftMode()).isEnabled;\n        } catch (error) {\n          // noop, not using nextjs\n        }\n      }\n\n\n\n    const isDraftResolved = true || isNextjsDraftMode || options.draft === true;\n\n    if (isDraftResolved) {\n      extra.headers = { ...extra.headers, \"x-basehub-draft\": \"true\" };\n\n      \n        // get rid of automatic nextjs caching\n        extra.next = { revalidate: undefined };\n        extra.cache = \"no-store\";\n        // try to get ref from cookies\n        try {\n          const { cookies } = await import(\"next/headers\");\n          const cookieStore = await cookies();\n          const ref = cookieStore.get(\"bshb-preview-ref-\" + resolvedRef.repoHash)?.value;\n          if (ref) {\n            extra.headers = {\n              ...extra.headers,\n              \"x-basehub-ref\": ref,\n            };\n          }\n        } catch (error) {\n          // noop \n        }\n\n    }\n\n    if (isDraftResolved) return extra;\n\n    \n      if (typeof options?.next === 'undefined') {\n        let isNextjs = false;\n        try {\n          isNextjs = !!(await import(\"next/headers\"))\n        } catch (error) {\n          // noop, not using nextjs\n        }\n        if (isNextjs) {\n          const cacheTag = cacheTagFromQuery(originalRequest, headers['x-basehub-api-version']);\n          // don't override if revalidation is already being handled by the user\n          extra.next = { tags: [cacheTag] };\n          extra.headers = {\n            ...extra.headers,\n            \"x-basehub-cache-tag\": cacheTag,\n          };\n        }\n      }\n      \n\n    return extra;\n  }\n\n  return {\n    ...createClient(options),\n    raw: createFetcher({ ...options, url, headers }) as <Cast = unknown>(\n      gql: GraphqlOperation\n    ) => Promise<Cast>,\n  };\n};\n\nbasehub.replaceSystemAliases = createClientOriginal.replaceSystemAliases;\n"], "names": [], "mappings": "AAAA,0HAA0H;AAE1H,kBAAkB,GAClB,wDAAwD,GACxD,kBAAkB,GAElB,cAAc;;;;;;;;;;;;;;AAOd;AAAA;AAAA;AAAA;AAYA;AACA;AAqOA;;;;;AAhOA,MAAM,UAAU,CAAA,GAAA,+JAAA,CAAA,cAAW,AAAD,EAAE,qIAAA,CAAA,UAAK;AA2BjC,MAAM,eAAe,SAAU,OAAuB;IACpD,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,gBAAgB;IACzC,OAAO,CAAA,GAAA,4JAAA,CAAA,eAAoB,AAAD,EAAE;QAC1B,KAAK,IAAI,QAAQ;QACjB,GAAG,OAAO;QACV,SAAS;YAAE,GAAG,SAAS,OAAO;YAAE,GAAG,OAAO;QAAC;QAC3C,WAAW,QAAQ,KAAK;QACxB,cAAc,QAAQ,QAAQ;QAC9B,kBAAkB,QAAQ,YAAY;IACxC;AACF;AAEO,MAAM,aAAa;IACxB,UAAU;AACZ;AAMO,MAAM,kBAEW,SAAU,MAAM;IACtC,OAAO,CAAA,GAAA,4KAAA,CAAA,2BAAwB,AAAD,EAAE,SAAS,QAAQ,KAAK,EAAG;AAC3D;AAIO,MAAM,qBAEW,SAAU,MAAM;IACtC,OAAO,CAAA,GAAA,4KAAA,CAAA,2BAAwB,AAAD,EAAE,YAAY,QAAQ,QAAQ,EAAG;AACjE;AAGO,MAAM,kBAAkB,CAAC;IAC5B,MAAM,sBAAsB;IAE5B,UAAU,WAAW,CAAC;IACtB,IAAI,QAAQ,KAAK,KAAK,WAAW;QAC/B,QAAQ,KAAK,GAAG,aAAa;IAC/B;IACA,IAAI,QAAQ,MAAM,KAAK,WAAW;QAChC,QAAQ,MAAM,GAAG,aAAa;IAChC;IACA,6CAA6C;IAC7C,IAAI,CAAC,QAAQ,KAAK,IAAI,MAAM;QAC1B,QAAQ,KAAK,GAAG;IAClB;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,SAAS;QACb,IAAI,QAAQ,MAAM,EAAE;YAClB,IAAI,QAAQ,MAAM,CAAC,QAAQ,CAAC,MAAM;gBAChC,QAAQ,MAAM,GAAG,QAAQ,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,2BAA2B;YAC3E;YAEA,IAAI,QAAQ,MAAM,CAAC,QAAQ,CAAC,OAAO;gBACjC,kCAAkC;gBAClC,QAAQ,MAAM,GAAG,QAAQ,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,MAAM;YACvD;YAEA,kDAAkD;YAClD,IAAI,QAAQ,MAAM,CAAC,QAAQ,CAAC,sBAAsB;gBAChD,SAAS,QAAQ,MAAM;YACzB,OAAO;gBACL,6CAA6C;gBAC7C,SAAS,GAAG,QAAQ,MAAM,CAAC,CAAC,EAAE,qBAAqB;YACrD;QACF;QACA,uGAAuG;QACvG,OAAO,GAAG,OAAO,CAAC,EAAE,MAAM;IAC5B;IAEA,MAAM,YAAY,CAAC;QACjB,IAAI,OAAO,YAAY,aAAa;YAClC,OAAO;QACT;QACA,OAAO,SAAS,KAAK,CAAC,gBAAgB,MAAM;IAC9C;IAEA,MAAM,uBAAuB,UAAU;IACvC,MAAM,2BAA2B,UAAU;IAE3C,MAAM,qBAAqB,2BACvB,IAAI,IAAI,4BACR;IAGJ,MAAM,aAAa,IAAI,IACrB,uBACI,uBACA,CAAC,+BAA+B,CAAC;IAGvC,6DAA6D;IAC7D,iDAAiD;IAEjD,MAAM,wBAAwB,UAAU;IACxC,MAAM,sBAAsB,UAAU;IACtC,MAAM,wBAAwB,UAAU;IACxC,MAAM,6BAA6B,UAAU;IAE7C,IAAI,4BAA4B,CAAC,6CAA6C,EAAE,gBAC9E,SACA,SAAS,CAAC;IAEZ,MAAM,oBAAoB,CAAC;QACzB,IAAI,CAAC,OAAO,OAAO;QACnB,MAAM,QAAQ,MAAM,UAAU,CAAC;QAC/B,IAAI,OAAO,OAAO;QAClB,4BAA4B,CAAC,6CAA6C,EAAE,MAAM,SAAS,CAAC;QAC5F,IAAI,OAAO,YAAY,aAAa;YAClC,OAAO;QACT;QACA,OAAO,SAAS,KAAK,CAAC,MAAM,IAAI,IAAI,mCAAmC;IACzE;IAEA,MAAM,gBAAgB,kBAAkB,SAAS,SAAS;IAE1D,MAAM,QACJ,iBAAiB,WAAW,YAAY,CAAC,GAAG,CAAC,YAC7C,yBACA,CAAC,qBACG,mBAAmB,YAAY,CAAC,GAAG,CAAC,WACpC,SAAS,KACb;IAEF,IAAI,CAAC,OAAO;QACV,MAAM,IAAI,MAAM;IAClB;IAEA,IAAI,QACD,WAAW,YAAY,CAAC,GAAG,CAAC,YAC7B,yBACA,CAAC,qBACG,mBAAmB,YAAY,CAAC,GAAG,CAAC,WACpC,SAAS,KACb;IAEF,IAAI,SAAS,UAAU,WAAW;QAChC,QAAQ,QAAQ,KAAK;IACvB;IAEA,IAAI,aACF,WAAW,YAAY,CAAC,GAAG,CAAC,kBAC5B,8BACA,CAAC,qBACG,mBAAmB,YAAY,CAAC,GAAG,CAAC,iBACpC,SAAS,KACb;IAEA,IAAI,SAAS,eAAe,WAAW;QACrC,aAAa,QAAQ,UAAU;IACjC;IAEF,4BAA4B;IAE5B,IAAI,WAAW,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,KAAK,WAAW;QACjD,MAAM,IAAI,MAAM,CAAC,8GAA8G,CAAC;IACpI;IAEA,+BAA+B;IAC/B,WAAW,YAAY,CAAC,MAAM,CAAC;IAC/B,WAAW,YAAY,CAAC,MAAM,CAAC;IAC/B,WAAW,YAAY,CAAC,MAAM,CAAC;IAC/B,WAAW,YAAY,CAAC,MAAM,CAAC;IAE/B,KAAK;IACL,MAAM,YAAY;IAClB,MAAM,eAAe;IAErB,MAAM,aAAa;IAEnB,OAAO;QACL,eAAe;QACf;QACA,KAAK;QACL;QACA,SAAS;YACP,mBAAmB;YACnB,iBAAiB,SAAS,OAAO,YAAY,GAAG;YAChD,0BAA0B;YAC1B,GAAI,uCAAY;gBAAE,wBAAwB;YAAU,wCAAM;YAC1D,GAAI,uCAAe;gBAAE,4BAA4B;YAAa,wCAAM;YACpE,GAAI,6EAA6F,CAAC,CAAC;YACnG,GAAI,6EAA+F,CAAC,CAAC;YACrG,GAAI,QAAQ;gBAAE,mBAAmB;YAAO,IAAI,CAAC,CAAC;YAC9C,GAAI,aAAa;gBAAE,yBAAyB;YAAW,IAAI,CAAC,CAAC;QAC/D;IACF;AACJ;;AAQO,MAAM,aAAa;AACnB,MAAM,cAAc;IAAC,YAAW;IAAW,QAAO;IAAS,OAAM;IAAO,6BAA4B;IAAK,MAAK;IAAwB,QAAO;IAAO,OAAM;IAAK,aAAY;IAA2B,cAAa;IAAK,cAAa;IAAK,gBAAe;IAAwB,aAAY;IAAK,aAAY;IAAK,sBAAqB;AAAuB;AAC/V,MAAM,yBAAyB;AAC/B,MAAM,0BAA0B;AAChC,MAAM,WAAW;AAExB;;;;CAIC,GACD,SAAS,WAAW,GAA4B;IAC5C,MAAM,iBAAiB,CAAoC;QACvD,IAAI,CAAC,2BAA2B,MAAM,OAAO;QAC7C,OAAO,OAAO,IAAI,CAAC,KACd,IAAI,GACJ,MAAM,CAAC,CAAC,KAAK;YACV,GAAG,CAAC,IAAe,GAAG,GAAG,CAAC,IAAe;YACzC,OAAO;QACX,GAAG,CAAC;IACZ;IAEA,MAAM,0BAA0B,CAAoC;QAChE,MAAM,YAAY,eAAe;QAEjC,IAAI,CAAC,2BAA2B,YAAY,OAAO;QAEnD,OAAO,IAAI,CAAC,WAAW,OAAO,CAAC,CAAC;YAC5B,IAAI,2BAA2B,SAAS,CAAC,IAAe,GAAG;gBACvD,SAAS,CAAC,IAAe,GAAG,wBACxB,SAAS,CAAC,IAAe;YAEjC,OAAO,IAAI,MAAM,OAAO,CAAC,SAAS,CAAC,IAAe,GAAG;gBACjD,SAAS,CAAC,IAAe,GAAG,AAAC,SAAS,CAAC,IAAe,CAAe,GAAG,CACpE,CAAC;oBACG,IAAI,2BAA2B,OAAO;wBAClC,OAAO,wBAAwB;oBACnC,OAAO;wBACH,OAAO;oBACX;gBACJ;YAER;QACJ;QAEA,OAAO;IACX;IAEA,MAAM,6BAA6B,CAC/B;QAEA,OAAO,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS;IACnD;IAEA,MAAM,YAAY,wBAAwB;IAC1C,MAAM,MAAM,KAAK,SAAS,CAAC;IAE3B,IAAI,OAAO;IACX,IAAK,IAAI,IAAI,GAAG,MAAM,IAAI,MAAM,EAAE,IAAI,KAAK,IAAK;QAC5C,IAAI,MAAM,IAAI,UAAU,CAAC;QACzB,OAAO,CAAC,QAAQ,CAAC,IAAI,OAAO;QAC5B,QAAQ,GAAG,2BAA2B;IAC1C;IACA,OAAO,KAAK,GAAG,CAAC,MAAM,QAAQ;AAClC;AAOO,SAAS,kBAAkB,KAA0B,EAAE,UAA8B;IAC1F,MAAM,MAAM,YAAY,GAAG;IAC3B,MAAM,SAAS,aAAa,WAAW;QAAE,GAAG,KAAK;QAAE,OAAO,YAAY,EAAE;QAAE,GAAI,aAAa;YAAE;QAAW,IAAI,CAAC,CAAC;IAAE;IAChH,OAAO;AACT;AAkBO,MAAM,UAAU,CAAC;IACtB,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,gBAAgB;IAEzC,IAAI,CAAC,SAAS;QACZ,UAAU,CAAC;IACb;IAEA,QAAQ,oBAAoB,GAAG,OAAO,IAAI,OAAO;QAC/C,IAAI,OAAO,SAAS,OAAO,CAAC;QAE5B,IAAI,QAAQ;YACV,SAAS;gBACP,0BAA0B;YAC5B;QACF;QAEA,IAAI,oBAAoB;QAGtB,IAAI,QAAQ,KAAK,KAAK,WAAW;YAC/B,uEAAuE;YACvE,IAAI;gBACF,MAAM,EAAE,SAAS,EAAE,GAAG;gBACtB,oBAAoB,CAAC,MAAM,WAAW,EAAE,SAAS;YACnD,EAAE,OAAO,OAAO;YACd,yBAAyB;YAC3B;QACF;QAIF,MAAM,kBAAkB,QAAQ,qBAAqB,QAAQ,KAAK,KAAK;QAEvE,wCAAqB;YACnB,MAAM,OAAO,GAAG;gBAAE,GAAG,MAAM,OAAO;gBAAE,mBAAmB;YAAO;YAG5D,sCAAsC;YACtC,MAAM,IAAI,GAAG;gBAAE,YAAY;YAAU;YACrC,MAAM,KAAK,GAAG;YACd,8BAA8B;YAC9B,IAAI;gBACF,MAAM,EAAE,OAAO,EAAE,GAAG;gBACpB,MAAM,cAAc,MAAM;gBAC1B,MAAM,MAAM,YAAY,GAAG,CAAC,sBAAsB,YAAY,QAAQ,GAAG;gBACzE,IAAI,KAAK;oBACP,MAAM,OAAO,GAAG;wBACd,GAAG,MAAM,OAAO;wBAChB,iBAAiB;oBACnB;gBACF;YACF,EAAE,OAAO,OAAO;YACd,QAAQ;YACV;QAEJ;QAEA,wCAAqB,OAAO;;IAuB9B;IAEA,OAAO;QACL,GAAG,aAAa,QAAQ;QACxB,KAAK,CAAA,GAAA,mJAAA,CAAA,gBAAa,AAAD,EAAE;YAAE,GAAG,OAAO;YAAE;YAAK;QAAQ;IAGhD;AACF;AAEA,QAAQ,oBAAoB,GAAG,4JAAA,CAAA,eAAoB,CAAC,oBAAoB", "debugId": null}}, {"offset": {"line": 3864, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/cms/.basehub/next-toolbar/index.js"], "sourcesContent": ["// This file was generated by basehub. Do not edit directly. Read more: https://basehub.com/docs/api-reference/basehub-sdk\n\n/* eslint-disable */\n/* eslint-disable eslint-comments/no-restricted-disable */\n/* tslint:disable */\n\nimport \"./chunk-YSQDPG26.js\";\n\n// ../../node_modules/.pnpm/basehub@8.2.7_@babel+runtim_3aebfa8e064bb601162c04844d38dd02/node_modules/basehub/src/next/toolbar/server-toolbar.tsx\nimport * as React from \"react\";\nimport { draftMode } from \"next/headers\";\nimport { revalidateTag } from \"next/cache\";\nimport {\n  getStuffFromEnv,\n  resolvedRef\n} from \"../index\";\nvar LazyClientConditionalRenderer = React.lazy(\n  () => import(\"./client-conditional-renderer-KQINRCBN.js\").then((mod) => ({\n    default: mod.ClientConditionalRenderer\n  }))\n);\nvar ServerToolbar = async ({\n  ...basehubProps\n}) => {\n  const { isForcedDraft } = getStuffFromEnv(basehubProps);\n  const enableDraftMode_unbound = async (basehubProps2, { bshbPreviewToken }) => {\n    \"use server\";\n    try {\n      const { headers, url } = getStuffFromEnv(basehubProps2);\n      const appApiEndpoint = getBaseHubAppApiEndpoint(\n        url,\n        \"/api/nextjs/preview-auth\"\n      );\n      const res = await fetch(appApiEndpoint, {\n        cache: \"no-store\",\n        method: \"POST\",\n        headers: {\n          \"content-type\": \"application/json\",\n          \"x-basehub-token\": headers[\"x-basehub-token\"]\n        },\n        body: JSON.stringify({ bshbPreview: bshbPreviewToken })\n      });\n      const responseIsJson = res.headers.get(\"content-type\")?.includes(\"json\");\n      if (!responseIsJson) {\n        return { status: 400, response: { error: \"Bad request\" } };\n      }\n      const response = await res.json();\n      if (res.status === 200)\n        (await draftMode()).enable();\n      return { status: res.status, response };\n    } catch (error) {\n      return { status: 500, response: { error: \"Something went wrong\" } };\n    }\n  };\n  const getLatestBranches_unbound = async (basehubProps2, { bshbPreviewToken }) => {\n    \"use server\";\n    try {\n      const { headers, url, isForcedDraft: isForcedDraft2 } = getStuffFromEnv(basehubProps2);\n      if ((await draftMode()).isEnabled === false && !isForcedDraft2 && !bshbPreviewToken) {\n        return { status: 403, response: { error: \"Unauthorized\" } };\n      }\n      const appApiEndpoint = getBaseHubAppApiEndpoint(\n        url,\n        \"/api/nextjs/latest-branches\"\n      );\n      const res = await fetch(appApiEndpoint, {\n        cache: \"no-store\",\n        method: \"GET\",\n        headers: {\n          \"content-type\": \"application/json\",\n          \"x-basehub-token\": headers[\"x-basehub-token\"],\n          ...bshbPreviewToken && {\n            \"x-basehub-preview-token\": bshbPreviewToken\n          },\n          ...isForcedDraft2 && {\n            \"x-basehub-forced-draft\": \"true\"\n          }\n        }\n      });\n      const responseIsJson = res.headers.get(\"content-type\")?.includes(\"json\");\n      if (!responseIsJson) {\n        return { status: 400, response: { error: \"Bad request\" } };\n      }\n      const response = await res.json();\n      return { status: res.status, response };\n    } catch (error) {\n      return { status: 500, response: { error: \"Something went wrong\" } };\n    }\n  };\n  const disableDraftMode = async () => {\n    \"use server\";\n    (await draftMode()).disable();\n  };\n  const revalidateTags_unbound = async (basehubProps2, {\n    bshbPreviewToken,\n    ref\n  }) => {\n    \"use server\";\n    const { headers, url } = getStuffFromEnv(basehubProps2);\n    const appApiEndpoint = getBaseHubAppApiEndpoint(\n      url,\n      \"/api/nextjs/pending-tags\"\n    );\n    if (!bshbPreviewToken) {\n      return { success: false, error: \"Unauthorized\" };\n    }\n    const res = await fetch(appApiEndpoint, {\n      cache: \"no-store\",\n      method: \"GET\",\n      headers: {\n        \"content-type\": \"application/json\",\n        \"x-basehub-token\": headers[\"x-basehub-token\"],\n        \"x-basehub-ref\": ref || headers[\"x-basehub-ref\"],\n        \"x-basehub-preview-token\": bshbPreviewToken,\n        \"x-basehub-sdk-build-id\": headers[\"x-basehub-sdk-build-id\"]\n      }\n    });\n    if (res.status !== 200) {\n      return {\n        success: false,\n        message: `Received status ${res.status} from server`\n      };\n    }\n    const response = await res.json();\n    try {\n      const { tags } = response;\n      if (!tags || !Array.isArray(tags) || tags.length === 0) {\n        return { success: true, message: \"No tags to revalidate\" };\n      }\n      await Promise.all(\n        tags.map(async (_tag) => {\n          const tag = _tag.startsWith(\"basehub-\") ? _tag : `basehub-${_tag}`;\n          await revalidateTag(tag);\n        })\n      );\n      return { success: true, message: `Revalidated ${tags.length} tags` };\n    } catch (error) {\n      console.log(response);\n      console.error(error);\n      return {\n        success: false,\n        message: \"Something went wrong while revalidating tags\"\n      };\n    }\n  };\n  const enableDraftMode = enableDraftMode_unbound.bind(null, basehubProps);\n  const getLatestBranches = getLatestBranches_unbound.bind(null, basehubProps);\n  const revalidateTags = revalidateTags_unbound.bind(null, basehubProps);\n  return /* @__PURE__ */ React.createElement(\n    LazyClientConditionalRenderer,\n    {\n      draft: (await draftMode()).isEnabled,\n      isForcedDraft,\n      enableDraftMode,\n      disableDraftMode,\n      revalidateTags,\n      getLatestBranches,\n      resolvedRef\n    }\n  );\n};\nfunction getBaseHubAppApiEndpoint(url, pathname) {\n  let origin;\n  switch (true) {\n    case url.origin.includes(\"api.basehub.com\"):\n      origin = \"https://basehub.com\" + pathname + url.search + url.hash;\n      break;\n    case url.origin.includes(\"api.bshb.dev\"):\n      origin = \"https://basehub.dev\" + pathname + url.search + url.hash;\n      break;\n    case url.origin.includes(\"localhost:3001\"):\n      origin = \"http://localhost:3000\" + pathname + url.search + url.hash;\n      break;\n    default:\n      origin = url.origin + pathname + url.search + url.hash;\n  }\n  return origin;\n}\nexport {\n  ServerToolbar as Toolbar\n};\n"], "names": [], "mappings": "AAAA,0HAA0H;AAE1H,kBAAkB,GAClB,wDAAwD,GACxD,kBAAkB;;;;;;;;;AAElB;AAEA,iJAAiJ;AACjJ;AACA;AACA;AACA;AAAA;;;;;;;;AAIA,IAAI,8CAAgC,CAAA,GAAA,oTAAA,CAAA,OAAU,AAAD,EAC3C,IAAM,8KAAoD,IAAI,CAAC,CAAC,MAAQ,CAAC;YACvE,SAAS,IAAI,yBAAyB;QACxC,CAAC;MAM+B,uCAA1B,wBAAiC,eAAe,EAAE,gBAAgB,EAAE;IAExE,IAAI;QACF,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,CAAA,GAAA,qJAAA,CAAA,kBAAe,AAAD,EAAE;QACzC,MAAM,iBAAiB,yBACrB,KACA;QAEF,MAAM,MAAM,MAAM,MAAM,gBAAgB;YACtC,OAAO;YACP,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,mBAAmB,OAAO,CAAC,kBAAkB;YAC/C;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE,aAAa;YAAiB;QACvD;QACA,MAAM,iBAAiB,IAAI,OAAO,CAAC,GAAG,CAAC,iBAAiB,SAAS;QACjE,IAAI,CAAC,gBAAgB;YACnB,OAAO;gBAAE,QAAQ;gBAAK,UAAU;oBAAE,OAAO;gBAAc;YAAE;QAC3D;QACA,MAAM,WAAW,MAAM,IAAI,IAAI;QAC/B,IAAI,IAAI,MAAM,KAAK,KACjB,CAAC,MAAM,CAAA,GAAA,8OAAA,CAAA,YAAS,AAAD,GAAG,EAAE,MAAM;QAC5B,OAAO;YAAE,QAAQ,IAAI,MAAM;YAAE;QAAS;IACxC,EAAE,OAAO,OAAO;QACd,OAAO;YAAE,QAAQ;YAAK,UAAU;gBAAE,OAAO;YAAuB;QAAE;IACpE;AACF;MACkC,uCAA5B,0BAAmC,eAAe,EAAE,gBAAgB,EAAE;IAE1E,IAAI;QACF,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,eAAe,cAAc,EAAE,GAAG,CAAA,GAAA,qJAAA,CAAA,kBAAe,AAAD,EAAE;QACxE,IAAI,CAAC,MAAM,CAAA,GAAA,8OAAA,CAAA,YAAS,AAAD,GAAG,EAAE,SAAS,KAAK,SAAS,CAAC,kBAAkB,CAAC,kBAAkB;YACnF,OAAO;gBAAE,QAAQ;gBAAK,UAAU;oBAAE,OAAO;gBAAe;YAAE;QAC5D;QACA,MAAM,iBAAiB,yBACrB,KACA;QAEF,MAAM,MAAM,MAAM,MAAM,gBAAgB;YACtC,OAAO;YACP,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,mBAAmB,OAAO,CAAC,kBAAkB;gBAC7C,GAAG,oBAAoB;oBACrB,2BAA2B;gBAC7B,CAAC;gBACD,GAAG,kBAAkB;oBACnB,0BAA0B;gBAC5B,CAAC;YACH;QACF;QACA,MAAM,iBAAiB,IAAI,OAAO,CAAC,GAAG,CAAC,iBAAiB,SAAS;QACjE,IAAI,CAAC,gBAAgB;YACnB,OAAO;gBAAE,QAAQ;gBAAK,UAAU;oBAAE,OAAO;gBAAc;YAAE;QAC3D;QACA,MAAM,WAAW,MAAM,IAAI,IAAI;QAC/B,OAAO;YAAE,QAAQ,IAAI,MAAM;YAAE;QAAS;IACxC,EAAE,OAAO,OAAO;QACd,OAAO;YAAE,QAAQ;YAAK,UAAU;gBAAE,OAAO;YAAuB;QAAE;IACpE;AACF;MACyB,uCAAnB;IAEJ,CAAC,MAAM,CAAA,GAAA,8OAAA,CAAA,YAAS,AAAD,GAAG,EAAE,OAAO;AAC7B;MAC+B,uCAAzB,uBAAgC,eAAe,EACnD,gBAAgB,EAChB,GAAG,EACJ;IAEC,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,CAAA,GAAA,qJAAA,CAAA,kBAAe,AAAD,EAAE;IACzC,MAAM,iBAAiB,yBACrB,KACA;IAEF,IAAI,CAAC,kBAAkB;QACrB,OAAO;YAAE,SAAS;YAAO,OAAO;QAAe;IACjD;IACA,MAAM,MAAM,MAAM,MAAM,gBAAgB;QACtC,OAAO;QACP,QAAQ;QACR,SAAS;YACP,gBAAgB;YAChB,mBAAmB,OAAO,CAAC,kBAAkB;YAC7C,iBAAiB,OAAO,OAAO,CAAC,gBAAgB;YAChD,2BAA2B;YAC3B,0BAA0B,OAAO,CAAC,yBAAyB;QAC7D;IACF;IACA,IAAI,IAAI,MAAM,KAAK,KAAK;QACtB,OAAO;YACL,SAAS;YACT,SAAS,CAAC,gBAAgB,EAAE,IAAI,MAAM,CAAC,YAAY,CAAC;QACtD;IACF;IACA,MAAM,WAAW,MAAM,IAAI,IAAI;IAC/B,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,GAAG;QACjB,IAAI,CAAC,QAAQ,CAAC,MAAM,OAAO,CAAC,SAAS,KAAK,MAAM,KAAK,GAAG;YACtD,OAAO;gBAAE,SAAS;gBAAM,SAAS;YAAwB;QAC3D;QACA,MAAM,QAAQ,GAAG,CACf,KAAK,GAAG,CAAC,OAAO;YACd,MAAM,MAAM,KAAK,UAAU,CAAC,cAAc,OAAO,CAAC,QAAQ,EAAE,MAAM;YAClE,MAAM,CAAA,GAAA,4OAAA,CAAA,gBAAa,AAAD,EAAE;QACtB;QAEF,OAAO;YAAE,SAAS;YAAM,SAAS,CAAC,YAAY,EAAE,KAAK,MAAM,CAAC,KAAK,CAAC;QAAC;IACrE,EAAE,OAAO,OAAO;QACd,QAAQ,GAAG,CAAC;QACZ,QAAQ,KAAK,CAAC;QACd,OAAO;YACL,SAAS;YACT,SAAS;QACX;IACF;AACF;AA3HF,IAAI,gBAAgB,OAAO,EACzB,GAAG,cACJ;IACC,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,qJAAA,CAAA,kBAAe,AAAD,EAAE;IAC1C,MAAM,0BAA0B,8VAAA;IA6BhC,MAAM,4BAA4B,8VAAA;IAmClC,MAAM,mBAAmB,8VAAA;IAIzB,MAAM,yBAAyB,8VAAA;IAoD/B,MAAM,kBAAkB,wBAAwB,IAAI,CAAC,MAAM;IAC3D,MAAM,oBAAoB,0BAA0B,IAAI,CAAC,MAAM;IAC/D,MAAM,iBAAiB,uBAAuB,IAAI,CAAC,MAAM;IACzD,OAAO,aAAa,iBAAG,CAAA,GAAA,oTAAA,CAAA,gBAAmB,AAAD,EACvC,+BACA;QACE,OAAO,CAAC,MAAM,CAAA,GAAA,8OAAA,CAAA,YAAS,AAAD,GAAG,EAAE,SAAS;QACpC;QACA;QACA;QACA;QACA;QACA,aAAA,qJAAA,CAAA,cAAW;IACb;AAEJ;AACA,SAAS,yBAAyB,GAAG,EAAE,QAAQ;IAC7C,IAAI;IACJ,OAAQ;QACN,KAAK,IAAI,MAAM,CAAC,QAAQ,CAAC;YACvB,SAAS,wBAAwB,WAAW,IAAI,MAAM,GAAG,IAAI,IAAI;YACjE;QACF,KAAK,IAAI,MAAM,CAAC,QAAQ,CAAC;YACvB,SAAS,wBAAwB,WAAW,IAAI,MAAM,GAAG,IAAI,IAAI;YACjE;QACF,KAAK,IAAI,MAAM,CAAC,QAAQ,CAAC;YACvB,SAAS,0BAA0B,WAAW,IAAI,MAAM,GAAG,IAAI,IAAI;YACnE;QACF;YACE,SAAS,IAAI,MAAM,GAAG,WAAW,IAAI,MAAM,GAAG,IAAI,IAAI;IAC1D;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 4077, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/cms/components/toolbar.tsx"], "sourcesContent": ["export { Toolbar } from '../.basehub/next-toolbar';\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 4095, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/analytics/google.ts"], "sourcesContent": ["export { GoogleAnalytics } from '@next/third-parties/google';\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 4113, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/analytics/keys.ts"], "sourcesContent": ["import { createEnv } from '@t3-oss/env-nextjs';\nimport { z } from 'zod';\n\nexport const keys = () =>\n  createEnv({\n    client: {\n      NEXT_PUBLIC_POSTHOG_KEY: z.string().startsWith('phc_').optional(),\n      NEXT_PUBLIC_POSTHOG_HOST: z.string().url().optional(),\n      NEXT_PUBLIC_GA_MEASUREMENT_ID: z.string().startsWith('G-').optional(),\n    },\n    runtimeEnv: {\n      NEXT_PUBLIC_POSTHOG_KEY: process.env.NEXT_PUBLIC_POSTHOG_KEY,\n      NEXT_PUBLIC_POSTHOG_HOST: process.env.NEXT_PUBLIC_POSTHOG_HOST,\n      NEXT_PUBLIC_GA_MEASUREMENT_ID: process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID,\n    },\n  });\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;;;AAEO,MAAM,OAAO,IAClB,CAAA,GAAA,0QAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ;YACN,yBAAyB,mOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,UAAU,CAAC,QAAQ,QAAQ;YAC/D,0BAA0B,mOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,QAAQ;YACnD,+BAA+B,mOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,QAAQ;QACrE;QACA,YAAY;YACV,uBAAuB;YACvB,wBAAwB;YACxB,6BAA6B;QAC/B;IACF", "debugId": null}}, {"offset": {"line": 4139, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/analytics/posthog/client.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const PostHogProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call PostHogProvider() from the server but PostHogProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/analytics/posthog/client.tsx <module evaluation>\",\n    \"PostHogProvider\",\n);\nexport const useAnalytics = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAnalytics() from the server but useAnalytics is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/analytics/posthog/client.tsx <module evaluation>\",\n    \"useAnalytics\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,kBAAkB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,uEACA;AAEG,MAAM,eAAe,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,uEACA", "debugId": null}}, {"offset": {"line": 4157, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/analytics/posthog/client.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const PostHogProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call PostHogProvider() from the server but PostHogProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/analytics/posthog/client.tsx\",\n    \"PostHogProvider\",\n);\nexport const useAnalytics = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAnalytics() from the server but useAnalytics is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/analytics/posthog/client.tsx\",\n    \"useAnalytics\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,kBAAkB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,mDACA;AAEG,MAAM,eAAe,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,mDACA", "debugId": null}}, {"offset": {"line": 4175, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 4185, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/analytics/vercel.ts"], "sourcesContent": ["export { Analytics as VercelAnalytics } from '@vercel/analytics/react';\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 4203, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/analytics/index.tsx"], "sourcesContent": ["import type { ReactNode } from 'react';\nimport { GoogleAnalytics } from './google';\nimport { keys } from './keys';\nimport { PostHogProvider } from './posthog/client';\nimport { VercelAnalytics } from './vercel';\n\ntype AnalyticsProviderProps = {\n  readonly children: ReactNode;\n};\n\nconst { NEXT_PUBLIC_GA_MEASUREMENT_ID } = keys();\n\nexport const AnalyticsProvider = ({ children }: AnalyticsProviderProps) => (\n  <PostHogProvider>\n    {children}\n    <VercelAnalytics />\n    {NEXT_PUBLIC_GA_MEASUREMENT_ID && (\n      <GoogleAnalytics gaId={NEXT_PUBLIC_GA_MEASUREMENT_ID} />\n    )}\n  </PostHogProvider>\n);\n"], "names": [], "mappings": ";;;;AACA;AAAA;AACA;AACA;AACA;AAAA;;;;;;AAMA,MAAM,EAAE,6BAA6B,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,OAAI,AAAD;AAEtC,MAAM,oBAAoB,CAAC,EAAE,QAAQ,EAA0B,iBACpE,6VAAC,2IAAA,CAAA,kBAAe;;YACb;0BACD,6VAAC,uTAAA,CAAA,kBAAe;;;;;YACf,+CACC,6VAAC,kRAAA,CAAA,kBAAe;gBAAC,MAAM", "debugId": null}}, {"offset": {"line": 4246, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/auth/provider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AuthProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/auth/provider.tsx <module evaluation>\",\n    \"AuthProvider\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,4DACA", "debugId": null}}, {"offset": {"line": 4260, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/auth/provider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AuthProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/auth/provider.tsx\",\n    \"AuthProvider\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,wCACA", "debugId": null}}, {"offset": {"line": 4274, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 4284, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/design-system/components/ui/sonner.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Toaster = registerClientReference(\n    function() { throw new Error(\"Attempted to call Toaster() from the server but To<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/ui/sonner.tsx <module evaluation>\",\n    \"Toaster\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,iFACA", "debugId": null}}, {"offset": {"line": 4298, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/design-system/components/ui/sonner.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Toaster = registerClientReference(\n    function() { throw new Error(\"Attempted to call Toaster() from the server but To<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/ui/sonner.tsx\",\n    \"Toaster\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,6DACA", "debugId": null}}, {"offset": {"line": 4312, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 4322, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/design-system/components/ui/tooltip.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Tooltip = registerClientReference(\n    function() { throw new Error(\"Attempted to call Tooltip() from the server but Tooltip is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/ui/tooltip.tsx <module evaluation>\",\n    \"Tooltip\",\n);\nexport const TooltipContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call TooltipContent() from the server but TooltipContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/ui/tooltip.tsx <module evaluation>\",\n    \"TooltipContent\",\n);\nexport const TooltipProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call TooltipProvider() from the server but TooltipProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/ui/tooltip.tsx <module evaluation>\",\n    \"TooltipProvider\",\n);\nexport const TooltipTrigger = registerClientReference(\n    function() { throw new Error(\"Attempted to call TooltipTrigger() from the server but TooltipTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/ui/tooltip.tsx <module evaluation>\",\n    \"TooltipTrigger\",\n);\n"], "names": [], "mappings": ";;;;;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,kFACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,kFACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,kFACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,kFACA", "debugId": null}}, {"offset": {"line": 4348, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/design-system/components/ui/tooltip.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Tooltip = registerClientReference(\n    function() { throw new Error(\"Attempted to call Tooltip() from the server but Tooltip is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/ui/tooltip.tsx\",\n    \"Tooltip\",\n);\nexport const TooltipContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call TooltipContent() from the server but TooltipContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/ui/tooltip.tsx\",\n    \"TooltipContent\",\n);\nexport const TooltipProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call TooltipProvider() from the server but TooltipProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/ui/tooltip.tsx\",\n    \"TooltipProvider\",\n);\nexport const TooltipTrigger = registerClientReference(\n    function() { throw new Error(\"Attempted to call TooltipTrigger() from the server but TooltipTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/ui/tooltip.tsx\",\n    \"TooltipTrigger\",\n);\n"], "names": [], "mappings": ";;;;;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,8DACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,8DACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,8DACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,8DACA", "debugId": null}}, {"offset": {"line": 4374, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 4384, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/design-system/providers/theme.tsx"], "sourcesContent": ["import type { ThemeProviderProps } from 'next-themes';\nimport { ThemeProvider as NextThemeProvider } from 'next-themes';\n\nexport const ThemeProvider = ({\n  children,\n  ...properties\n}: ThemeProviderProps) => (\n  <NextThemeProvider\n    attribute=\"class\"\n    defaultTheme=\"dark\"\n    forcedTheme=\"dark\"\n    enableSystem={false}\n    disableTransitionOnChange\n    {...properties}\n  >\n    {children}\n  </NextThemeProvider>\n);\n"], "names": [], "mappings": ";;;;AACA;;;AAEO,MAAM,gBAAgB,CAAC,EAC5B,QAAQ,EACR,GAAG,YACgB,iBACnB,6VAAC,yPAAA,CAAA,gBAAiB;QAChB,WAAU;QACV,cAAa;QACb,aAAY;QACZ,cAAc;QACd,yBAAyB;QACxB,GAAG,UAAU;kBAEb", "debugId": null}}, {"offset": {"line": 4410, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/design-system/index.tsx"], "sourcesContent": ["import { AnalyticsProvider } from '@repo/analytics';\nimport { AuthProvider } from '@repo/auth/provider';\nimport type { ThemeProviderProps } from 'next-themes';\nimport { Toaster } from './components/ui/sonner';\nimport { TooltipProvider } from './components/ui/tooltip';\nimport { ThemeProvider } from './providers/theme';\n\ntype DesignSystemProviderProperties = ThemeProviderProps & {\n  privacyUrl?: string;\n  termsUrl?: string;\n  helpUrl?: string;\n};\n\nexport const DesignSystemProvider = ({\n  children,\n  privacyUrl,\n  termsUrl,\n  helpUrl,\n  ...properties\n}: DesignSystemProviderProperties) => (\n  <ThemeProvider {...properties}>\n    <AuthProvider privacyUrl={privacyUrl} termsUrl={termsUrl} helpUrl={helpUrl}>\n      <AnalyticsProvider>\n        <TooltipProvider>{children}</TooltipProvider>\n        <Toaster />\n      </AnalyticsProvider>\n    </AuthProvider>\n  </ThemeProvider>\n);\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAEA;AACA;AACA;;;;;;;AAQO,MAAM,uBAAuB,CAAC,EACnC,QAAQ,EACR,UAAU,EACV,QAAQ,EACR,OAAO,EACP,GAAG,YAC4B,iBAC/B,6VAAC,mJAAA,CAAA,gBAAa;QAAE,GAAG,UAAU;kBAC3B,cAAA,6VAAC,6HAAA,CAAA,eAAY;YAAC,YAAY;YAAY,UAAU;YAAU,SAAS;sBACjE,cAAA,6VAAC,+HAAA,CAAA,oBAAiB;;kCAChB,6VAAC,4JAAA,CAAA,kBAAe;kCAAE;;;;;;kCAClB,6VAAC,2JAAA,CAAA,UAAO", "debugId": null}}, {"offset": {"line": 4699, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/observability/log.ts"], "sourcesContent": ["import { log as logtail } from '@logtail/next';\n\nexport const log = process.env.NODE_ENV === 'production' ? logtail : console;\n"], "names": [], "mappings": ";;;AAAA;;AAEO,MAAM,MAAM,6EAAkD", "debugId": null}}, {"offset": {"line": 4711, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/observability/error.ts"], "sourcesContent": ["import { captureException } from '@sentry/nextjs';\nimport { log } from './log';\n\nexport const parseError = (error: unknown): string => {\n  let message = 'An error occurred';\n\n  if (error instanceof Error) {\n    message = error.message;\n  } else if (error && typeof error === 'object' && 'message' in error) {\n    message = error.message as string;\n  } else {\n    message = String(error);\n  }\n\n  try {\n    captureException(error);\n    log.error(`Parsing error: ${message}`);\n  } catch (newError) {\n    // biome-ignore lint/suspicious/noConsole: Need console here\n    console.error('Error parsing error:', newError);\n  }\n\n  return message;\n};\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,MAAM,aAAa,CAAC;IACzB,IAAI,UAAU;IAEd,IAAI,iBAAiB,OAAO;QAC1B,UAAU,MAAM,OAAO;IACzB,OAAO,IAAI,SAAS,OAAO,UAAU,YAAY,aAAa,OAAO;QACnE,UAAU,MAAM,OAAO;IACzB,OAAO;QACL,UAAU,OAAO;IACnB;IAEA,IAAI;QACF,CAAA,GAAA,+QAAA,CAAA,mBAAgB,AAAD,EAAE;QACjB,gIAAA,CAAA,MAAG,CAAC,KAAK,CAAC,CAAC,eAAe,EAAE,SAAS;IACvC,EAAE,OAAO,UAAU;QACjB,4DAA4D;QAC5D,QAAQ,KAAK,CAAC,wBAAwB;IACxC;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 4742, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/design-system/lib/utils.ts"], "sourcesContent": ["import { parseError } from '@repo/observability/error';\nimport { clsx } from 'clsx';\nimport type { ClassValue } from 'clsx';\nimport { toast } from 'sonner';\nimport { twMerge } from 'tailwind-merge';\n\nexport const cn = (...inputs: ClassValue[]): string => twMerge(clsx(inputs));\n\nexport const capitalize = (str: string) =>\n  str.charAt(0).toUpperCase() + str.slice(1);\n\nexport const handleError = (error: unknown): void => {\n  const message = parseError(error);\n\n  toast.error(message);\n};\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAEA;AACA;;;;;AAEO,MAAM,KAAK,CAAC,GAAG,SAAiC,CAAA,GAAA,yNAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,sLAAA,CAAA,OAAI,AAAD,EAAE;AAE7D,MAAM,aAAa,CAAC,MACzB,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC;AAEnC,MAAM,cAAc,CAAC;IAC1B,MAAM,UAAU,CAAA,GAAA,kIAAA,CAAA,aAAU,AAAD,EAAE;IAE3B,wQAAA,CAAA,QAAK,CAAC,KAAK,CAAC;AACd", "debugId": null}}, {"offset": {"line": 4767, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/design-system/lib/fonts.ts"], "sourcesContent": ["import { cn } from '@repo/design-system/lib/utils';\nimport { GeistMono } from 'geist/font/mono';\nimport { GeistSans } from 'geist/font/sans';\n\nexport const fonts = cn(\n  GeistSans.variable,\n  GeistMono.variable,\n  'touch-manipulation font-sans antialiased'\n);\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;AACA;AAAA;;;;AAEO,MAAM,QAAQ,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EACpB,0SAAA,CAAA,YAAS,CAAC,QAAQ,EAClB,0SAAA,CAAA,YAAS,CAAC,QAAQ,EAClB", "debugId": null}}, {"offset": {"line": 4785, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/feature-flags/keys.ts"], "sourcesContent": ["import { createEnv } from '@t3-oss/env-nextjs';\nimport { z } from 'zod';\n\nexport const keys = () =>\n  createEnv({\n    server: {\n      FLAGS_SECRET: z.string().optional(),\n    },\n    runtimeEnv: {\n      FLAGS_SECRET: process.env.FLAGS_SECRET,\n    },\n  });\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;;;AAEO,MAAM,OAAO,IAClB,CAAA,GAAA,0QAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ;YACN,cAAc,mOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACnC;QACA,YAAY;YACV,cAAc,QAAQ,GAAG,CAAC,YAAY;QACxC;IACF", "debugId": null}}, {"offset": {"line": 4807, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/feature-flags/components/toolbar.tsx"], "sourcesContent": ["import { VercelToolbar } from '@vercel/toolbar/next';\nimport { keys } from '../keys';\n\nexport const Toolbar = () => (keys().FLAGS_SECRET ? <VercelToolbar /> : null);\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEO,MAAM,UAAU,IAAO,CAAA,GAAA,oIAAA,CAAA,OAAI,AAAD,IAAI,YAAY,iBAAG,6VAAC,sQAAA,CAAA,gBAAa;;;;eAAM", "debugId": null}}, {"offset": {"line": 4832, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/internationalization/index.ts"], "sourcesContent": ["import 'server-only';\nimport type en from './dictionaries/en.json';\nimport languine from './languine.json';\n\nexport const locales = [\n  languine.locale.source,\n  ...languine.locale.targets,\n] as const;\n\nexport type Dictionary = typeof en;\n\nconst dictionaries: Record<string, () => Promise<Dictionary>> =\n  Object.fromEntries(\n    locales.map((locale) => [\n      locale,\n      () =>\n        import(`./dictionaries/${locale}.json`)\n          .then((mod) => mod.default)\n          .catch((err) => {\n            console.error(\n              `Failed to load dictionary for locale: ${locale}`,\n              err\n            );\n            return import('./dictionaries/en.json').then((mod) => mod.default);\n          }),\n    ])\n  );\n\nexport const getDictionary = async (locale: string): Promise<Dictionary> => {\n  const normalizedLocale = locale.split('-')[0];\n\n  if (!locales.includes(normalizedLocale as any)) {\n    console.warn(`Locale \"${locale}\" is not supported, defaulting to \"en\"`);\n    return dictionaries['en']();\n  }\n\n  try {\n    return await dictionaries[normalizedLocale]();\n  } catch (error) {\n    console.error(\n      `Error loading dictionary for locale \"${normalizedLocale}\", falling back to \"en\"`,\n      error\n    );\n    return dictionaries['en']();\n  }\n};\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;AAEO,MAAM,UAAU;IACrB,oHAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,MAAM;OACnB,oHAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,OAAO;CAC3B;AAID,MAAM,eACJ,OAAO,WAAW,CAChB,QAAQ,GAAG,CAAC,CAAC,SAAW;QACtB;QACA,IACE,sIACG,IAAI,CAAC,CAAC,MAAQ,IAAI,OAAO,EACzB,KAAK,CAAC,CAAC;gBACN,QAAQ,KAAK,CACX,CAAC,sCAAsC,EAAE,QAAQ,EACjD;gBAEF,OAAO,sIAAiC,IAAI,CAAC,CAAC,MAAQ,IAAI,OAAO;YACnE;KACL;AAGE,MAAM,gBAAgB,OAAO;IAClC,MAAM,mBAAmB,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE;IAE7C,IAAI,CAAC,QAAQ,QAAQ,CAAC,mBAA0B;QAC9C,QAAQ,IAAI,CAAC,CAAC,QAAQ,EAAE,OAAO,sCAAsC,CAAC;QACtE,OAAO,YAAY,CAAC,KAAK;IAC3B;IAEA,IAAI;QACF,OAAO,MAAM,YAAY,CAAC,iBAAiB;IAC7C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CACX,CAAC,qCAAqC,EAAE,iBAAiB,uBAAuB,CAAC,EACjF;QAEF,OAAO,YAAY,CAAC,KAAK;IAC3B;AACF", "debugId": null}}, {"offset": {"line": 4870, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/cms/keys.ts"], "sourcesContent": ["import { createEnv } from '@t3-oss/env-nextjs';\nimport { z } from 'zod';\n\nexport const keys = () =>\n  createEnv({\n    server: {\n      BASEHUB_TOKEN: z.string().startsWith('bshb_pk_'),\n    },\n    runtimeEnv: {\n      BASEHUB_TOKEN: process.env.BASEHUB_TOKEN,\n    },\n  });\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;;;AAEO,MAAM,OAAO,IAClB,CAAA,GAAA,0QAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ;YACN,eAAe,mOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,UAAU,CAAC;QACvC;QACA,YAAY;YACV,eAAe,QAAQ,GAAG,CAAC,aAAa;QAC1C;IACF", "debugId": null}}, {"offset": {"line": 4892, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/email/keys.ts"], "sourcesContent": ["import { createEnv } from '@t3-oss/env-nextjs';\nimport { z } from 'zod';\n\nexport const keys = () =>\n  createEnv({\n    server: {\n      RESEND_FROM: z.string().email(),\n      RESEND_TOKEN: z.string().startsWith('re_'),\n    },\n    runtimeEnv: {\n      RESEND_FROM: process.env.RESEND_FROM,\n      RESEND_TOKEN: process.env.RESEND_TOKEN,\n    },\n  });\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;;;AAEO,MAAM,OAAO,IAClB,CAAA,GAAA,0QAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ;YACN,aAAa,mOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK;YAC7B,cAAc,mOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,UAAU,CAAC;QACtC;QACA,YAAY;YACV,aAAa,QAAQ,GAAG,CAAC,WAAW;YACpC,cAAc,QAAQ,GAAG,CAAC,YAAY;QACxC;IACF", "debugId": null}}, {"offset": {"line": 4916, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/next-config/keys.ts"], "sourcesContent": ["import { vercel } from '@t3-oss/env-core/presets-zod';\nimport { createEnv } from '@t3-oss/env-nextjs';\nimport { z } from 'zod';\n\nexport const keys = () =>\n  createEnv({\n    extends: [vercel()],\n    server: {\n      ANALYZE: z.string().optional(),\n\n      // Added by Vercel\n      NEXT_RUNTIME: z.enum(['nodejs', 'edge']).optional(),\n    },\n    client: {\n      NEXT_PUBLIC_APP_URL: z.string().url(),\n      NEXT_PUBLIC_WEB_URL: z.string().url(),\n      NEXT_PUBLIC_API_URL: z.string().url().optional(),\n      NEXT_PUBLIC_DOCS_URL: z.string().url().optional(),\n    },\n    runtimeEnv: {\n      ANALYZE: process.env.ANALYZE,\n      NEXT_RUNTIME: process.env.NEXT_RUNTIME,\n      NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL,\n      NEXT_PUBLIC_WEB_URL: process.env.NEXT_PUBLIC_WEB_URL,\n      NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL,\n      NEXT_PUBLIC_DOCS_URL: process.env.NEXT_PUBLIC_DOCS_URL,\n    },\n  });\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AAAA;;;;AAEO,MAAM,OAAO,IAClB,CAAA,GAAA,0QAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS;YAAC,CAAA,GAAA,iRAAA,CAAA,SAAM,AAAD;SAAI;QACnB,QAAQ;YACN,SAAS,mOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;YAE5B,kBAAkB;YAClB,cAAc,mOAAA,CAAA,IAAC,CAAC,IAAI,CAAC;gBAAC;gBAAU;aAAO,EAAE,QAAQ;QACnD;QACA,QAAQ;YACN,qBAAqB,mOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG;YACnC,qBAAqB,mOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG;YACnC,qBAAqB,mOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,QAAQ;YAC9C,sBAAsB,mOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,QAAQ;QACjD;QACA,YAAY;YACV,SAAS,QAAQ,GAAG,CAAC,OAAO;YAC5B,YAAY;YACZ,mBAAmB;YACnB,mBAAmB;YACnB,mBAAmB;YACnB,oBAAoB;QACtB;IACF", "debugId": null}}, {"offset": {"line": 4959, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/observability/keys.ts"], "sourcesContent": ["import { createEnv } from '@t3-oss/env-nextjs';\nimport { z } from 'zod';\n\nexport const keys = () =>\n  createEnv({\n    server: {\n      BETTERSTACK_API_KEY: z.string().optional(),\n      BETTERSTACK_URL: z.string().optional(),\n\n      // Added by Sentry Integration, Vercel Marketplace\n      SENTRY_ORG: z.string().optional(),\n      SENTRY_PROJECT: z.string().optional(),\n    },\n    client: {\n      // Added by Sentry Integration, Vercel Marketplace\n      NEXT_PUBLIC_SENTRY_DSN: z.string().url().optional(),\n    },\n    runtimeEnv: {\n      BETTERSTACK_API_KEY: process.env.BETTERSTACK_API_KEY,\n      BETTERSTACK_URL: process.env.BETTERSTACK_URL,\n      SENTRY_ORG: process.env.SENTRY_ORG,\n      SENTRY_PROJECT: process.env.SENTRY_PROJECT,\n      NEXT_PUBLIC_SENTRY_DSN: process.env.NEXT_PUBLIC_SENTRY_DSN,\n    },\n  });\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;;;AAEO,MAAM,OAAO,IAClB,CAAA,GAAA,0QAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ;YACN,qBAAqB,mOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;YACxC,iBAAiB,mOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;YAEpC,kDAAkD;YAClD,YAAY,mOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;YAC/B,gBAAgB,mOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACrC;QACA,QAAQ;YACN,kDAAkD;YAClD,wBAAwB,mOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,QAAQ;QACnD;QACA,YAAY;YACV,qBAAqB,QAAQ,GAAG,CAAC,mBAAmB;YACpD,iBAAiB,QAAQ,GAAG,CAAC,eAAe;YAC5C,YAAY,QAAQ,GAAG,CAAC,UAAU;YAClC,gBAAgB,QAAQ,GAAG,CAAC,cAAc;YAC1C,wBAAwB,QAAQ,GAAG,CAAC,sBAAsB;QAC5D;IACF", "debugId": null}}, {"offset": {"line": 4993, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/rate-limit/keys.ts"], "sourcesContent": ["import { createEnv } from '@t3-oss/env-nextjs';\nimport { z } from 'zod';\n\nexport const keys = () =>\n  createEnv({\n    server: {\n      UPSTASH_REDIS_REST_URL: z.string().url().optional(),\n      UPSTASH_REDIS_REST_TOKEN: z.string().optional(),\n    },\n    runtimeEnv: {\n      UPSTASH_REDIS_REST_URL: process.env.UPSTASH_REDIS_REST_URL,\n      UPSTASH_REDIS_REST_TOKEN: process.env.UPSTASH_REDIS_REST_TOKEN,\n    },\n  });\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;;;AAEO,MAAM,OAAO,IAClB,CAAA,GAAA,0QAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ;YACN,wBAAwB,mOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,QAAQ;YACjD,0BAA0B,mOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC/C;QACA,YAAY;YACV,wBAAwB,QAAQ,GAAG,CAAC,sBAAsB;YAC1D,0BAA0B,QAAQ,GAAG,CAAC,wBAAwB;QAChE;IACF", "debugId": null}}, {"offset": {"line": 5017, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/security/keys.ts"], "sourcesContent": ["import { createEnv } from '@t3-oss/env-nextjs';\nimport { z } from 'zod';\n\nexport const keys = () =>\n  createEnv({\n    server: {\n      ARCJET_KEY: z.string().startsWith('ajkey_').optional(),\n    },\n    runtimeEnv: {\n      ARCJET_KEY: process.env.ARCJET_KEY,\n    },\n  });\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;;;AAEO,MAAM,OAAO,IAClB,CAAA,GAAA,0QAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ;YACN,YAAY,mOAAA,CAAA,IAAC,CAAC,MAAM,GAAG,UAAU,CAAC,UAAU,QAAQ;QACtD;QACA,YAAY;YACV,YAAY,QAAQ,GAAG,CAAC,UAAU;QACpC;IACF", "debugId": null}}, {"offset": {"line": 5039, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/env.ts"], "sourcesContent": ["import { keys as cms } from '@repo/cms/keys';\nimport { keys as email } from '@repo/email/keys';\nimport { keys as flags } from '@repo/feature-flags/keys';\nimport { keys as core } from '@repo/next-config/keys';\nimport { keys as observability } from '@repo/observability/keys';\nimport { keys as rateLimit } from '@repo/rate-limit/keys';\nimport { keys as security } from '@repo/security/keys';\nimport { createEnv } from '@t3-oss/env-nextjs';\n\nexport const env = createEnv({\n  extends: [\n    cms(),\n    core(),\n    email(),\n    observability(),\n    flags(),\n    security(),\n    rateLimit(),\n  ],\n  server: {},\n  client: {},\n  runtimeEnv: {},\n});\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AAEO,MAAM,MAAM,CAAA,GAAA,0QAAA,CAAA,YAAS,AAAD,EAAE;IAC3B,SAAS;QACP,CAAA,GAAA,uHAAA,CAAA,OAAG,AAAD;QACF,CAAA,GAAA,kIAAA,CAAA,OAAI,AAAD;QACH,CAAA,GAAA,yHAAA,CAAA,OAAK,AAAD;QACJ,CAAA,GAAA,iIAAA,CAAA,OAAa,AAAD;QACZ,CAAA,GAAA,oIAAA,CAAA,OAAK,AAAD;QACJ,CAAA,GAAA,4HAAA,CAAA,OAAQ,AAAD;QACP,CAAA,GAAA,iIAAA,CAAA,OAAS,AAAD;KACT;IACD,QAAQ,CAAC;IACT,QAAQ,CAAC;IACT,YAAY,CAAC;AACf", "debugId": null}}, {"offset": {"line": 5078, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/cms/index.ts"], "sourcesContent": ["import { basehub as basehubClient, fragmentOn } from './.basehub';\r\nimport { keys } from './keys';\r\n\r\nconst basehub = basehubClient({\r\n  token: keys().BASEHUB_TOKEN,\r\n});\r\n\r\n/* -------------------------------------------------------------------------------------------------\r\n * Common Fragments\r\n * -----------------------------------------------------------------------------------------------*/\r\n\r\nconst imageFragment = fragmentOn('BlockImage', {\r\n  url: true,\r\n  width: true,\r\n  height: true,\r\n  alt: true,\r\n  blurDataURL: true,\r\n});\r\n\r\n/* -------------------------------------------------------------------------------------------------\r\n * Blog Fragments & Queries\r\n * -----------------------------------------------------------------------------------------------*/\r\n\r\nconst postMetaFragment = fragmentOn('PostsItem', {\r\n  _slug: true,\r\n  _title: true,\r\n  authors: {\r\n    _title: true,\r\n    avatar: imageFragment,\r\n    xUrl: true,\r\n  },\r\n  categories: {\r\n    _title: true,\r\n  },\r\n  date: true,\r\n  description: true,\r\n  image: imageFragment,\r\n});\r\n\r\nconst postFragment = fragmentOn('PostsItem', {\r\n  ...postMetaFragment,\r\n  body: {\r\n    plainText: true,\r\n    json: {\r\n      content: true,\r\n      toc: true,\r\n    },\r\n    readingTime: true,\r\n  },\r\n});\r\n\r\nexport type PostMeta = fragmentOn.infer<typeof postMetaFragment>;\r\nexport type Post = fragmentOn.infer<typeof postFragment>;\r\n\r\nexport const blog = {\r\n  postsQuery: fragmentOn('Query', {\r\n    blog: {\r\n      posts: {\r\n        items: postMetaFragment,\r\n      },\r\n    },\r\n  }),\r\n\r\n  latestPostQuery: fragmentOn('Query', {\r\n    blog: {\r\n      posts: {\r\n        __args: {\r\n          orderBy: '_sys_createdAt__DESC',\r\n        },\r\n        item: postFragment,\r\n      },\r\n    },\r\n  }),\r\n\r\n  postQuery: (slug: string) => ({\r\n    blog: {\r\n      posts: {\r\n        __args: {\r\n          filter: {\r\n            _sys_slug: { eq: slug },\r\n          },\r\n        },\r\n        item: postFragment,\r\n      },\r\n    },\r\n  }),\r\n\r\n  getPosts: async (): Promise<PostMeta[]> => {\r\n    const data = await basehub.query(blog.postsQuery);\r\n\r\n    return data.blog.posts.items;\r\n  },\r\n\r\n  getLatestPost: async () => {\r\n    const data = await basehub.query(blog.latestPostQuery);\r\n\r\n    return data.blog.posts.item;\r\n  },\r\n\r\n  getPost: async (slug: string) => {\r\n    const query = blog.postQuery(slug);\r\n    const data = await basehub.query(query);\r\n\r\n    return data.blog.posts.item;\r\n  },\r\n};\r\n\r\n/* -------------------------------------------------------------------------------------------------\r\n * Legal Fragments & Queries\r\n * -----------------------------------------------------------------------------------------------*/\r\n\r\nconst legalPostMetaFragment = fragmentOn('LegalPagesItem', {\r\n  _slug: true,\r\n  _title: true,\r\n  description: true,\r\n});\r\n\r\nconst legalPostFragment = fragmentOn('LegalPagesItem', {\r\n  ...legalPostMetaFragment,\r\n  body: {\r\n    plainText: true,\r\n    json: {\r\n      content: true,\r\n      toc: true,\r\n    },\r\n    readingTime: true,\r\n  },\r\n});\r\n\r\nexport type LegalPostMeta = fragmentOn.infer<typeof legalPostMetaFragment>;\r\nexport type LegalPost = fragmentOn.infer<typeof legalPostFragment>;\r\n\r\nexport const legal = {\r\n  postsQuery: fragmentOn('Query', {\r\n    legalPages: {\r\n      items: legalPostFragment,\r\n    },\r\n  }),\r\n\r\n  latestPostQuery: fragmentOn('Query', {\r\n    legalPages: {\r\n      __args: {\r\n        orderBy: '_sys_createdAt__DESC',\r\n      },\r\n      item: legalPostFragment,\r\n    },\r\n  }),\r\n\r\n  postQuery: (slug: string) =>\r\n    fragmentOn('Query', {\r\n      legalPages: {\r\n        __args: {\r\n          filter: {\r\n            _sys_slug: { eq: slug },\r\n          },\r\n        },\r\n        item: legalPostFragment,\r\n      },\r\n    }),\r\n\r\n  getPosts: async (): Promise<LegalPost[]> => {\r\n    const data = await basehub.query(legal.postsQuery);\r\n\r\n    return data.legalPages.items;\r\n  },\r\n\r\n  getLatestPost: async () => {\r\n    const data = await basehub.query(legal.latestPostQuery);\r\n\r\n    return data.legalPages.item;\r\n  },\r\n\r\n  getPost: async (slug: string) => {\r\n    const query = legal.postQuery(slug);\r\n    const data = await basehub.query(query);\r\n\r\n    return data.legalPages.item;\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AACA;;;AAEA,MAAM,UAAU,CAAA,GAAA,qJAAA,CAAA,UAAa,AAAD,EAAE;IAC5B,OAAO,CAAA,GAAA,uHAAA,CAAA,OAAI,AAAD,IAAI,aAAa;AAC7B;AAEA;;kGAEkG,GAElG,MAAM,gBAAgB,CAAA,GAAA,sIAAA,CAAA,aAAU,AAAD,EAAE,cAAc;IAC7C,KAAK;IACL,OAAO;IACP,QAAQ;IACR,KAAK;IACL,aAAa;AACf;AAEA;;kGAEkG,GAElG,MAAM,mBAAmB,CAAA,GAAA,sIAAA,CAAA,aAAU,AAAD,EAAE,aAAa;IAC/C,OAAO;IACP,QAAQ;IACR,SAAS;QACP,QAAQ;QACR,QAAQ;QACR,MAAM;IACR;IACA,YAAY;QACV,QAAQ;IACV;IACA,MAAM;IACN,aAAa;IACb,OAAO;AACT;AAEA,MAAM,eAAe,CAAA,GAAA,sIAAA,CAAA,aAAU,AAAD,EAAE,aAAa;IAC3C,GAAG,gBAAgB;IACnB,MAAM;QACJ,WAAW;QACX,MAAM;YACJ,SAAS;YACT,KAAK;QACP;QACA,aAAa;IACf;AACF;AAKO,MAAM,OAAO;IAClB,YAAY,CAAA,GAAA,sIAAA,CAAA,aAAU,AAAD,EAAE,SAAS;QAC9B,MAAM;YACJ,OAAO;gBACL,OAAO;YACT;QACF;IACF;IAEA,iBAAiB,CAAA,GAAA,sIAAA,CAAA,aAAU,AAAD,EAAE,SAAS;QACnC,MAAM;YACJ,OAAO;gBACL,QAAQ;oBACN,SAAS;gBACX;gBACA,MAAM;YACR;QACF;IACF;IAEA,WAAW,CAAC,OAAiB,CAAC;YAC5B,MAAM;gBACJ,OAAO;oBACL,QAAQ;wBACN,QAAQ;4BACN,WAAW;gCAAE,IAAI;4BAAK;wBACxB;oBACF;oBACA,MAAM;gBACR;YACF;QACF,CAAC;IAED,UAAU;QACR,MAAM,OAAO,MAAM,QAAQ,KAAK,CAAC,KAAK,UAAU;QAEhD,OAAO,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK;IAC9B;IAEA,eAAe;QACb,MAAM,OAAO,MAAM,QAAQ,KAAK,CAAC,KAAK,eAAe;QAErD,OAAO,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI;IAC7B;IAEA,SAAS,OAAO;QACd,MAAM,QAAQ,KAAK,SAAS,CAAC;QAC7B,MAAM,OAAO,MAAM,QAAQ,KAAK,CAAC;QAEjC,OAAO,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI;IAC7B;AACF;AAEA;;kGAEkG,GAElG,MAAM,wBAAwB,CAAA,GAAA,sIAAA,CAAA,aAAU,AAAD,EAAE,kBAAkB;IACzD,OAAO;IACP,QAAQ;IACR,aAAa;AACf;AAEA,MAAM,oBAAoB,CAAA,GAAA,sIAAA,CAAA,aAAU,AAAD,EAAE,kBAAkB;IACrD,GAAG,qBAAqB;IACxB,MAAM;QACJ,WAAW;QACX,MAAM;YACJ,SAAS;YACT,KAAK;QACP;QACA,aAAa;IACf;AACF;AAKO,MAAM,QAAQ;IACnB,YAAY,CAAA,GAAA,sIAAA,CAAA,aAAU,AAAD,EAAE,SAAS;QAC9B,YAAY;YACV,OAAO;QACT;IACF;IAEA,iBAAiB,CAAA,GAAA,sIAAA,CAAA,aAAU,AAAD,EAAE,SAAS;QACnC,YAAY;YACV,QAAQ;gBACN,SAAS;YACX;YACA,MAAM;QACR;IACF;IAEA,WAAW,CAAC,OACV,CAAA,GAAA,sIAAA,CAAA,aAAU,AAAD,EAAE,SAAS;YAClB,YAAY;gBACV,QAAQ;oBACN,QAAQ;wBACN,WAAW;4BAAE,IAAI;wBAAK;oBACxB;gBACF;gBACA,MAAM;YACR;QACF;IAEF,UAAU;QACR,MAAM,OAAO,MAAM,QAAQ,KAAK,CAAC,MAAM,UAAU;QAEjD,OAAO,KAAK,UAAU,CAAC,KAAK;IAC9B;IAEA,eAAe;QACb,MAAM,OAAO,MAAM,QAAQ,KAAK,CAAC,MAAM,eAAe;QAEtD,OAAO,KAAK,UAAU,CAAC,IAAI;IAC7B;IAEA,SAAS,OAAO;QACd,MAAM,QAAQ,MAAM,SAAS,CAAC;QAC9B,MAAM,OAAO,MAAM,QAAQ,KAAK,CAAC;QAEjC,OAAO,KAAK,UAAU,CAAC,IAAI;IAC7B;AACF", "debugId": null}}, {"offset": {"line": 5238, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/cms/.basehub/react-pump/chunk-F5PHAOMO.js"], "sourcesContent": ["// This file was generated by basehub. Do not edit directly. Read more: https://basehub.com/docs/api-reference/basehub-sdk\n\n/* eslint-disable */\n/* eslint-disable eslint-comments/no-restricted-disable */\n/* tslint:disable */\n\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __commonJS = (cb, mod) => function __require() {\n  return mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = { exports: {} }).exports, mod), mod.exports;\n};\n\nexport {\n  __commonJS\n};\n"], "names": [], "mappings": "AAAA,0HAA0H;AAE1H,kBAAkB,GAClB,wDAAwD,GACxD,kBAAkB;;;AAElB,IAAI,oBAAoB,OAAO,mBAAmB;AAClD,IAAI,aAAa,CAAC,IAAI,MAAQ,SAAS;QACrC,OAAO,OAAO,CAAC,GAAG,EAAE,CAAC,kBAAkB,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,MAAM;YAAE,SAAS,CAAC;QAAE,CAAC,EAAE,OAAO,EAAE,MAAM,IAAI,OAAO;IACpG", "debugId": null}}, {"offset": {"line": 5255, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/cms/.basehub/react-pump/index.js"], "sourcesContent": ["// This file was generated by basehub. Do not edit directly. Read more: https://basehub.com/docs/api-reference/basehub-sdk\n\n/* eslint-disable */\n/* eslint-disable eslint-comments/no-restricted-disable */\n/* tslint:disable */\n\nimport \"./chunk-F5PHAOMO.js\";\n\n// ../../node_modules/.pnpm/basehub@8.2.7_@babel+runtim_3aebfa8e064bb601162c04844d38dd02/node_modules/basehub/src/react/pump/server-pump.tsx\nimport * as React from \"react\";\nimport {\n  basehub,\n  generateQueryOp,\n  getStuffFromEnv,\n  resolvedRef,\n  isNextjs\n} from \"../index\";\nvar LazyClientPump = React.lazy(\n  () => import(\"./client-pump-WYUPTPKD.js\").then((mod) => ({ default: mod.ClientPump }))\n);\nvar cache = /* @__PURE__ */ new Map();\nvar pumpToken = null;\nvar spaceID = null;\nvar pusherData = null;\nvar DEDUPE_TIME_MS = 32;\nvar Pump = async ({\n  children,\n  queries,\n  bind,\n  ...basehubProps\n}) => {\n  const errors = [];\n  const responseHashes = [];\n  if (isNextjs) {\n    let isNextjsDraftMode = false;\n    if (basehubProps.draft === void 0) {\n      try {\n        const { draftMode } = await import(\"next/headers\");\n        isNextjsDraftMode = (await draftMode()).isEnabled;\n      } catch (error) {\n      }\n    }\n    if (isNextjsDraftMode && basehubProps.draft === void 0) {\n      basehubProps.draft = true;\n    }\n  }\n  const { headers, draft } = getStuffFromEnv(basehubProps);\n  const token = headers[\"x-basehub-token\"];\n  const apiVersion = headers[\"x-basehub-api-version\"];\n  const pumpEndpoint = \"https://aws.basehub.com/pump\";\n  const noQueries = queries.length === 0;\n  const queriesWithFallback = draft && noQueries ? [{ _sys: { id: true } }] : queries;\n  if (draft) {\n    if (isNextjs) {\n      try {\n        const { cookies } = await import(\"next/headers\");\n        const cookieStore = await cookies();\n        const ref = cookieStore.get(\"bshb-preview-ref-\" + resolvedRef.repoHash)?.value;\n        if (ref) {\n          headers[\"x-basehub-ref\"] = ref;\n        }\n      } catch (error) {\n      }\n    }\n  }\n  const results = await Promise.all(\n    // @ts-ignore\n    queriesWithFallback.map(async (singleQuery, index) => {\n      const rawQueryOp = generateQueryOp(singleQuery);\n      const cacheKey = JSON.stringify({ ...rawQueryOp, ...headers }) + (draft ? \"_draft\" : \"_prod\");\n      let data = void 0;\n      if (cache.has(cacheKey)) {\n        const cached = cache.get(cacheKey);\n        if (performance.now() - cached.start < DEDUPE_TIME_MS) {\n          data = await cached.data;\n        }\n      }\n      if (!data) {\n        const dataPromise = draft ? fetch(pumpEndpoint, {\n          ...isNextjs ? { cache: \"no-store\" } : {},\n          method: \"POST\",\n          headers: {\n            ...headers,\n            \"content-type\": \"application/json\",\n            \"x-basehub-token\": token,\n            \"x-basehub-api-version\": apiVersion\n          },\n          body: JSON.stringify(rawQueryOp)\n        }).then(async (response) => {\n          const {\n            data: data2 = null,\n            newPumpToken,\n            errors: _errors = null,\n            spaceID: _spaceID,\n            pusherData: _pusherData,\n            responseHash: _responseHash\n          } = await response.json();\n          pumpToken = newPumpToken;\n          pusherData = _pusherData;\n          spaceID = _spaceID;\n          errors.push(_errors);\n          responseHashes[index] = _responseHash;\n          return basehub.replaceSystemAliases(data2);\n        }) : basehub(basehubProps).query(singleQuery);\n        cache.set(cacheKey, {\n          start: performance.now(),\n          data: dataPromise\n        });\n        data = await dataPromise;\n      }\n      return { data, rawQueryOp };\n    })\n  );\n  if (bind) {\n    children = children.bind(null, bind);\n  }\n  let resolvedChildren;\n  const childrenPromise = children(results.map((r) => r.data));\n  if (childrenPromise instanceof Promise) {\n    resolvedChildren = await childrenPromise?.catch((e) => {\n      if (draft) {\n        console.error(\"Error in Pump children function\", e);\n        return null;\n      } else\n        throw e;\n    });\n  } else {\n    resolvedChildren = childrenPromise;\n  }\n  if (draft) {\n    if (!pumpToken || !spaceID || !pusherData) {\n      console.log(\"Results (length):\", results?.length);\n      console.log(\"Errors:\", JSON.stringify(errors, null, 2));\n      console.log(\"Pump Endpoint:\", pumpEndpoint);\n      console.log(\"Pump Token:\", pumpToken);\n      console.log(\"Space ID:\", spaceID);\n      console.log(\"Pusher Data:\", pusherData);\n      console.log(\"Response Hashes:\", JSON.stringify(responseHashes, null, 2));\n      throw new Error(\n        \"Pump did not return the necessary data. Look at the logs to see what's missing.\"\n      );\n    }\n    return /* @__PURE__ */ React.createElement(\n      LazyClientPump,\n      {\n        rawQueries: results.map((r) => r.rawQueryOp),\n        initialState: {\n          // @ts-ignore\n          data: !noQueries ? results.map((r) => r.data ?? null) : [],\n          errors,\n          responseHashes,\n          pusherData,\n          spaceID\n        },\n        pumpEndpoint,\n        pumpToken: pumpToken ?? void 0,\n        initialResolvedChildren: resolvedChildren,\n        apiVersion,\n        previewRef: headers[\"x-basehub-ref\"] || resolvedRef.ref\n      },\n      children\n    );\n  }\n  return resolvedChildren;\n};\nvar createPump = (queries) => {\n  return (props) => {\n    const queryResult = typeof queries === \"function\" ? queries(props.params) : queries;\n    return /* @__PURE__ */ React.createElement(Pump, { ...props, queries: queryResult });\n  };\n};\nexport {\n  Pump,\n  createPump\n};\n"], "names": [], "mappings": "AAAA,0HAA0H;AAE1H,kBAAkB,GAClB,wDAAwD,GACxD,kBAAkB;;;;AAElB;AAEA,4IAA4I;AAC5I;AACA;AAAA;;;;AAOA,IAAI,+BAAiB,CAAA,GAAA,oTAAA,CAAA,OAAU,AAAD,EAC5B,IAAM,4JAAoC,IAAI,CAAC,CAAC,MAAQ,CAAC;YAAE,SAAS,IAAI,UAAU;QAAC,CAAC;AAEtF,IAAI,QAAQ,aAAa,GAAG,IAAI;AAChC,IAAI,YAAY;AAChB,IAAI,UAAU;AACd,IAAI,aAAa;AACjB,IAAI,iBAAiB;AACrB,IAAI,OAAO,OAAO,EAChB,QAAQ,EACR,OAAO,EACP,IAAI,EACJ,GAAG,cACJ;IACC,MAAM,SAAS,EAAE;IACjB,MAAM,iBAAiB,EAAE;IACzB,IAAI,qJAAA,CAAA,WAAQ,EAAE;QACZ,IAAI,oBAAoB;QACxB,IAAI,aAAa,KAAK,KAAK,KAAK,GAAG;YACjC,IAAI;gBACF,MAAM,EAAE,SAAS,EAAE,GAAG;gBACtB,oBAAoB,CAAC,MAAM,WAAW,EAAE,SAAS;YACnD,EAAE,OAAO,OAAO,CAChB;QACF;QACA,IAAI,qBAAqB,aAAa,KAAK,KAAK,KAAK,GAAG;YACtD,aAAa,KAAK,GAAG;QACvB;IACF;IACA,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,qJAAA,CAAA,kBAAe,AAAD,EAAE;IAC3C,MAAM,QAAQ,OAAO,CAAC,kBAAkB;IACxC,MAAM,aAAa,OAAO,CAAC,wBAAwB;IACnD,MAAM,eAAe;IACrB,MAAM,YAAY,QAAQ,MAAM,KAAK;IACrC,MAAM,sBAAsB,SAAS,YAAY;QAAC;YAAE,MAAM;gBAAE,IAAI;YAAK;QAAE;KAAE,GAAG;IAC5E,IAAI,OAAO;QACT,IAAI,qJAAA,CAAA,WAAQ,EAAE;YACZ,IAAI;gBACF,MAAM,EAAE,OAAO,EAAE,GAAG;gBACpB,MAAM,cAAc,MAAM;gBAC1B,MAAM,MAAM,YAAY,GAAG,CAAC,sBAAsB,qJAAA,CAAA,cAAW,CAAC,QAAQ,GAAG;gBACzE,IAAI,KAAK;oBACP,OAAO,CAAC,gBAAgB,GAAG;gBAC7B;YACF,EAAE,OAAO,OAAO,CAChB;QACF;IACF;IACA,MAAM,UAAU,MAAM,QAAQ,GAAG,CAC/B,aAAa;IACb,oBAAoB,GAAG,CAAC,OAAO,aAAa;QAC1C,MAAM,aAAa,CAAA,GAAA,qJAAA,CAAA,kBAAe,AAAD,EAAE;QACnC,MAAM,WAAW,KAAK,SAAS,CAAC;YAAE,GAAG,UAAU;YAAE,GAAG,OAAO;QAAC,KAAK,CAAC,QAAQ,WAAW,OAAO;QAC5F,IAAI,OAAO,KAAK;QAChB,IAAI,MAAM,GAAG,CAAC,WAAW;YACvB,MAAM,SAAS,MAAM,GAAG,CAAC;YACzB,IAAI,YAAY,GAAG,KAAK,OAAO,KAAK,GAAG,gBAAgB;gBACrD,OAAO,MAAM,OAAO,IAAI;YAC1B;QACF;QACA,IAAI,CAAC,MAAM;YACT,MAAM,cAAc,QAAQ,MAAM,cAAc;gBAC9C,GAAG,qJAAA,CAAA,WAAQ,GAAG;oBAAE,OAAO;gBAAW,IAAI,CAAC,CAAC;gBACxC,QAAQ;gBACR,SAAS;oBACP,GAAG,OAAO;oBACV,gBAAgB;oBAChB,mBAAmB;oBACnB,yBAAyB;gBAC3B;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB,GAAG,IAAI,CAAC,OAAO;gBACb,MAAM,EACJ,MAAM,QAAQ,IAAI,EAClB,YAAY,EACZ,QAAQ,UAAU,IAAI,EACtB,SAAS,QAAQ,EACjB,YAAY,WAAW,EACvB,cAAc,aAAa,EAC5B,GAAG,MAAM,SAAS,IAAI;gBACvB,YAAY;gBACZ,aAAa;gBACb,UAAU;gBACV,OAAO,IAAI,CAAC;gBACZ,cAAc,CAAC,MAAM,GAAG;gBACxB,OAAO,qJAAA,CAAA,UAAO,CAAC,oBAAoB,CAAC;YACtC,KAAK,CAAA,GAAA,qJAAA,CAAA,UAAO,AAAD,EAAE,cAAc,KAAK,CAAC;YACjC,MAAM,GAAG,CAAC,UAAU;gBAClB,OAAO,YAAY,GAAG;gBACtB,MAAM;YACR;YACA,OAAO,MAAM;QACf;QACA,OAAO;YAAE;YAAM;QAAW;IAC5B;IAEF,IAAI,MAAM;QACR,WAAW,SAAS,IAAI,CAAC,MAAM;IACjC;IACA,IAAI;IACJ,MAAM,kBAAkB,SAAS,QAAQ,GAAG,CAAC,CAAC,IAAM,EAAE,IAAI;IAC1D,IAAI,2BAA2B,SAAS;QACtC,mBAAmB,MAAM,iBAAiB,MAAM,CAAC;YAC/C,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,mCAAmC;gBACjD,OAAO;YACT,OACE,MAAM;QACV;IACF,OAAO;QACL,mBAAmB;IACrB;IACA,IAAI,OAAO;QACT,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,YAAY;YACzC,QAAQ,GAAG,CAAC,qBAAqB,SAAS;YAC1C,QAAQ,GAAG,CAAC,WAAW,KAAK,SAAS,CAAC,QAAQ,MAAM;YACpD,QAAQ,GAAG,CAAC,kBAAkB;YAC9B,QAAQ,GAAG,CAAC,eAAe;YAC3B,QAAQ,GAAG,CAAC,aAAa;YACzB,QAAQ,GAAG,CAAC,gBAAgB;YAC5B,QAAQ,GAAG,CAAC,oBAAoB,KAAK,SAAS,CAAC,gBAAgB,MAAM;YACrE,MAAM,IAAI,MACR;QAEJ;QACA,OAAO,aAAa,iBAAG,CAAA,GAAA,oTAAA,CAAA,gBAAmB,AAAD,EACvC,gBACA;YACE,YAAY,QAAQ,GAAG,CAAC,CAAC,IAAM,EAAE,UAAU;YAC3C,cAAc;gBACZ,aAAa;gBACb,MAAM,CAAC,YAAY,QAAQ,GAAG,CAAC,CAAC,IAAM,EAAE,IAAI,IAAI,QAAQ,EAAE;gBAC1D;gBACA;gBACA;gBACA;YACF;YACA;YACA,WAAW,aAAa,KAAK;YAC7B,yBAAyB;YACzB;YACA,YAAY,OAAO,CAAC,gBAAgB,IAAI,qJAAA,CAAA,cAAW,CAAC,GAAG;QACzD,GACA;IAEJ;IACA,OAAO;AACT;AACA,IAAI,aAAa,CAAC;IAChB,OAAO,CAAC;QACN,MAAM,cAAc,OAAO,YAAY,aAAa,QAAQ,MAAM,MAAM,IAAI;QAC5E,OAAO,aAAa,iBAAG,CAAA,GAAA,oTAAA,CAAA,gBAAmB,AAAD,EAAE,MAAM;YAAE,GAAG,KAAK;YAAE,SAAS;QAAY;IACpF;AACF", "debugId": null}}, {"offset": {"line": 5423, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/cms/components/feed.tsx"], "sourcesContent": ["export { Pump as Feed } from '../.basehub/react-pump';\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 5451, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/observability/status/index.tsx"], "sourcesContent": ["import 'server-only';\nimport { keys } from '../keys';\nimport type { BetterStackResponse } from './types';\n\nconst apiKey = keys().BETTERSTACK_API_KEY;\nconst url = keys().BETTERSTACK_URL;\n\nexport const Status = async () => {\n  if (!apiKey || !url) {\n    return null;\n  }\n\n  let statusColor = 'bg-muted-foreground';\n  let statusLabel = 'Unable to fetch status';\n\n  try {\n    const response = await fetch(\n      'https://uptime.betterstack.com/api/v2/monitors',\n      {\n        headers: {\n          Authorization: `Bearer ${apiKey}`,\n        },\n      }\n    );\n\n    if (!response.ok) {\n      throw new Error('Failed to fetch status');\n    }\n\n    const { data } = (await response.json()) as BetterStackResponse;\n\n    const status =\n      data.filter((monitor) => monitor.attributes.status === 'up').length /\n      data.length;\n\n    if (status === 0) {\n      statusColor = 'bg-destructive';\n      statusLabel = 'Degraded performance';\n    } else if (status < 1) {\n      statusColor = 'bg-warning';\n      statusLabel = 'Partial outage';\n    } else {\n      statusColor = 'bg-success';\n      statusLabel = 'All systems normal';\n    }\n  } catch {\n    statusColor = 'bg-muted-foreground';\n    statusLabel = 'Unable to fetch status';\n  }\n\n  return (\n    <a\n      className=\"flex items-center gap-3 font-medium text-sm\"\n      target=\"_blank\"\n      rel=\"noreferrer\"\n      href={url}\n    >\n      <span className=\"relative flex h-2 w-2\">\n        <span\n          className={`absolute inline-flex h-full w-full animate-ping rounded-full opacity-75 ${statusColor}`}\n        />\n        <span\n          className={`relative inline-flex h-2 w-2 rounded-full ${statusColor}`}\n        />\n      </span>\n      <span className=\"text-muted-foreground\">{statusLabel}</span>\n    </a>\n  );\n};\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAGA,MAAM,SAAS,CAAA,GAAA,iIAAA,CAAA,OAAI,AAAD,IAAI,mBAAmB;AACzC,MAAM,MAAM,CAAA,GAAA,iIAAA,CAAA,OAAI,AAAD,IAAI,eAAe;AAE3B,MAAM,SAAS;IACpB,IAAI,CAAC,UAAU,CAAC,KAAK;QACnB,OAAO;IACT;IAEA,IAAI,cAAc;IAClB,IAAI,cAAc;IAElB,IAAI;QACF,MAAM,WAAW,MAAM,MACrB,kDACA;YACE,SAAS;gBACP,eAAe,CAAC,OAAO,EAAE,QAAQ;YACnC;QACF;QAGF,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,EAAE,IAAI,EAAE,GAAI,MAAM,SAAS,IAAI;QAErC,MAAM,SACJ,KAAK,MAAM,CAAC,CAAC,UAAY,QAAQ,UAAU,CAAC,MAAM,KAAK,MAAM,MAAM,GACnE,KAAK,MAAM;QAEb,IAAI,WAAW,GAAG;YAChB,cAAc;YACd,cAAc;QAChB,OAAO,IAAI,SAAS,GAAG;YACrB,cAAc;YACd,cAAc;QAChB,OAAO;YACL,cAAc;YACd,cAAc;QAChB;IACF,EAAE,OAAM;QACN,cAAc;QACd,cAAc;IAChB;IAEA,qBACE,6VAAC;QACC,WAAU;QACV,QAAO;QACP,KAAI;QACJ,MAAM;;0BAEN,6VAAC;gBAAK,WAAU;;kCACd,6VAAC;wBACC,WAAW,CAAC,wEAAwE,EAAE,aAAa;;;;;;kCAErG,6VAAC;wBACC,WAAW,CAAC,0CAA0C,EAAE,aAAa;;;;;;;;;;;;0BAGzE,6VAAC;gBAAK,WAAU;0BAAyB;;;;;;;;;;;;AAG/C", "debugId": null}}, {"offset": {"line": 5543, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/components/footer.tsx"], "sourcesContent": ["import { env } from '@/env';\nimport { legal } from '@repo/cms';\nimport { Feed } from '@repo/cms/components/feed';\nimport { Status } from '@repo/observability/status';\nimport Link from 'next/link';\n\nexport const Footer = () => (\n  <Feed queries={[legal.postsQuery]}>\n    {async ([data]: [any]) => {\n      'use server';\n\n      const navigationItems = [\n        {\n          title: 'Product',\n          items: [\n            { title: 'Home', href: '/' },\n            { title: 'Pricing', href: '/pricing' },\n            { title: 'Features', href: '/features' },\n            { title: 'Enterprise', href: '/enterprise' },\n            { title: 'Downloads', href: '/downloads' },\n            { title: 'Students', href: '/students' },\n          ],\n        },\n        {\n          title: 'Resources',\n          items: [\n            { title: 'Docs', href: env.NEXT_PUBLIC_DOCS_URL || '/docs' },\n            { title: 'Blog', href: '/blog' },\n            { title: 'Forum', href: '/forum' },\n            { title: 'Changelog', href: '/changelog' },\n          ],\n        },\n        {\n          title: 'Company',\n          items: [\n            { title: 'Anysphere', href: '/company' },\n            { title: 'Careers', href: '/careers' },\n            { title: 'Community', href: '/community' },\n            { title: 'Customers', href: '/customers' },\n          ],\n        },\n        {\n          title: 'Legal',\n          items: data.legalPages.items.map((post: any) => ({\n            title: post._title,\n            href: `/legal/${post._slug}`,\n          })),\n        },\n      ];\n\n      return (\n        <footer className=\"relative bg-background border-t border-border overflow-hidden\">\n          {/* Subtle orange gradient background - starting from bottom */}\n          <div className=\"absolute inset-0 bg-gradient-to-tr from-orange-500/5 via-background to-orange-600/3 pointer-events-none\"></div>\n\n          {/* Huge transparent \"Cubent\" background text - slightly smaller */}\n          <div className=\"absolute inset-0 flex items-center justify-center pointer-events-none select-none\">\n            <div\n              className=\"text-[16rem] lg:text-[20rem] xl:text-[24rem] font-bold tracking-tighter opacity-[0.02] whitespace-nowrap\"\n              style={{\n                fontFamily: 'var(--font-geist-sans)',\n                transform: 'translateY(-10%)',\n              }}\n            >\n              Cubent\n            </div>\n          </div>\n\n          <div className=\"relative z-10 mx-auto max-w-7xl px-6 py-12 lg:px-8 lg:py-16\">\n            {/* Contact section on top */}\n            <div className=\"flex flex-col items-center gap-4 mb-12\">\n              <Link\n                href=\"mailto:<EMAIL>\"\n                className=\"text-foreground hover:text-muted-foreground transition-colors text-lg font-medium\"\n              >\n                <EMAIL>\n              </Link>\n\n              {/* Social icons - orange styling */}\n              <div className=\"flex gap-4\">\n                <Link\n                  href=\"https://x.com/cubent\"\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"text-orange-500 hover:text-orange-400 transition-colors\"\n                >\n                  <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path d=\"M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z\"/>\n                  </svg>\n                </Link>\n                <Link\n                  href=\"https://github.com/cubent\"\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"text-orange-500 hover:text-orange-400 transition-colors\"\n                >\n                  <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path d=\"M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z\"/>\n                  </svg>\n                </Link>\n                <Link\n                  href=\"https://discord.gg/cubent\"\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"text-orange-500 hover:text-orange-400 transition-colors\"\n                >\n                  <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path d=\"M20.317 4.3698a19.7913 19.7913 0 00-4.8851-1.5152.0741.0741 0 00-.0785.0371c-.211.3753-.4447.8648-.6083 1.2495-1.8447-.2762-3.68-.2762-5.4868 0-.1636-.3933-.4058-.8742-.6177-1.2495a.077.077 0 00-.0785-.037 19.7363 19.7363 0 00-4.8852 1.515.0699.0699 0 00-.0321.0277C.5334 9.0458-.319 13.5799.0992 18.0578a.0824.0824 0 00.0312.0561c2.0528 1.5076 4.0413 2.4228 5.9929 3.0294a.0777.0777 0 00.0842-.0276c.4616-.6304.8731-1.2952 1.226-1.9942a.076.076 0 00-.0416-.1057c-.6528-.2476-1.2743-.5495-1.8722-.8923a.077.077 0 01-.0076-.1277c.1258-.0943.2517-.1923.3718-.2914a.0743.0743 0 01.0776-.0105c3.9278 1.7933 8.18 1.7933 12.0614 0a.0739.0739 0 01.0785.0095c.1202.099.246.1981.3728.2924a.077.077 0 01-.0066.1276 12.2986 12.2986 0 01-1.873.8914.0766.0766 0 00-.0407.1067c.3604.698.7719 1.3628 1.225 1.9932a.076.076 0 00.0842.0286c1.961-.6067 3.9495-1.5219 6.0023-3.0294a.077.077 0 00.0313-.0552c.5004-5.177-.8382-9.6739-3.5485-13.6604a.061.061 0 00-.0312-.0286zM8.02 15.3312c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9555-2.4189 2.157-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419-.0189 1.3332-.9555 2.4189-2.1569 2.4189zm7.9748 0c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9554-2.4189 2.1569-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.9555 2.4189-2.1568 2.4189Z\"/>\n                  </svg>\n                </Link>\n                <Link\n                  href=\"https://youtube.com/@cubent\"\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"text-orange-500 hover:text-orange-400 transition-colors\"\n                >\n                  <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path d=\"M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z\"/>\n                  </svg>\n                </Link>\n              </div>\n            </div>\n\n            {/* Centered Navigation columns */}\n            <div className=\"flex justify-center mb-12\">\n              <div className=\"grid grid-cols-2 gap-8 sm:grid-cols-4 max-w-4xl\">\n                {navigationItems.map((section) => (\n                  <div key={section.title} className=\"flex flex-col gap-4\">\n                    <h3 className=\"text-sm font-medium text-foreground\">\n                      {section.title}\n                    </h3>\n                    <ul className=\"flex flex-col gap-3\">\n                      {section.items?.map((item: any) => (\n                        <li key={item.title}>\n                          <Link\n                            href={item.href}\n                            className=\"text-sm text-muted-foreground hover:text-foreground transition-colors\"\n                            target={\n                              item.href.includes('http') ? '_blank' : undefined\n                            }\n                            rel={\n                              item.href.includes('http')\n                                ? 'noopener noreferrer'\n                                : undefined\n                            }\n                          >\n                            {item.title}\n                          </Link>\n                        </li>\n                      ))}\n                    </ul>\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            {/* Status and Copyright */}\n            <div className=\"flex flex-col items-center gap-6\">\n              <Status />\n              <p className=\"text-sm text-muted-foreground\">\n                © {new Date().getFullYear()} Made by Logicent Ltd\n              </p>\n            </div>\n          </div>\n        </footer>\n      );\n    }}\n  </Feed>\n);\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AAAA;AACA;AACA;;;;;;;;;MAIK,gDAAO,CAAC,KAAY;IAGnB,MAAM,kBAAkB;QACtB;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,OAAO;oBAAQ,MAAM;gBAAI;gBAC3B;oBAAE,OAAO;oBAAW,MAAM;gBAAW;gBACrC;oBAAE,OAAO;oBAAY,MAAM;gBAAY;gBACvC;oBAAE,OAAO;oBAAc,MAAM;gBAAc;gBAC3C;oBAAE,OAAO;oBAAa,MAAM;gBAAa;gBACzC;oBAAE,OAAO;oBAAY,MAAM;gBAAY;aACxC;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,OAAO;oBAAQ,MAAM,kHAAA,CAAA,MAAG,CAAC,oBAAoB,IAAI;gBAAQ;gBAC3D;oBAAE,OAAO;oBAAQ,MAAM;gBAAQ;gBAC/B;oBAAE,OAAO;oBAAS,MAAM;gBAAS;gBACjC;oBAAE,OAAO;oBAAa,MAAM;gBAAa;aAC1C;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,OAAO;oBAAa,MAAM;gBAAW;gBACvC;oBAAE,OAAO;oBAAW,MAAM;gBAAW;gBACrC;oBAAE,OAAO;oBAAa,MAAM;gBAAa;gBACzC;oBAAE,OAAO;oBAAa,MAAM;gBAAa;aAC1C;QACH;QACA;YACE,OAAO;YACP,OAAO,KAAK,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,OAAc,CAAC;oBAC/C,OAAO,KAAK,MAAM;oBAClB,MAAM,CAAC,OAAO,EAAE,KAAK,KAAK,EAAE;gBAC9B,CAAC;QACH;KACD;IAED,qBACE,6VAAC;QAAO,WAAU;;0BAEhB,6VAAC;gBAAI,WAAU;;;;;;0BAGf,6VAAC;gBAAI,WAAU;0BACb,cAAA,6VAAC;oBACC,WAAU;oBACV,OAAO;wBACL,YAAY;wBACZ,WAAW;oBACb;8BACD;;;;;;;;;;;0BAKH,6VAAC;gBAAI,WAAU;;kCAEb,6VAAC;wBAAI,WAAU;;0CACb,6VAAC,2QAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAKD,6VAAC;gCAAI,WAAU;;kDACb,6VAAC,2QAAA,CAAA,UAAI;wCACH,MAAK;wCACL,QAAO;wCACP,KAAI;wCACJ,WAAU;kDAEV,cAAA,6VAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAe,SAAQ;sDACnD,cAAA,6VAAC;gDAAK,GAAE;;;;;;;;;;;;;;;;kDAGZ,6VAAC,2QAAA,CAAA,UAAI;wCACH,MAAK;wCACL,QAAO;wCACP,KAAI;wCACJ,WAAU;kDAEV,cAAA,6VAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAe,SAAQ;sDACnD,cAAA,6VAAC;gDAAK,GAAE;;;;;;;;;;;;;;;;kDAGZ,6VAAC,2QAAA,CAAA,UAAI;wCACH,MAAK;wCACL,QAAO;wCACP,KAAI;wCACJ,WAAU;kDAEV,cAAA,6VAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAe,SAAQ;sDACnD,cAAA,6VAAC;gDAAK,GAAE;;;;;;;;;;;;;;;;kDAGZ,6VAAC,2QAAA,CAAA,UAAI;wCACH,MAAK;wCACL,QAAO;wCACP,KAAI;wCACJ,WAAU;kDAEV,cAAA,6VAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAe,SAAQ;sDACnD,cAAA,6VAAC;gDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOhB,6VAAC;wBAAI,WAAU;kCACb,cAAA,6VAAC;4BAAI,WAAU;sCACZ,gBAAgB,GAAG,CAAC,CAAC,wBACpB,6VAAC;oCAAwB,WAAU;;sDACjC,6VAAC;4CAAG,WAAU;sDACX,QAAQ,KAAK;;;;;;sDAEhB,6VAAC;4CAAG,WAAU;sDACX,QAAQ,KAAK,EAAE,IAAI,CAAC,qBACnB,6VAAC;8DACC,cAAA,6VAAC,2QAAA,CAAA,UAAI;wDACH,MAAM,KAAK,IAAI;wDACf,WAAU;wDACV,QACE,KAAK,IAAI,CAAC,QAAQ,CAAC,UAAU,WAAW;wDAE1C,KACE,KAAK,IAAI,CAAC,QAAQ,CAAC,UACf,wBACA;kEAGL,KAAK,KAAK;;;;;;mDAbN,KAAK,KAAK;;;;;;;;;;;mCANf,QAAQ,KAAK;;;;;;;;;;;;;;;kCA8B7B,6VAAC;wBAAI,WAAU;;0CACb,6VAAC,6IAAA,CAAA,SAAM;;;;;0CACP,6VAAC;gCAAE,WAAU;;oCAAgC;oCACxC,IAAI,OAAO,WAAW;oCAAG;;;;;;;;;;;;;;;;;;;;;;;;;AAMxC;AAhKG,MAAM,SAAS,kBACpB,6VAAC,sLAAA,CAAA,OAAI;QAAC,SAAS;YAAC,wHAAA,CAAA,QAAK,CAAC,UAAU;SAAC;kBAC9B,8VAAA", "debugId": null}}, {"offset": {"line": 5918, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/components/header/index.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Header = registerClientReference(\n    function() { throw new Error(\"Attempted to call Head<PERSON>() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/app/[locale]/components/header/index.tsx <module evaluation>\",\n    \"Header\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,mFACA", "debugId": null}}, {"offset": {"line": 5932, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/components/header/index.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Header = registerClientReference(\n    function() { throw new Error(\"Attempted to call Head<PERSON>() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/app/[locale]/components/header/index.tsx\",\n    \"Header\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,+DACA", "debugId": null}}, {"offset": {"line": 5946, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 5956, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/layout.tsx"], "sourcesContent": ["// Main layout component for the Cubent website\nimport './styles.css';\nimport { Toolbar as CMSToolbar } from '@repo/cms/components/toolbar';\nimport { DesignSystemProvider } from '@repo/design-system';\nimport { fonts } from '@repo/design-system/lib/fonts';\nimport { cn } from '@repo/design-system/lib/utils';\nimport { Toolbar } from '@repo/feature-flags/components/toolbar';\nimport { getDictionary } from '@repo/internationalization';\nimport type { ReactNode } from 'react';\nimport { Footer } from './components/footer';\nimport { Header } from './components/header';\n\ntype RootLayoutProperties = {\n  readonly children: ReactNode;\n  readonly params: Promise<{\n    locale: string;\n  }>;\n};\n\nconst RootLayout = async ({ children, params }: RootLayoutProperties) => {\n  const { locale } = await params;\n  const dictionary = await getDictionary(locale);\n\n  return (\n    <html\n      lang={locale}\n      className={cn(fonts, 'scroll-smooth')}\n      suppressHydrationWarning\n    >\n      <body>\n        <DesignSystemProvider>\n          <Header dictionary={dictionary} />\n          {children}\n          <Footer />\n        </DesignSystemProvider>\n        <Toolbar />\n        <CMSToolbar />\n      </body>\n    </html>\n  );\n};\n\nexport default RootLayout;\n"], "names": [], "mappings": "AAAA,+CAA+C;;;;;AAE/C;AAAA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;;;;;;;;;;;AASA,MAAM,aAAa,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAwB;IAClE,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IACzB,MAAM,aAAa,MAAM,CAAA,GAAA,yIAAA,CAAA,gBAAa,AAAD,EAAE;IAEvC,qBACE,6VAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,4IAAA,CAAA,QAAK,EAAE;QACrB,wBAAwB;kBAExB,cAAA,6VAAC;;8BACC,6VAAC,sIAAA,CAAA,uBAAoB;;sCACnB,6VAAC,kKAAA,CAAA,SAAM;4BAAC,YAAY;;;;;;wBACnB;sCACD,6VAAC,yJAAA,CAAA,SAAM;;;;;;;;;;;8BAET,6VAAC,sJAAA,CAAA,UAAO;;;;;8BACR,6VAAC,wJAAA,CAAA,UAAU;;;;;;;;;;;;;;;;AAInB;uCAEe", "debugId": null}}]}